#!/usr/bin/env python3
"""
Migration script to add ShortUrl table to the database.
Run this script to update your database schema.
"""

from app import create_app
from models import db, ShortUrl

def migrate_database():
    """Create the ShortUrl table."""
    app = create_app()
    
    with app.app_context():
        try:
            # Create the ShortUrl table
            db.create_all()
            print("✅ ShortUrl table created successfully!")
            
            # Verify the table was created
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            if 'short_url' in tables:
                print("✅ ShortUrl table verified in database")
                
                # Show table structure
                columns = inspector.get_columns('short_url')
                print("\n📋 ShortUrl table structure:")
                for column in columns:
                    print(f"  - {column['name']}: {column['type']}")
            else:
                print("❌ ShortUrl table not found in database")
                
        except Exception as e:
            print(f"❌ Error creating ShortUrl table: {e}")
            return False
            
    return True

if __name__ == '__main__':
    print("🚀 Starting database migration for ShortUrl table...")
    success = migrate_database()
    
    if success:
        print("\n✅ Migration completed successfully!")
        print("You can now use short URLs for your shops.")
    else:
        print("\n❌ Migration failed. Please check the error messages above.")
