#!/usr/bin/env python3
"""
Migration script to convert existing random short codes to sequential numeric codes.
This will update all existing short URLs to use numbers starting from 1.
"""

from app import create_app
from models import db, ShortUrl

def migrate_to_numeric_codes():
    """Convert existing short codes to sequential numeric codes."""
    app = create_app()
    
    with app.app_context():
        try:
            # Get all existing short URLs ordered by creation date
            existing_urls = ShortUrl.query.order_by(ShortUrl.created_at.asc()).all()
            
            if not existing_urls:
                print("✅ No existing short URLs found. Ready for new numeric codes!")
                return True
            
            print(f"🔄 Found {len(existing_urls)} existing short URLs to convert...")
            
            # Convert each URL to a sequential number
            for index, short_url in enumerate(existing_urls, start=1):
                old_code = short_url.short_code
                new_code = str(index)
                
                # Update the short code
                short_url.short_code = new_code
                
                print(f"  {index}. {old_code} → {new_code} (Shop ID: {short_url.shop_id})")
            
            # Commit all changes
            db.session.commit()
            
            print(f"\n✅ Successfully converted {len(existing_urls)} short URLs to numeric codes!")
            print("📋 Summary:")
            
            # Show the updated URLs
            updated_urls = ShortUrl.query.order_by(ShortUrl.short_code.asc()).all()
            for url in updated_urls:
                print(f"  - /s/{url.short_code} → Shop ID {url.shop_id}")
                
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error converting short codes: {e}")
            return False
            
    return True

if __name__ == '__main__':
    print("🚀 Starting migration to numeric short codes...")
    success = migrate_to_numeric_codes()
    
    if success:
        print("\n✅ Migration completed successfully!")
        print("🎉 All short URLs now use sequential numbers starting from 1!")
        print("📱 Examples: /s/1, /s/2, /s/3, etc.")
    else:
        print("\n❌ Migration failed. Please check the error messages above.")
