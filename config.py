import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///afroly.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Upload settings
    UPLOAD_FOLDER = 'static/uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

    # Pagination
    PRODUCTS_PER_PAGE = 12
    SHOPS_PER_PAGE = 9
    ORDERS_PER_PAGE = 10

    # Commission rates by tier
    COMMISSION_RATES = {
        'free': 0.15,      # 15% for free tier
        'premium': 0.10,   # 10% for premium tier
        'gold': 0.05       # 5% for gold tier
    }

    # Product limits by tier (default values - can be overridden in admin settings)
    PRODUCT_LIMITS = {
        'free': 10,
        'premium': 30,
        'gold': 75
    }

    # Shop limits by tier (updated per user preferences)
    SHOP_LIMITS = {
        'free': 1,
        'premium': 2,
        'gold': 5
    }

    # Payment settings
    STRIPE_PUBLISHABLE_KEY = os.environ.get('STRIPE_PUBLISHABLE_KEY')
    STRIPE_SECRET_KEY = os.environ.get('STRIPE_SECRET_KEY')
    PAYPAL_CLIENT_ID = os.environ.get('PAYPAL_CLIENT_ID')
    PAYPAL_CLIENT_SECRET = os.environ.get('PAYPAL_CLIENT_SECRET')
    PAYPAL_MODE = os.environ.get('PAYPAL_MODE', 'sandbox')  # sandbox or live

    # Email settings
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USE_SSL = os.environ.get('MAIL_USE_SSL', 'false').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER') or '<EMAIL>'
    MAIL_MAX_EMAILS = int(os.environ.get('MAIL_MAX_EMAILS') or 100)
    MAIL_SUPPRESS_SEND = os.environ.get('MAIL_SUPPRESS_SEND', 'false').lower() in ['true', 'on', '1']

    # Base URL for email links
    BASE_URL = os.environ.get('BASE_URL', 'https://afroly.org')

    # AliExpress API settings for dropshipping
    ALIEXPRESS_API_KEY = '6395a497bamsh3fa52da4312cd82p117579jsn7988a8d074b0'
    ALIEXPRESS_API_HOST = 'aliexpress-true-api.p.rapidapi.com'

    # eBay API settings for dropshipping
    EBAY_API_KEY = '6395a497bamsh3fa52da4312cd82p117579jsn7988a8d074b0'
    EBAY_API_HOST = 'ebay-search-result.p.rapidapi.com'

    # African Countries List
    AFRICAN_COUNTRIES = [
        'Afrique du Sud', 'Algérie', 'Angola', 'Bénin', 'Botswana', 'Burkina Faso',
        'Burundi', 'Cameroun', 'Cap-Vert', 'République Centrafricaine', 'Tchad',
        'Comores', 'République du Congo', 'République Démocratique du Congo',
        'Côte d\'Ivoire', 'Djibouti', 'Égypte', 'Guinée Équatoriale', 'Érythrée',
        'Eswatini', 'Éthiopie', 'Gabon', 'Gambie', 'Ghana', 'Guinée', 'Guinée-Bissau',
        'Kenya', 'Lesotho', 'Libéria', 'Libye', 'Madagascar', 'Malawi', 'Mali',
        'Mauritanie', 'Maurice', 'Maroc', 'Mozambique', 'Namibie', 'Niger',
        'Nigéria', 'Rwanda', 'São Tomé-et-Príncipe', 'Sénégal', 'Seychelles',
        'Sierra Leone', 'Somalie', 'Soudan', 'Soudan du Sud', 'Tanzanie', 'Togo',
        'Tunisie', 'Ouganda', 'Zambie', 'Zimbabwe'
    ]

    # African localization
    DEFAULT_CURRENCY = 'USD'  # Can be changed to local currencies
    SUPPORTED_CURRENCIES = ['USD', 'NGN', 'KES', 'GHS', 'ZAR', 'EGP']

    # Payment Methods Configuration
    PAYMENT_METHODS = {
        'mobile_money': {
            'mtn_money': {
                'name': 'MTN Money',
                'icon': 'fas fa-mobile-alt',
                'color': '#FFCC00',
                'countries': ['Ghana', 'Ouganda', 'Rwanda', 'Côte d\'Ivoire', 'Cameroun', 'Bénin', 'Guinée-Bissau', 'Guinée', 'Libéria', 'Congo-Brazzaville', 'Zambie', 'Eswatini', 'Afrique du Sud', 'Sénégal'],
                'fields': ['phone_number', 'account_name'],
                'tier_required': 'free',
                'description': 'Service de paiement mobile MTN disponible dans 13+ pays africains'
            },
            'orange_money': {
                'name': 'Orange Money',
                'icon': 'fas fa-mobile-alt',
                'color': '#FF6600',
                'countries': ['Sénégal', 'Mali', 'Burkina Faso', 'Niger', 'Côte d\'Ivoire', 'Cameroun'],
                'fields': ['phone_number', 'account_name'],
                'tier_required': 'free'
            },
            'airtel_money': {
                'name': 'Airtel Money',
                'icon': 'fas fa-mobile-alt',
                'color': '#FF0000',
                'countries': ['Kenya', 'Tanzanie', 'Ouganda', 'Rwanda', 'Zambie', 'Madagascar', 'Malawi', 'Niger', 'Tchad', 'Gabon', 'République démocratique du Congo', 'Sénégal'],
                'fields': ['phone_number', 'account_name'],
                'tier_required': 'free',
                'description': 'Airtel Money - Service de paiement mobile Airtel Africa'
            },
            'wave': {
                'name': 'Wave',
                'icon': 'fas fa-wave-square',
                'color': '#00D4AA',
                'countries': ['Sénégal', 'Côte d\'Ivoire', 'Mali', 'Burkina Faso'],
                'fields': ['phone_number', 'account_name'],
                'tier_required': 'free'
            },
            'mtn_momo': {
                'name': 'MTN MoMo',
                'icon': 'fas fa-mobile-alt',
                'color': '#FFCC00',
                'countries': ['Ghana', 'Ouganda', 'Cameroun', 'Rwanda', 'Côte d\'Ivoire', 'Sénégal'],
                'fields': ['phone_number', 'account_name', 'momo_name'],
                'tier_required': 'free',
                'description': 'MTN Mobile Money (MoMo) - Service de paiement mobile MTN'
            },
            'moov_money': {
                'name': 'Moov Money',
                'icon': 'fas fa-mobile-alt',
                'color': '#0066CC',
                'countries': ['Bénin', 'Burkina Faso', 'Côte d\'Ivoire', 'Mali', 'Niger', 'Sénégal', 'Togo'],
                'fields': ['phone_number', 'account_name'],
                'tier_required': 'free',
                'description': 'Moov Money - Service de paiement mobile Moov Africa'
            }
        },
        'bank_transfer': {
            'bank_account': {
                'name': 'Virement Bancaire',
                'icon': 'fas fa-university',
                'color': '#2E86AB',
                'countries': 'all',
                'fields': ['bank_name', 'account_number', 'account_name', 'swift_code'],
                'tier_required': 'free'
            }
        },
        'international': {
            'paypal': {
                'name': 'PayPal',
                'icon': 'fab fa-paypal',
                'color': '#0070BA',
                'countries': 'all',
                'fields': ['paypal_email'],
                'tier_required': 'premium'
            },
            'stripe': {
                'name': 'Stripe',
                'icon': 'fab fa-stripe',
                'color': '#635BFF',
                'countries': 'all',
                'fields': ['stripe_account_id'],
                'tier_required': 'premium'
            }
        },
        'other': {
            'custom': {
                'name': 'Autre Méthode',
                'icon': 'fas fa-credit-card',
                'color': '#6C757D',
                'countries': 'all',
                'fields': ['method_name', 'account_details', 'instructions'],
                'tier_required': 'free'
            }
        }
    }
    DEFAULT_TIMEZONE = 'Africa/Lagos'
