#!/usr/bin/env python3
"""
Migration script to add custom_product_limit column to User table
and create SiteSetting table.
"""

import sqlite3
import os
from datetime import datetime

def run_migration():
    """Run the database migration."""
    # Try different possible database paths
    possible_paths = [
        'instance/afromall.db',
        'afromall.db',
        'afroly.db',
        'instance/database.db'
    ]

    db_path = None
    for path in possible_paths:
        if os.path.exists(path):
            db_path = path
            print(f"Found database at: {path}")
            break

    if not db_path:
        print("Database file not found. Checked paths:")
        for path in possible_paths:
            print(f"  - {path}")
        return False

    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        print("Starting migration...")

        # Check if custom_product_limit column already exists
        cursor.execute("PRAGMA table_info(user)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'custom_product_limit' not in columns:
            print("Adding custom_product_limit column to user table...")
            cursor.execute("""
                ALTER TABLE user
                ADD COLUMN custom_product_limit INTEGER
            """)
            print("✓ Added custom_product_limit column")
        else:
            print("✓ custom_product_limit column already exists")

        # Check if site_setting table exists
        cursor.execute("""
            SELECT name FROM sqlite_master
            WHERE type='table' AND name='site_setting'
        """)

        if not cursor.fetchone():
            print("Creating site_setting table...")
            cursor.execute("""
                CREATE TABLE site_setting (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key VARCHAR(100) NOT NULL UNIQUE,
                    value TEXT NOT NULL,
                    description TEXT,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            """)
            print("✓ Created site_setting table")

            # Insert default settings
            print("Inserting default settings...")
            default_settings = [
                ('free_product_limit', '10', 'Maximum products for free tier users'),
                ('premium_product_limit', '30', 'Maximum products for premium tier users'),
                ('gold_product_limit', '75', 'Maximum products for gold tier users')
            ]

            for key, value, description in default_settings:
                cursor.execute("""
                    INSERT INTO site_setting (key, value, description)
                    VALUES (?, ?, ?)
                """, (key, value, description))

            print("✓ Inserted default settings")
        else:
            print("✓ site_setting table already exists")

        # Commit changes
        conn.commit()
        print("Migration completed successfully!")

        return True

    except Exception as e:
        print(f"Migration failed: {e}")
        if conn:
            conn.rollback()
        return False

    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("Database Migration: Add custom_product_limit and SiteSetting table")
    print("=" * 60)

    success = run_migration()

    if success:
        print("\n✅ Migration completed successfully!")
        print("You can now restart your application.")
    else:
        print("\n❌ Migration failed!")
        print("Please check the error messages above.")
