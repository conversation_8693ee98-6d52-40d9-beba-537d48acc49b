/* AfroMall Custom Styles */

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --african-gold: #FFD700;
    --african-green: #228B22;
    --african-red: #DC143C;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

.text-african-gold {
    color: var(--african-gold) !important;
}

.text-african-green {
    color: var(--african-green) !important;
}

.text-african-red {
    color: var(--african-red) !important;
}

.bg-african-gold {
    background-color: var(--african-gold) !important;
}

.bg-african-green {
    background-color: var(--african-green) !important;
}

.bg-african-red {
    background-color: var(--african-red) !important;
}

/* Navigation */
.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--african-gold) !important;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.product-card .card-img-top {
    border-radius: 10px 10px 0 0;
    transition: transform 0.3s ease;
}

.product-card:hover .card-img-top {
    transform: scale(1.05);
}

.shop-card .card-img-top {
    border-radius: 10px 10px 0 0;
}

.category-card {
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.category-card:hover {
    border-color: var(--primary-color);
    background-color: var(--light-color);
}

/* Buttons */
.btn {
    border-radius: 25px;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, var(--primary-color) 100%);
    transform: translateY(-2px);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Forms */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

/* Rating Stars */
.rating {
    display: inline-flex;
    gap: 2px;
}

.rating i {
    font-size: 0.9rem;
    color: var(--warning-color);
}

/* Price Display */
.price-display {
    font-size: 1.1rem;
    font-weight: 600;
}

.price-original {
    text-decoration: line-through;
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.price-sale {
    color: var(--danger-color);
    font-weight: 700;
}

/* Badges */
.badge {
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Footer */
footer {
    background: linear-gradient(135deg, var(--dark-color) 0%, #212529 100%);
}

footer a {
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: var(--african-gold) !important;
}

.social-links a {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-3px);
}

/* Alerts */
.alert {
    border-radius: 10px;
    border: none;
    font-weight: 500;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive Images */
.img-fluid {
    border-radius: 10px;
}

/* Search Bar */
.search-bar {
    border-radius: 25px;
    border: 2px solid #e9ecef;
    overflow: hidden;
}

.search-bar .form-control {
    border: none;
    border-radius: 0;
}

.search-bar .btn {
    border-radius: 0;
    border: none;
}

/* Product Grid */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

/* Shop Grid */
.shop-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

/* Category Grid */
.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

/* Pagination */
.pagination {
    border-radius: 10px;
}

.page-link {
    border: none;
    color: var(--primary-color);
    font-weight: 500;
    padding: 0.75rem 1rem;
    margin: 0 2px;
    border-radius: 8px;
}

.page-link:hover {
    background-color: var(--primary-color);
    color: white;
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Tables */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table th {
    background-color: var(--light-color);
    font-weight: 600;
    border: none;
}

.table td {
    border: none;
    border-bottom: 1px solid #e9ecef;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2rem;
    }

    .hero-section .lead {
        font-size: 1rem;
    }

    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
    }

    .shop-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .category-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 0.5rem;
    }

    .navbar-brand {
        font-size: 1.2rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-50px); }
    to { opacity: 1; transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(50px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Utility Classes */
.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.box-shadow {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.border-radius-lg {
    border-radius: 15px;
}

.border-radius-xl {
    border-radius: 20px;
}

/* WhatsApp Floating Button */
.whatsapp-float {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.whatsapp-button {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: white;
    padding: 12px 16px;
    border-radius: 50px;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
    transition: all 0.3s ease;
    font-weight: 500;
}

.whatsapp-button:hover {
    background: linear-gradient(135deg, #128c7e 0%, #25d366 100%);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
}

.whatsapp-button i {
    font-size: 20px;
    margin-right: 8px;
}

.whatsapp-text {
    font-size: 14px;
    white-space: nowrap;
}

/* Mobile responsive WhatsApp */
@media (max-width: 768px) {
    .whatsapp-float {
        bottom: 15px;
        right: 15px;
    }

    .whatsapp-button {
        padding: 10px 12px;
    }

    .whatsapp-text {
        display: none;
    }

    .whatsapp-button i {
        margin-right: 0;
        font-size: 24px;
    }
}

/* WhatsApp Animation */
@keyframes whatsappPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.whatsapp-button.pulse {
    animation: whatsappPulse 2s infinite;
}

/* Payment Method Badges */
.payment-method-badge {
    display: inline-flex;
    align-items: center;
    padding: 8px 12px;
    background-color: white;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    color: #495057;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.payment-method-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.payment-method-badge i {
    margin-right: 6px;
    font-size: 1rem;
}

/* Social Platform Items */
.social-platform-item {
    text-align: center;
    transition: all 0.3s ease;
}

.social-platform-item:hover {
    transform: translateY(-3px);
}

.social-logo-container {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px;
    color: white;
    font-size: 20px;
    transition: all 0.3s ease;
}

.social-logo-container.facebook {
    background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
}

.social-logo-container.instagram {
    background: linear-gradient(135deg, #e4405f 0%, #f77737 50%, #fccc63 100%);
}

.social-logo-container.whatsapp {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
}

.social-logo-container.tiktok {
    background: linear-gradient(135deg, #000000 0%, #ff0050 100%);
}

.social-logo-container.telegram {
    background: linear-gradient(135deg, #0088cc 0%, #229ed9 100%);
}

/* Share Buttons */
.share-buttons .dropdown-menu {
    min-width: 200px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.share-buttons .dropdown-item {
    padding: 10px 15px;
    transition: all 0.3s ease;
    border-radius: 5px;
    margin: 2px 5px;
}

.share-buttons .dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.share-section {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.share-section .btn {
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    border: 2px solid rgba(255,255,255,0.3);
}

.share-section .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    border-color: rgba(255,255,255,0.8);
}

.share-section .btn i {
    font-size: 16px;
}

/* Social Media Brand Colors on Hover */
.btn:hover .fab.fa-facebook {
    color: #1877f2 !important;
}

.btn:hover .fab.fa-whatsapp {
    color: #25d366 !important;
}

.btn:hover .fab.fa-twitter {
    color: #1da1f2 !important;
}

.btn:hover .fab.fa-telegram {
    color: #0088cc !important;
}

.btn:hover .fas.fa-copy {
    color: #6c757d !important;
}

/* Product Page Share Button */
.product-info .share-buttons .btn {
    border-radius: 8px;
    padding: 8px 12px;
    width: auto;
    height: auto;
}

/* Shop Page Share Button */
.shop-actions .dropdown-menu {
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Mobile Responsive Share Buttons */
@media (max-width: 768px) {
    .share-section {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .share-section span {
        margin-bottom: 10px;
        font-size: 14px;
    }

    .share-section .d-inline-flex {
        justify-content: center;
    }

    .product-info .d-flex {
        flex-direction: column;
        align-items: flex-start;
    }

    .product-info .share-buttons {
        margin-top: 10px;
        align-self: flex-end;
    }
}
