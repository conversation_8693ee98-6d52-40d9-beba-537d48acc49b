// Afroly.org Main JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Auto-hide alerts after 5 seconds (except permanent ones)
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            // Don't auto-hide alerts that contain tier information or have permanent class
            if (!alert.textContent.includes('Niveau :') &&
                !alert.textContent.includes('Niveau Gratuit') &&
                !alert.textContent.includes('Niveau Premium') &&
                !alert.textContent.includes('Niveau Gold') &&
                !alert.classList.contains('alert-permanent') &&
                !alert.closest('.tier-info')) {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        });
    }, 5000);

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add to cart functionality
    const addToCartForms = document.querySelectorAll('.add-to-cart-form');
    addToCartForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const button = form.querySelector('button[type="submit"]');
            const originalText = button.innerHTML;

            // Show loading state
            button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Adding...';
            button.disabled = true;

            // Re-enable button after 2 seconds (form will submit normally)
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 2000);
        });
    });

    // Quantity input validation
    const quantityInputs = document.querySelectorAll('input[name="quantity"]');
    quantityInputs.forEach(input => {
        input.addEventListener('change', function() {
            const min = parseInt(this.min) || 1;
            const max = parseInt(this.max) || 999;
            let value = parseInt(this.value);

            if (isNaN(value) || value < min) {
                this.value = min;
            } else if (value > max) {
                this.value = max;
                showAlert('Maximum quantity available: ' + max, 'warning');
            }
        });
    });

    // Image preview for file uploads
    const imageInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
    imageInputs.forEach(input => {
        input.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Find or create preview element
                    let preview = input.parentNode.querySelector('.image-preview');
                    if (!preview) {
                        preview = document.createElement('img');
                        preview.className = 'image-preview img-thumbnail mt-2';
                        preview.style.maxWidth = '200px';
                        preview.style.maxHeight = '200px';
                        input.parentNode.appendChild(preview);
                    }
                    preview.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    });

    // Search functionality
    const searchForm = document.querySelector('.search-form');
    if (searchForm) {
        const searchInput = searchForm.querySelector('input[name="q"]');
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length >= 3) {
                searchTimeout = setTimeout(() => {
                    // Implement live search suggestions here
                    console.log('Searching for:', query);
                }, 300);
            }
        });
    }

    // Rating stars interaction
    const ratingStars = document.querySelectorAll('.rating-input .star');
    ratingStars.forEach((star, index) => {
        star.addEventListener('click', function() {
            const rating = index + 1;
            const ratingInput = this.closest('.rating-input').querySelector('input[name="rating"]');
            ratingInput.value = rating;

            // Update visual state
            ratingStars.forEach((s, i) => {
                if (i < rating) {
                    s.classList.remove('far');
                    s.classList.add('fas');
                } else {
                    s.classList.remove('fas');
                    s.classList.add('far');
                }
            });
        });

        star.addEventListener('mouseover', function() {
            const rating = index + 1;
            ratingStars.forEach((s, i) => {
                if (i < rating) {
                    s.style.color = '#ffc107';
                } else {
                    s.style.color = '#dee2e6';
                }
            });
        });
    });

    // Cart update functionality
    const cartUpdateForms = document.querySelectorAll('.cart-update-form');
    cartUpdateForms.forEach(form => {
        const quantityInput = form.querySelector('input[name="quantity"]');
        const updateBtn = form.querySelector('.update-cart-btn');

        quantityInput.addEventListener('change', function() {
            updateBtn.style.display = 'inline-block';
        });
    });

    // Confirm delete actions
    const deleteButtons = document.querySelectorAll('.delete-btn, .btn-danger[data-confirm]');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const message = this.dataset.confirm || 'Are you sure you want to delete this item?';
            if (!confirm(message)) {
                e.preventDefault();
            }
        });
    });

    // Auto-resize textareas
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    });

    // Price formatting
    const priceElements = document.querySelectorAll('.price');
    priceElements.forEach(element => {
        const price = parseFloat(element.textContent);
        if (!isNaN(price)) {
            element.textContent = formatCurrency(price);
        }
    });

    // Lazy loading for images
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));

    // Mobile menu enhancements
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (navbarToggler && navbarCollapse) {
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!navbarCollapse.contains(e.target) && !navbarToggler.contains(e.target)) {
                if (navbarCollapse.classList.contains('show')) {
                    navbarToggler.click();
                }
            }
        });
    }
});

// Utility functions
function showAlert(message, type = 'info') {
    const alertContainer = document.querySelector('.alert-container') || document.body;
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    alertContainer.insertBefore(alert, alertContainer.firstChild);

    // Auto-hide after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

function formatCurrency(amount, currency = 'USD') {
    const formatters = {
        'USD': new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }),
        'NGN': new Intl.NumberFormat('en-NG', { style: 'currency', currency: 'NGN' }),
        'KES': new Intl.NumberFormat('en-KE', { style: 'currency', currency: 'KES' }),
        'GHS': new Intl.NumberFormat('en-GH', { style: 'currency', currency: 'GHS' }),
        'ZAR': new Intl.NumberFormat('en-ZA', { style: 'currency', currency: 'ZAR' }),
        'EGP': new Intl.NumberFormat('ar-EG', { style: 'currency', currency: 'EGP' })
    };

    const formatter = formatters[currency] || formatters['USD'];
    return formatter.format(amount);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// AJAX helper function
function makeRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    };

    const config = { ...defaultOptions, ...options };

    return fetch(url, config)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .catch(error => {
            console.error('Request failed:', error);
            showAlert('An error occurred. Please try again.', 'danger');
            throw error;
        });
}

// WhatsApp Floating Button Functions
function initWhatsAppButton() {
    // Default WhatsApp number (can be overridden by admin)
    const defaultWhatsApp = '+221771234567'; // Default AfroMall support

    // Check if admin has set a custom WhatsApp number
    const adminWhatsApp = localStorage.getItem('adminWhatsApp') || defaultWhatsApp;

    // Set up the WhatsApp link
    const whatsappFloat = document.getElementById('whatsapp-float');
    const whatsappLink = document.getElementById('whatsapp-link');

    if (whatsappFloat && whatsappLink) {
        // Clean the phone number (remove non-digits except +)
        const cleanNumber = adminWhatsApp.replace(/[^\d+]/g, '');
        const whatsappUrl = `https://wa.me/${cleanNumber.replace('+', '')}?text=Bonjour! Je suis intéressé(e) par AfroMall.`;

        whatsappLink.href = whatsappUrl;

        // Show the button after a delay
        setTimeout(() => {
            whatsappFloat.style.display = 'block';
            whatsappFloat.style.opacity = '0';
            whatsappFloat.style.transform = 'translateY(20px)';
            whatsappFloat.style.transition = 'all 0.5s ease';

            setTimeout(() => {
                whatsappFloat.style.opacity = '1';
                whatsappFloat.style.transform = 'translateY(0)';
            }, 100);
        }, 2000);

        // Add pulse animation every 10 seconds
        setInterval(() => {
            const button = whatsappLink.querySelector('.whatsapp-button') || whatsappLink;
            button.classList.add('pulse');
            setTimeout(() => {
                button.classList.remove('pulse');
            }, 2000);
        }, 10000);

        // Track clicks for analytics
        whatsappLink.addEventListener('click', function() {
            // Analytics tracking can be added here
            console.log('WhatsApp button clicked');
        });
    }
}

// Admin function to set WhatsApp number
function setAdminWhatsApp(phoneNumber) {
    if (phoneNumber && phoneNumber.trim()) {
        localStorage.setItem('adminWhatsApp', phoneNumber.trim());
        initWhatsAppButton(); // Reinitialize with new number
        return true;
    }
    return false;
}

// Function to get current admin WhatsApp
function getAdminWhatsApp() {
    return localStorage.getItem('adminWhatsApp') || '+447951658211';
}

// Social Media Sharing Functions
function shareOnFacebook(url, title) {
    const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}&quote=${encodeURIComponent(title)}`;
    window.open(shareUrl, '_blank', 'width=600,height=400');
}

function shareOnWhatsApp(url, title) {
    const text = `${title} - ${url}`;
    const shareUrl = `https://wa.me/?text=${encodeURIComponent(text)}`;
    window.open(shareUrl, '_blank');
}

function shareOnTwitter(url, title) {
    const shareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`;
    window.open(shareUrl, '_blank', 'width=600,height=400');
}

function shareOnTelegram(url, title) {
    const text = `${title} - ${url}`;
    const shareUrl = `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`;
    window.open(shareUrl, '_blank', 'width=600,height=400');
}

function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
        // Use the Clipboard API when available
        navigator.clipboard.writeText(text).then(() => {
            showAlert('Lien copié dans le presse-papiers !', 'success');
        }).catch(() => {
            fallbackCopyToClipboard(text);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyToClipboard(text);
    }
}

function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        showAlert('Lien copié dans le presse-papiers !', 'success');
    } catch (err) {
        showAlert('Impossible de copier le lien. Veuillez le copier manuellement.', 'error');
    }

    document.body.removeChild(textArea);
}

// Web Share API (for mobile devices)
function nativeShare(url, title, text) {
    if (navigator.share) {
        navigator.share({
            title: title,
            text: text,
            url: url
        }).catch(err => {
            console.log('Error sharing:', err);
        });
        return true;
    }
    return false;
}

// Initialize WhatsApp button when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initWhatsAppButton();
});

// Export functions for use in other scripts
window.Afroly = {
    showAlert,
    formatCurrency,
    debounce,
    throttle,
    makeRequest,
    initWhatsAppButton,
    setAdminWhatsApp,
    getAdminWhatsApp,
    shareOnFacebook,
    shareOnWhatsApp,
    shareOnTwitter,
    shareOnTelegram,
    copyToClipboard,
    nativeShare
};
