from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import (db, Shop, Product, Order, Review, ShopStatus, ProductStatus, UserRole,
                   CreditPackage, CreditTransaction, CreditTransactionType, ProductBoost)
from utils.decorators import vendor_required
from utils.helpers import generate_slug, allowed_file, save_uploaded_file, get_short_url_for_shop
from services.statistics_service import StatisticsService
from config import Config
import os

shop_bp = Blueprint('shop', __name__)

@shop_bp.route('/browse')
def browse():
    """Browse all shops."""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    country = request.args.get('country', '', type=str)
    sort_by = request.args.get('sort', 'newest')

    # Build query
    shops_query = Shop.query.filter_by(status=ShopStatus.ACTIVE)

    # Apply search filter
    if search:
        shops_query = shops_query.filter(
            Shop.name.contains(search) |
            Shop.description.contains(search)
        )

    # Apply country filter
    if country:
        shops_query = shops_query.filter_by(country=country)

    # Apply sorting
    if sort_by == 'oldest':
        shops_query = shops_query.order_by(Shop.created_at.asc())
    elif sort_by == 'name':
        shops_query = shops_query.order_by(Shop.name.asc())
    elif sort_by == 'country':
        shops_query = shops_query.order_by(Shop.country.asc(), Shop.name.asc())
    else:  # newest (default)
        shops_query = shops_query.order_by(Shop.created_at.desc())

    shops = shops_query.paginate(
        page=page,
        per_page=Config.SHOPS_PER_PAGE,
        error_out=False
    )

    # Get list of countries with active shops for filter dropdown
    countries_with_shops = db.session.query(Shop.country).filter_by(status=ShopStatus.ACTIVE).distinct().order_by(Shop.country).all()
    countries_list = [country[0] for country in countries_with_shops]

    return render_template('shop/browse.html',
                         shops=shops,
                         search=search,
                         country=country,
                         countries_list=countries_list,
                         sort_by=sort_by)

@shop_bp.route('/<slug>')
def view(slug):
    """View a specific shop."""
    shop = Shop.query.filter_by(slug=slug, status=ShopStatus.ACTIVE).first_or_404()

    page = request.args.get('page', 1, type=int)
    sort_by = request.args.get('sort', 'newest')

    # Get shop products
    products_query = Product.query.filter_by(
        shop_id=shop.id,
        status=ProductStatus.ACTIVE
    )

    # Apply sorting
    if sort_by == 'price_low':
        products_query = products_query.order_by(Product.price.asc())
    elif sort_by == 'price_high':
        products_query = products_query.order_by(Product.price.desc())
    elif sort_by == 'name':
        products_query = products_query.order_by(Product.name.asc())
    else:  # newest (default)
        products_query = products_query.order_by(Product.created_at.desc())

    products = products_query.paginate(
        page=page,
        per_page=Config.PRODUCTS_PER_PAGE,
        error_out=False
    )

    # Get shop reviews
    reviews = Review.query.filter_by(shop_id=shop.id, is_approved=True).order_by(
        Review.created_at.desc()
    ).limit(5).all()

    return render_template('shop/view.html',
                         shop=shop,
                         products=products,
                         reviews=reviews,
                         sort_by=sort_by)

@shop_bp.route('/create', methods=['GET', 'POST'])
@login_required
@vendor_required
def create():
    """Create a new shop."""
    # Check if user can create more shops
    if not current_user.can_create_shop():
        flash('Vous avez atteint le nombre maximum de boutiques pour votre niveau. Veuillez passer à un niveau supérieur pour créer plus de boutiques.', 'error')
        return redirect(url_for('shop.vendor_dashboard'))

    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()
        country = request.form.get('country', '').strip()
        contact_email = request.form.get('contact_email', '').strip()
        contact_phone = request.form.get('contact_phone', '').strip()
        address = request.form.get('address', '').strip()

        # Handle payment methods (for create, we'll create a temporary shop object to check permissions)
        payment_methods = []
        payment_method_keys = request.form.getlist('payment_methods')

        # Create temporary shop object to check payment method availability
        temp_shop = Shop(owner_id=current_user.id, country=country)
        temp_shop.owner = current_user  # Set the owner relationship

        for method_key in payment_method_keys:
            if not temp_shop.can_use_payment_method(method_key):
                continue

            method_data = {'type': method_key, 'details': {}}

            # Get method configuration
            method_config = None
            for category, methods in Config.PAYMENT_METHODS.items():
                if method_key in methods:
                    method_config = methods[method_key]
                    break

            if method_config:
                # Collect field data for this payment method
                for field in method_config.get('fields', []):
                    field_name = f"{method_key}_{field}"
                    field_value = request.form.get(field_name, '').strip()
                    if field_value:
                        method_data['details'][field] = field_value

                # Only add if we have some details
                if method_data['details']:
                    payment_methods.append(method_data)

        # Validation
        errors = []

        if not name:
            errors.append('Shop name is required.')
        elif len(name) < 3:
            errors.append('Shop name must be at least 3 characters long.')

        if not description:
            errors.append('Shop description is required.')
        elif len(description) < 20:
            errors.append('Shop description must be at least 20 characters long.')

        if not country:
            errors.append('Country is required.')
        elif country not in Config.AFRICAN_COUNTRIES:
            errors.append('Please select a valid African country.')

        # Generate unique slug
        slug = generate_slug(name)
        if Shop.query.filter_by(slug=slug).first():
            errors.append('A shop with this name already exists. Please choose a different name.')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('shop/create.html')

        # Handle file uploads
        logo_filename = None
        banner_filename = None

        if 'logo' in request.files:
            logo_file = request.files['logo']
            if logo_file and allowed_file(logo_file.filename):
                logo_filename = save_uploaded_file(logo_file, 'shops')

        if 'banner' in request.files:
            banner_file = request.files['banner']
            if banner_file and allowed_file(banner_file.filename):
                banner_filename = save_uploaded_file(banner_file, 'shops')

        # Create shop
        try:
            shop = Shop(
                owner_id=current_user.id,
                name=name,
                slug=slug,
                description=description,
                country=country,
                logo=logo_filename,
                banner=banner_filename,
                contact_email=contact_email,
                contact_phone=contact_phone,
                address=address,
                status=ShopStatus.PENDING,
                payment_info={'methods': payment_methods} if payment_methods else None
            )

            db.session.add(shop)
            db.session.commit()

            flash('Your shop has been created and is pending approval. You will be notified once it\'s approved.', 'success')
            return redirect(url_for('shop.vendor_dashboard'))

        except Exception as e:
            db.session.rollback()
            flash('An error occurred while creating your shop. Please try again.', 'error')

    # Create temporary shop object to get available payment methods
    temp_shop = Shop(owner_id=current_user.id, country='Sénégal')  # Default country for preview
    temp_shop.owner = current_user

    return render_template('shop/create.html',
                         countries=Config.AFRICAN_COUNTRIES,
                         temp_shop=temp_shop)

@shop_bp.route('/dashboard')
@login_required
@vendor_required
def vendor_dashboard():
    """Vendor dashboard."""
    # Get user's shops
    shops = Shop.query.filter_by(owner_id=current_user.id).order_by(Shop.created_at.desc()).all()

    # Get recent orders across all shops
    recent_orders = db.session.query(Order).join(Shop).filter(
        Shop.owner_id == current_user.id
    ).order_by(Order.created_at.desc()).limit(10).all()

    # Calculate stats
    total_products = db.session.query(Product).join(Shop).filter(
        Shop.owner_id == current_user.id,
        Product.status == ProductStatus.ACTIVE
    ).count()

    pending_products = db.session.query(Product).join(Shop).filter(
        Shop.owner_id == current_user.id,
        Product.status == ProductStatus.PENDING
    ).count()

    total_orders = db.session.query(Order).join(Shop).filter(
        Shop.owner_id == current_user.id
    ).count()

    # Get traffic statistics for each shop
    shop_traffic_stats = {}
    for shop in shops:
        shop_traffic_stats[shop.id] = StatisticsService.get_shop_statistics(shop.id)

    return render_template('shop/dashboard.html',
                         shops=shops,
                         recent_orders=recent_orders,
                         total_products=total_products,
                         pending_products=pending_products,
                         total_orders=total_orders,
                         shop_traffic_stats=shop_traffic_stats,
                         get_short_url_for_shop=get_short_url_for_shop)

@shop_bp.route('/<int:shop_id>/edit', methods=['GET', 'POST'])
@login_required
@vendor_required
def edit(shop_id):
    """Edit shop details."""
    shop = Shop.query.filter_by(id=shop_id, owner_id=current_user.id).first_or_404()

    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()
        country = request.form.get('country', '').strip()
        currency = request.form.get('currency', '').strip()
        contact_email = request.form.get('contact_email', '').strip()
        contact_phone = request.form.get('contact_phone', '').strip()
        address = request.form.get('address', '').strip()

        # Handle social media settings
        social_media = {}

        # WhatsApp - Default enabled with default number
        whatsapp_enabled = request.form.get('whatsapp_enabled') == 'on'
        whatsapp_number = request.form.get('whatsapp_number', '').strip()

        # If no WhatsApp number provided, use default
        if not whatsapp_number:
            whatsapp_number = '+44 7951 658211'  # Default WhatsApp number
            whatsapp_enabled = True  # Auto-enable with default number

        if whatsapp_enabled and whatsapp_number:
            social_media['whatsapp'] = {
                'enabled': True,
                'number': whatsapp_number
            }
        else:
            social_media['whatsapp'] = {'enabled': False, 'number': ''}

        # Telegram
        telegram_enabled = request.form.get('telegram_enabled') == 'on'
        telegram_username = request.form.get('telegram_username', '').strip()
        if telegram_enabled and telegram_username:
            social_media['telegram'] = {
                'enabled': True,
                'username': telegram_username
            }
        else:
            social_media['telegram'] = {'enabled': False, 'username': ''}

        # Facebook
        facebook_enabled = request.form.get('facebook_enabled') == 'on'
        facebook_page = request.form.get('facebook_page', '').strip()
        if facebook_enabled and facebook_page:
            social_media['facebook'] = {
                'enabled': True,
                'page': facebook_page
            }
        else:
            social_media['facebook'] = {'enabled': False, 'page': ''}

        # Validation
        errors = []

        if not name:
            errors.append('Shop name is required.')
        elif len(name) < 3:
            errors.append('Shop name must be at least 3 characters long.')

        if not description:
            errors.append('Shop description is required.')
        elif len(description) < 20:
            errors.append('Shop description must be at least 20 characters long.')

        if not country:
            errors.append('Country is required.')
        elif country not in Config.AFRICAN_COUNTRIES:
            errors.append('Please select a valid African country.')

        # Validate currency
        valid_currencies = [
            'FCFA', 'XOF', 'XAF', 'NGN', 'GHS', 'KES', 'UGX', 'TZS', 'RWF',
            'ETB', 'ZAR', 'MAD', 'TND', 'EGP', 'DZD', 'EUR', 'USD', 'GBP'
        ]
        if not currency:
            errors.append('Currency is required.')
        elif currency not in valid_currencies:
            errors.append('Please select a valid currency.')

        # Check if name changed and generate new slug
        if name != shop.name:
            slug = generate_slug(name)
            existing_shop = Shop.query.filter_by(slug=slug).first()
            if existing_shop and existing_shop.id != shop.id:
                errors.append('A shop with this name already exists. Please choose a different name.')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('shop/edit.html', shop=shop)

        # Handle file uploads
        if 'logo' in request.files:
            logo_file = request.files['logo']
            if logo_file and allowed_file(logo_file.filename):
                # Delete old logo if exists
                if shop.logo:
                    old_logo_path = os.path.join('static/uploads/shops', shop.logo)
                    if os.path.exists(old_logo_path):
                        os.remove(old_logo_path)

                shop.logo = save_uploaded_file(logo_file, 'shops')

        if 'banner' in request.files:
            banner_file = request.files['banner']
            if banner_file and allowed_file(banner_file.filename):
                # Delete old banner if exists
                if shop.banner:
                    old_banner_path = os.path.join('static/uploads/shops', shop.banner)
                    if os.path.exists(old_banner_path):
                        os.remove(old_banner_path)

                shop.banner = save_uploaded_file(banner_file, 'shops')

        # Update shop
        try:
            shop.name = name
            if name != shop.name:
                shop.slug = generate_slug(name)
            shop.description = description
            shop.country = country
            shop.currency = currency
            shop.contact_email = contact_email
            shop.contact_phone = contact_phone
            shop.address = address
            shop.social_links = social_media

            db.session.commit()

            flash('Your shop has been updated successfully.', 'success')
            return redirect(url_for('shop.view', slug=shop.slug))

        except Exception as e:
            db.session.rollback()
            flash('An error occurred while updating your shop. Please try again.', 'error')

    return render_template('shop/edit.html', shop=shop, countries=Config.AFRICAN_COUNTRIES)

@shop_bp.route('/<int:shop_id>/products')
@login_required
@vendor_required
def manage_products(shop_id):
    """Manage shop products."""
    shop = Shop.query.filter_by(id=shop_id, owner_id=current_user.id).first_or_404()

    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', 'all')

    # Build query
    products_query = Product.query.filter_by(shop_id=shop.id)

    if status_filter != 'all':
        if status_filter == 'active':
            products_query = products_query.filter_by(status=ProductStatus.ACTIVE)
        elif status_filter == 'pending':
            products_query = products_query.filter_by(status=ProductStatus.PENDING)
        elif status_filter == 'rejected':
            products_query = products_query.filter_by(status=ProductStatus.REJECTED)

    products = products_query.order_by(Product.created_at.desc()).paginate(
        page=page,
        per_page=Config.PRODUCTS_PER_PAGE,
        error_out=False
    )

    return render_template('shop/manage_products.html',
                         shop=shop,
                         products=products,
                         status_filter=status_filter)

@shop_bp.route('/<int:shop_id>/orders')
@login_required
@vendor_required
def manage_orders(shop_id):
    """Manage shop orders."""
    shop = Shop.query.filter_by(id=shop_id, owner_id=current_user.id).first_or_404()

    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', 'all')

    # Build query
    orders_query = Order.query.filter_by(shop_id=shop.id)

    if status_filter != 'all':
        orders_query = orders_query.filter_by(status=status_filter)

    orders = orders_query.order_by(Order.created_at.desc()).paginate(
        page=page,
        per_page=Config.ORDERS_PER_PAGE,
        error_out=False
    )

    return render_template('shop/manage_orders.html',
                         shop=shop,
                         orders=orders,
                         status_filter=status_filter)

@shop_bp.route('/<int:shop_id>/orders/<int:order_id>')
@login_required
@vendor_required
def view_order(shop_id, order_id):
    """View order details."""
    shop = Shop.query.filter_by(id=shop_id, owner_id=current_user.id).first_or_404()
    order = Order.query.filter_by(id=order_id, shop_id=shop.id).first_or_404()

    return render_template('shop/order_details.html',
                         shop=shop,
                         order=order)

@shop_bp.route('/order/details/<int:order_id>')
@login_required
@vendor_required
def order_details_ajax(order_id):
    """Get order details for AJAX modal."""
    # Find the order and verify the user owns the shop
    order = Order.query.join(Shop).filter(
        Order.id == order_id,
        Shop.owner_id == current_user.id
    ).first_or_404()

    return render_template('shop/order_details_modal.html', order=order)

@shop_bp.route('/order/<int:order_id>/status', methods=['POST'])
@login_required
@vendor_required
def update_order_status(order_id):
    """Update order status."""
    # Find the order and verify the user owns the shop
    order = Order.query.join(Shop).filter(
        Order.id == order_id,
        Shop.owner_id == current_user.id
    ).first_or_404()

    data = request.get_json()
    new_status = data.get('status')

    if new_status not in ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded']:
        return jsonify({'success': False, 'message': 'Invalid status'})

    try:
        from models import OrderStatus
        order.status = OrderStatus(new_status)
        db.session.commit()

        flash(f'Statut de la commande mis à jour: {new_status}', 'success')
        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@shop_bp.route('/<int:shop_id>/orders/create', methods=['GET', 'POST'])
@login_required
@vendor_required
def create_manual_order(shop_id):
    """Create a manual order for the shop."""
    shop = Shop.query.filter_by(id=shop_id, owner_id=current_user.id).first_or_404()

    if request.method == 'POST':
        try:
            # Get form data
            customer_name = request.form.get('customer_name', '').strip()
            customer_email = request.form.get('customer_email', '').strip()
            customer_phone = request.form.get('customer_phone', '').strip()

            # Shipping address
            shipping_address = {
                'name': customer_name,
                'email': customer_email,
                'phone': customer_phone,
                'address': request.form.get('address', '').strip(),
                'city': request.form.get('city', '').strip(),
                'country': request.form.get('country', '').strip()
            }

            # Payment method
            payment_method = request.form.get('payment_method', '')
            customer_notes = request.form.get('customer_notes', '')

            # Get selected products and quantities
            product_ids = request.form.getlist('product_ids')
            quantities = request.form.getlist('quantities')

            if not customer_name or not customer_email:
                flash('Nom et email du client sont requis.', 'error')
                return redirect(url_for('shop.create_manual_order', shop_id=shop_id))

            if not product_ids:
                flash('Veuillez sélectionner au moins un produit.', 'error')
                return redirect(url_for('shop.create_manual_order', shop_id=shop_id))

            # Calculate totals
            subtotal = Decimal('0.00')
            order_items_data = []

            for i, product_id in enumerate(product_ids):
                if not product_id:
                    continue

                product = Product.query.filter_by(
                    id=int(product_id),
                    shop_id=shop_id,
                    status=ProductStatus.ACTIVE
                ).first()

                if not product:
                    continue

                quantity = int(quantities[i]) if i < len(quantities) and quantities[i] else 1

                if quantity > product.stock_quantity:
                    flash(f'Stock insuffisant pour {product.name}. Disponible: {product.stock_quantity}', 'error')
                    return redirect(url_for('shop.create_manual_order', shop_id=shop_id))

                price = product.get_display_price()
                total = price * quantity
                subtotal += total

                order_items_data.append({
                    'product': product,
                    'quantity': quantity,
                    'price': price,
                    'total': total
                })

            if not order_items_data:
                flash('Aucun produit valide sélectionné.', 'error')
                return redirect(url_for('shop.create_manual_order', shop_id=shop_id))

            # Calculate other costs
            shipping_cost = Decimal('0.00')
            tax_amount = Decimal('0.00')
            commission_amount = calculate_commission(subtotal, shop.owner.tier.value)
            total = subtotal + shipping_cost + tax_amount

            # Create order
            order = Order(
                order_number=generate_order_number(),
                user_id=None,  # Manual order, no user account
                shop_id=shop_id,
                subtotal=subtotal,
                shipping_cost=shipping_cost,
                tax_amount=tax_amount,
                commission_amount=commission_amount,
                total=total,
                shipping_address=shipping_address,
                payment_method=payment_method,
                customer_notes=customer_notes,
                admin_notes=f'Commande manuelle créée par {current_user.get_full_name()}'
            )

            db.session.add(order)
            db.session.flush()  # Get order ID

            # Create order items and update stock
            for item_data in order_items_data:
                order_item = OrderItem(
                    order_id=order.id,
                    product_id=item_data['product'].id,
                    quantity=item_data['quantity'],
                    price=item_data['price'],
                    total=item_data['total']
                )

                # Update product stock
                item_data['product'].stock_quantity -= item_data['quantity']

                db.session.add(order_item)

            db.session.commit()

            flash(f'Commande manuelle {order.order_number} créée avec succès!', 'success')
            return redirect(url_for('shop.manage_orders', shop_id=shop_id))

        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de la création de la commande. Veuillez réessayer.', 'error')
            return redirect(url_for('shop.create_manual_order', shop_id=shop_id))

    # GET request - show form
    # Get shop products for selection
    products = Product.query.filter_by(
        shop_id=shop_id,
        status=ProductStatus.ACTIVE
    ).order_by(Product.name.asc()).all()

    # Get payment methods
    payment_methods = shop.get_payment_methods()

    return render_template('shop/create_manual_order.html',
                         shop=shop,
                         products=products,
                         payment_methods=payment_methods)

@shop_bp.route('/payment-methods')
@login_required
@vendor_required
def payment_methods():
    """Manage payment methods for all user's shops."""
    # Get user's shops
    shops = Shop.query.filter_by(owner_id=current_user.id).order_by(Shop.created_at.desc()).all()

    return render_template('shop/payment_methods.html', shops=shops)

@shop_bp.route('/<int:shop_id>/payment-methods', methods=['GET', 'POST'])
@login_required
@vendor_required
def edit_payment_methods(shop_id):
    """Edit payment methods for a specific shop."""
    shop = Shop.query.filter_by(id=shop_id, owner_id=current_user.id).first_or_404()

    if request.method == 'POST':
        # Handle payment methods
        payment_methods = []
        payment_method_keys = request.form.getlist('payment_methods')

        for method_key in payment_method_keys:
            if not shop.can_use_payment_method(method_key):
                continue

            method_data = {'type': method_key, 'details': {}}

            # Get method configuration
            method_config = None
            for category, methods in Config.PAYMENT_METHODS.items():
                if method_key in methods:
                    method_config = methods[method_key]
                    break

            if method_config:
                # Collect field data for this payment method
                for field in method_config.get('fields', []):
                    field_name = f"{method_key}_{field}"
                    field_value = request.form.get(field_name, '').strip()
                    if field_value:
                        method_data['details'][field] = field_value

                # Only add if we have some details
                if method_data['details']:
                    payment_methods.append(method_data)

        # Update payment information
        if not shop.payment_info:
            shop.payment_info = {}
        shop.payment_info['methods'] = payment_methods

        db.session.commit()
        flash('Méthodes de paiement mises à jour avec succès.', 'success')
        return redirect(url_for('shop.payment_methods'))

    return render_template('shop/edit_payment_methods.html', shop=shop)

# Credit Management Routes
@shop_bp.route('/credits')
@login_required
@vendor_required
def credits_dashboard():
    """User credits dashboard."""
    # Get credit packages
    packages = CreditPackage.query.filter_by(is_active=True).order_by(
        CreditPackage.display_order, CreditPackage.credits
    ).all()

    # Get recent credit transactions
    recent_transactions = CreditTransaction.query.filter_by(
        user_id=current_user.id
    ).order_by(CreditTransaction.created_at.desc()).limit(10).all()

    # Get active boosts
    active_boosts = ProductBoost.query.join(Product).filter(
        ProductBoost.user_id == current_user.id,
        ProductBoost.is_active == True,
        ProductBoost.expires_at > db.func.now()
    ).order_by(ProductBoost.expires_at.desc()).all()

    return render_template('shop/credits_dashboard.html',
                         packages=packages,
                         recent_transactions=recent_transactions,
                         active_boosts=active_boosts)

@shop_bp.route('/credits/purchase/<int:package_id>', methods=['POST'])
@login_required
@vendor_required
def purchase_credits(package_id):
    """Purchase credits - creates pending transaction for admin approval."""
    package = CreditPackage.query.filter_by(id=package_id, is_active=True).first_or_404()

    try:
        # Create pending credit transaction
        total_credits = package.get_total_credits()
        transaction = current_user.add_credits(
            total_credits,
            CreditTransactionType.PURCHASE,
            f"Achat package {package.name} - {total_credits} crédits"
        )

        # Add package information to transaction
        transaction.payment_method = f"Package {package.name}"
        transaction.payment_id = f"PKG-{package.id}-{transaction.id}"

        db.session.commit()

        flash(f'Votre demande d\'achat de {total_credits} crédits a été soumise. Elle sera traitée par un administrateur sous peu.', 'info')
        return redirect(url_for('shop.credits_dashboard'))

    except Exception as e:
        db.session.rollback()
        flash('Erreur lors de la soumission de votre demande d\'achat.', 'error')
        return redirect(url_for('shop.credits_dashboard'))

@shop_bp.route('/products/<int:product_id>/boost', methods=['GET', 'POST'])
@login_required
@vendor_required
def boost_product(product_id):
    """Boost a product."""
    # Verify product ownership
    product = Product.query.join(Shop).filter(
        Product.id == product_id,
        Shop.owner_id == current_user.id
    ).first_or_404()

    if request.method == 'POST':
        duration_days = request.form.get('duration_days', 7, type=int)

        # Calculate credits needed (updated pricing: 50 credits per week)
        credits_per_week = 50
        credits_needed = max(1, (duration_days / 7) * credits_per_week)
        credits_needed = int(credits_needed)

        # Check if product can be boosted
        if not product.can_be_boosted():
            flash('Ce produit ne peut pas être boosté actuellement.', 'error')
            return redirect(url_for('shop.manage_products', shop_id=product.shop_id))

        # Check if user has sufficient credits
        if not current_user.has_sufficient_credits(credits_needed):
            flash(f'Crédits insuffisants. Vous avez besoin de {credits_needed} crédits.', 'error')
            return redirect(url_for('shop.credits_dashboard'))

        try:
            from datetime import datetime, timedelta

            # Deduct credits
            current_user.deduct_credits(
                credits_needed,
                CreditTransactionType.BOOST,
                f"Boost produit: {product.name}"
            )

            # Create boost record
            boost = ProductBoost(
                product_id=product.id,
                user_id=current_user.id,
                credits_spent=credits_needed,
                duration_days=duration_days,
                expires_at=datetime.utcnow() + timedelta(days=duration_days)
            )

            db.session.add(boost)
            db.session.commit()

            flash(f'Produit "{product.name}" boosté pour {duration_days} jours !', 'success')
            return redirect(url_for('shop.manage_products', shop_id=product.shop_id))

        except Exception as e:
            db.session.rollback()
            flash('Erreur lors du boost du produit.', 'error')

    # Calculate credit costs for different durations
    credit_costs = {
        7: 50,   # 1 week
        14: 90,  # 2 weeks (10% discount)
        30: 150  # 1 month (20% discount)
    }

    return render_template('shop/boost_product.html',
                         product=product,
                         credit_costs=credit_costs)

@shop_bp.route('/boosts')
@login_required
@vendor_required
def manage_boosts():
    """Manage user's product boosts."""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', 'all')

    # Build query
    boosts_query = ProductBoost.query.join(Product).filter(
        ProductBoost.user_id == current_user.id
    )

    if status_filter == 'active':
        boosts_query = boosts_query.filter(
            ProductBoost.is_active == True,
            ProductBoost.expires_at > db.func.now()
        )
    elif status_filter == 'expired':
        boosts_query = boosts_query.filter(
            db.or_(
                ProductBoost.is_active == False,
                ProductBoost.expires_at <= db.func.now()
            )
        )

    boosts = boosts_query.order_by(ProductBoost.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('shop/manage_boosts.html',
                         boosts=boosts,
                         status_filter=status_filter)
