from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import (db, EmailTemplate, EmailQueue, Newsletter, NewsletterSubscriber, 
                   EmailType, EmailStatus, User, SiteSetting)
from utils.decorators import admin_required
from services.email_service import email_service
from datetime import datetime
import json

email_bp = Blueprint('email', __name__)

@email_bp.route('/settings')
@login_required
@admin_required
def settings():
    """Email settings page."""
    # Get current SMTP settings from database
    smtp_settings = {
        'mail_server': SiteSetting.get_value('mail_server', ''),
        'mail_port': SiteSetting.get_value('mail_port', '587'),
        'mail_use_tls': SiteSetting.get_value('mail_use_tls', 'true'),
        'mail_use_ssl': SiteSetting.get_value('mail_use_ssl', 'false'),
        'mail_username': SiteSetting.get_value('mail_username', ''),
        'mail_password': SiteSetting.get_value('mail_password', ''),
        'mail_default_sender': SiteSetting.get_value('mail_default_sender', '<EMAIL>'),
    }
    
    return render_template('admin/email_settings.html', smtp_settings=smtp_settings)

@email_bp.route('/settings', methods=['POST'])
@login_required
@admin_required
def update_settings():
    """Update email settings."""
    try:
        # Update SMTP settings
        settings_to_update = [
            'mail_server', 'mail_port', 'mail_use_tls', 'mail_use_ssl',
            'mail_username', 'mail_password', 'mail_default_sender'
        ]
        
        for setting in settings_to_update:
            value = request.form.get(setting, '')
            SiteSetting.set_value(setting, value)
        
        db.session.commit()
        flash('Paramètres email mis à jour avec succès !', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la mise à jour : {str(e)}', 'error')
    
    return redirect(url_for('email.settings'))

@email_bp.route('/test')
@login_required
@admin_required
def test_email():
    """Test email configuration."""
    try:
        success = email_service.send_email(
            recipient_email=current_user.email,
            subject='Test Email - Afroly.org',
            html_content='''
            <h2>Test Email</h2>
            <p>Si vous recevez cet email, votre configuration SMTP fonctionne correctement !</p>
            <p>Envoyé le : {}</p>
            '''.format(datetime.now().strftime('%d/%m/%Y à %H:%M')),
            text_content='Test email - Configuration SMTP fonctionnelle !',
            recipient_name=current_user.get_full_name()
        )
        
        if success:
            flash('Email de test envoyé avec succès ! Vérifiez votre boîte de réception.', 'success')
        else:
            flash('Échec de l\'envoi de l\'email de test. Vérifiez votre configuration SMTP.', 'error')
            
    except Exception as e:
        flash(f'Erreur lors du test : {str(e)}', 'error')
    
    return redirect(url_for('email.settings'))

@email_bp.route('/templates')
@login_required
@admin_required
def templates():
    """Email templates management."""
    templates = EmailTemplate.query.all()
    return render_template('admin/email_templates.html', templates=templates)

@email_bp.route('/templates/create')
@login_required
@admin_required
def create_template():
    """Create new email template."""
    return render_template('admin/email_template_form.html', 
                         template=None, 
                         email_types=EmailType)

@email_bp.route('/templates/create', methods=['POST'])
@login_required
@admin_required
def store_template():
    """Store new email template."""
    try:
        template = EmailTemplate(
            name=request.form.get('name'),
            email_type=EmailType(request.form.get('email_type')),
            subject=request.form.get('subject'),
            html_content=request.form.get('html_content'),
            text_content=request.form.get('text_content'),
            variables=json.loads(request.form.get('variables', '{}'))
        )
        
        db.session.add(template)
        db.session.commit()
        
        flash('Template email créé avec succès !', 'success')
        return redirect(url_for('email.templates'))
        
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la création : {str(e)}', 'error')
        return redirect(url_for('email.create_template'))

@email_bp.route('/templates/<int:template_id>/edit')
@login_required
@admin_required
def edit_template(template_id):
    """Edit email template."""
    template = EmailTemplate.query.get_or_404(template_id)
    return render_template('admin/email_template_form.html', 
                         template=template, 
                         email_types=EmailType)

@email_bp.route('/templates/<int:template_id>/edit', methods=['POST'])
@login_required
@admin_required
def update_template(template_id):
    """Update email template."""
    try:
        template = EmailTemplate.query.get_or_404(template_id)
        
        template.name = request.form.get('name')
        template.email_type = EmailType(request.form.get('email_type'))
        template.subject = request.form.get('subject')
        template.html_content = request.form.get('html_content')
        template.text_content = request.form.get('text_content')
        template.variables = json.loads(request.form.get('variables', '{}'))
        template.is_active = 'is_active' in request.form
        
        db.session.commit()
        
        flash('Template email mis à jour avec succès !', 'success')
        return redirect(url_for('email.templates'))
        
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la mise à jour : {str(e)}', 'error')
        return redirect(url_for('email.edit_template', template_id=template_id))

@email_bp.route('/queue')
@login_required
@admin_required
def queue():
    """Email queue management."""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', '')
    
    query = EmailQueue.query
    
    if status_filter:
        query = query.filter_by(status=EmailStatus(status_filter))
    
    emails = query.order_by(EmailQueue.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('admin/email_queue.html', 
                         emails=emails, 
                         status_filter=status_filter,
                         email_statuses=EmailStatus)

@email_bp.route('/queue/process')
@login_required
@admin_required
def process_queue():
    """Process email queue manually."""
    try:
        sent_count = email_service.process_email_queue(limit=50)
        flash(f'{sent_count} emails traités avec succès !', 'success')
    except Exception as e:
        flash(f'Erreur lors du traitement : {str(e)}', 'error')
    
    return redirect(url_for('email.queue'))

@email_bp.route('/newsletters')
@login_required
@admin_required
def newsletters():
    """Newsletter management."""
    newsletters = Newsletter.query.order_by(Newsletter.created_at.desc()).all()
    return render_template('admin/newsletters.html', newsletters=newsletters)

@email_bp.route('/newsletters/create')
@login_required
@admin_required
def create_newsletter():
    """Create new newsletter."""
    return render_template('admin/newsletter_form.html', newsletter=None)

@email_bp.route('/newsletters/create', methods=['POST'])
@login_required
@admin_required
def store_newsletter():
    """Store new newsletter."""
    try:
        newsletter = Newsletter(
            title=request.form.get('title'),
            subject=request.form.get('subject'),
            html_content=request.form.get('html_content'),
            text_content=request.form.get('text_content'),
            target_audience=request.form.get('target_audience', 'all'),
            created_by=current_user.id
        )
        
        if request.form.get('schedule_date'):
            newsletter.scheduled_at = datetime.strptime(
                request.form.get('schedule_date'), '%Y-%m-%dT%H:%M'
            )
            newsletter.status = 'scheduled'
        
        db.session.add(newsletter)
        db.session.commit()
        
        flash('Newsletter créée avec succès !', 'success')
        return redirect(url_for('email.newsletters'))
        
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la création : {str(e)}', 'error')
        return redirect(url_for('email.create_newsletter'))

@email_bp.route('/newsletters/<int:newsletter_id>/send')
@login_required
@admin_required
def send_newsletter(newsletter_id):
    """Send newsletter to subscribers."""
    try:
        newsletter = Newsletter.query.get_or_404(newsletter_id)
        
        if newsletter.status == 'sent':
            flash('Cette newsletter a déjà été envoyée !', 'warning')
            return redirect(url_for('email.newsletters'))
        
        # Get subscribers based on target audience
        if newsletter.target_audience == 'all':
            subscribers = NewsletterSubscriber.query.filter_by(is_active=True).all()
        elif newsletter.target_audience == 'vendors':
            subscribers = NewsletterSubscriber.query.join(User).filter(
                User.role == 'vendor',
                NewsletterSubscriber.is_active == True
            ).all()
        elif newsletter.target_audience == 'shoppers':
            subscribers = NewsletterSubscriber.query.join(User).filter(
                User.role == 'shopper',
                NewsletterSubscriber.is_active == True
            ).all()
        else:
            subscribers = NewsletterSubscriber.query.filter_by(is_active=True).all()
        
        # Queue emails for all subscribers
        sent_count = 0
        for subscriber in subscribers:
            success = email_service.queue_email(
                email_type=EmailType.NEWSLETTER,
                recipient_email=subscriber.email,
                template_data={
                    'subscriber_name': subscriber.name or 'Cher(e) abonné(e)',
                    'newsletter_title': newsletter.title,
                    'newsletter_content': newsletter.html_content,
                    'unsubscribe_url': url_for('email.unsubscribe', 
                                             email=subscriber.email, _external=True)
                },
                recipient_name=subscriber.name
            )
            if success:
                sent_count += 1
        
        # Update newsletter status
        newsletter.status = 'sent'
        newsletter.sent_at = datetime.utcnow()
        newsletter.total_recipients = len(subscribers)
        newsletter.sent_count = sent_count
        
        db.session.commit()
        
        flash(f'Newsletter envoyée à {sent_count} abonnés !', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'envoi : {str(e)}', 'error')
    
    return redirect(url_for('email.newsletters'))

@email_bp.route('/subscribers')
@login_required
@admin_required
def subscribers():
    """Newsletter subscribers management."""
    page = request.args.get('page', 1, type=int)
    subscribers = NewsletterSubscriber.query.order_by(
        NewsletterSubscriber.subscribed_at.desc()
    ).paginate(page=page, per_page=50, error_out=False)
    
    return render_template('admin/newsletter_subscribers.html', subscribers=subscribers)

@email_bp.route('/unsubscribe')
def unsubscribe():
    """Unsubscribe from newsletter."""
    email = request.args.get('email')
    if not email:
        flash('Email non spécifié.', 'error')
        return redirect(url_for('main.index'))
    
    subscriber = NewsletterSubscriber.query.filter_by(email=email).first()
    if subscriber:
        subscriber.is_active = False
        subscriber.unsubscribed_at = datetime.utcnow()
        db.session.commit()
        flash('Vous avez été désabonné(e) avec succès de notre newsletter.', 'success')
    else:
        flash('Email non trouvé dans notre liste d\'abonnés.', 'warning')
    
    return render_template('emails/unsubscribed.html')
