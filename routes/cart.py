from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import login_required, current_user
from models import db, Cart, CartItem, Product, Shop, Order, OrderItem, ProductStatus, OrderStatus
from utils.helpers import generate_order_number, calculate_commission
from config import Config
from decimal import Decimal

cart_bp = Blueprint('cart', __name__)

@cart_bp.route('/add', methods=['POST'])
def add_to_cart():
    """Add product to cart (shop-specific)."""
    product_id = request.form.get('product_id', type=int)
    quantity = request.form.get('quantity', 1, type=int)

    if not product_id:
        flash('Invalid product.', 'error')
        return redirect(url_for('main.index'))

    product = Product.query.filter_by(id=product_id, status=ProductStatus.ACTIVE).first()
    if not product:
        flash('Product not found.', 'error')
        return redirect(url_for('main.index'))

    if quantity <= 0:
        flash('Invalid quantity.', 'error')
        return redirect(url_for('product.view', slug=product.slug))

    if quantity > product.stock_quantity:
        flash(f'Only {product.stock_quantity} items available in stock.', 'error')
        return redirect(url_for('product.view', slug=product.slug))

    # For guest users, use session-based cart
    if not current_user.is_authenticated:
        return add_to_session_cart(product, quantity)

    # For logged-in users, use database cart
    try:
        # Get or create cart for this shop
        cart = Cart.query.filter_by(
            user_id=current_user.id,
            shop_id=product.shop_id
        ).first()

        if not cart:
            cart = Cart(user_id=current_user.id, shop_id=product.shop_id)
            db.session.add(cart)
            db.session.flush()  # Get cart ID

        # Check if product already in cart
        cart_item = CartItem.query.filter_by(
            cart_id=cart.id,
            product_id=product.id
        ).first()

        if cart_item:
            # Update quantity
            new_quantity = cart_item.quantity + quantity
            if new_quantity > product.stock_quantity:
                flash(f'Cannot add more items. Only {product.stock_quantity} available.', 'error')
                return redirect(url_for('product.view', slug=product.slug))

            cart_item.quantity = new_quantity
        else:
            # Add new item
            cart_item = CartItem(
                cart_id=cart.id,
                product_id=product.id,
                quantity=quantity
            )
            db.session.add(cart_item)

        db.session.commit()
        flash(f'{product.name} added to cart!', 'success')

    except Exception as e:
        db.session.rollback()
        flash('Error adding product to cart. Please try again.', 'error')

    return redirect(url_for('product.view', slug=product.slug))

@cart_bp.route('/buy-now', methods=['POST'])
@login_required
def buy_now():
    """Buy a product immediately without adding to cart."""
    product_id = request.form.get('product_id', type=int)
    quantity = request.form.get('quantity', 1, type=int)

    if not product_id or quantity <= 0:
        return jsonify({'success': False, 'message': 'Produit ou quantité invalide.'})

    product = Product.query.filter_by(
        id=product_id,
        status=ProductStatus.ACTIVE
    ).first()

    if not product:
        return jsonify({'success': False, 'message': 'Produit non trouvé.'})

    if quantity > product.stock_quantity:
        return jsonify({'success': False, 'message': f'Seulement {product.stock_quantity} articles disponibles en stock.'})

    try:
        # Create a temporary cart for this purchase
        cart = Cart.query.filter_by(
            user_id=current_user.id,
            shop_id=product.shop_id
        ).first()

        if not cart:
            cart = Cart(user_id=current_user.id, shop_id=product.shop_id)
            db.session.add(cart)
            db.session.flush()

        # Clear existing cart items for this shop (buy now replaces cart)
        CartItem.query.filter_by(cart_id=cart.id).delete()

        # Add the buy now item
        cart_item = CartItem(
            cart_id=cart.id,
            product_id=product.id,
            quantity=quantity
        )
        db.session.add(cart_item)
        db.session.commit()

        # Redirect to checkout
        checkout_url = url_for('cart.checkout', shop_id=product.shop_id)
        return jsonify({'success': True, 'checkout_url': checkout_url})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Erreur lors du traitement de votre commande.'})

def add_to_session_cart(product, quantity):
    """Add product to session-based cart for guest users."""
    if 'carts' not in session:
        session['carts'] = {}

    shop_id = str(product.shop_id)
    if shop_id not in session['carts']:
        session['carts'][shop_id] = {}

    product_id = str(product.id)
    if product_id in session['carts'][shop_id]:
        new_quantity = session['carts'][shop_id][product_id] + quantity
        if new_quantity > product.stock_quantity:
            flash(f'Cannot add more items. Only {product.stock_quantity} available.', 'error')
            return redirect(url_for('product.view', slug=product.slug))
        session['carts'][shop_id][product_id] = new_quantity
    else:
        session['carts'][shop_id][product_id] = quantity

    session.modified = True
    flash(f'{product.name} added to cart!', 'success')
    return redirect(url_for('product.view', slug=product.slug))

@cart_bp.route('/view/<int:shop_id>')
def view_cart(shop_id):
    """View cart for a specific shop."""
    shop = Shop.query.get_or_404(shop_id)

    if current_user.is_authenticated:
        # Get database cart
        cart = Cart.query.filter_by(
            user_id=current_user.id,
            shop_id=shop_id
        ).first()

        cart_items = cart.items if cart else []

    else:
        # Get session cart
        cart_items = []
        if 'carts' in session and str(shop_id) in session['carts']:
            shop_cart = session['carts'][str(shop_id)]
            for product_id, quantity in shop_cart.items():
                product = Product.query.get(int(product_id))
                if product and product.status == ProductStatus.ACTIVE:
                    # Create a mock cart item object
                    class MockCartItem:
                        def __init__(self, product, quantity):
                            self.product = product
                            self.quantity = quantity

                        def get_total_price(self):
                            return self.product.get_display_price() * self.quantity

                    cart_items.append(MockCartItem(product, quantity))

    # Calculate totals
    subtotal = sum(item.get_total_price() for item in cart_items)
    shipping_cost = Decimal('0.00')  # Can be calculated based on shop settings
    total = subtotal + shipping_cost

    return render_template('cart/view.html',
                         shop=shop,
                         cart_items=cart_items,
                         subtotal=subtotal,
                         shipping_cost=shipping_cost,
                         total=total)

@cart_bp.route('/update', methods=['POST'])
def update_cart():
    """Update cart item quantity."""
    product_id = request.form.get('product_id', type=int)
    quantity = request.form.get('quantity', type=int)
    shop_id = request.form.get('shop_id', type=int)

    if not all([product_id, shop_id]) or quantity < 0:
        flash('Invalid request.', 'error')
        return redirect(url_for('main.index'))

    product = Product.query.get(product_id)
    if not product:
        flash('Product not found.', 'error')
        return redirect(url_for('cart.view_cart', shop_id=shop_id))

    if quantity > product.stock_quantity:
        flash(f'Only {product.stock_quantity} items available.', 'error')
        return redirect(url_for('cart.view_cart', shop_id=shop_id))

    if current_user.is_authenticated:
        # Update database cart
        cart = Cart.query.filter_by(
            user_id=current_user.id,
            shop_id=shop_id
        ).first()

        if cart:
            cart_item = CartItem.query.filter_by(
                cart_id=cart.id,
                product_id=product_id
            ).first()

            if cart_item:
                if quantity == 0:
                    db.session.delete(cart_item)
                else:
                    cart_item.quantity = quantity

                db.session.commit()
                flash('Cart updated successfully.', 'success')
    else:
        # Update session cart
        if 'carts' in session and str(shop_id) in session['carts']:
            if quantity == 0:
                session['carts'][str(shop_id)].pop(str(product_id), None)
            else:
                session['carts'][str(shop_id)][str(product_id)] = quantity

            session.modified = True
            flash('Cart updated successfully.', 'success')

    return redirect(url_for('cart.view_cart', shop_id=shop_id))

@cart_bp.route('/remove', methods=['POST'])
def remove_from_cart():
    """Remove item from cart."""
    product_id = request.form.get('product_id', type=int)
    shop_id = request.form.get('shop_id', type=int)

    if not all([product_id, shop_id]):
        flash('Invalid request.', 'error')
        return redirect(url_for('main.index'))

    if current_user.is_authenticated:
        # Remove from database cart
        cart = Cart.query.filter_by(
            user_id=current_user.id,
            shop_id=shop_id
        ).first()

        if cart:
            cart_item = CartItem.query.filter_by(
                cart_id=cart.id,
                product_id=product_id
            ).first()

            if cart_item:
                db.session.delete(cart_item)
                db.session.commit()
                flash('Item removed from cart.', 'success')
    else:
        # Remove from session cart
        if 'carts' in session and str(shop_id) in session['carts']:
            session['carts'][str(shop_id)].pop(str(product_id), None)
            session.modified = True
            flash('Item removed from cart.', 'success')

    return redirect(url_for('cart.view_cart', shop_id=shop_id))

@cart_bp.route('/checkout/<int:shop_id>')
@login_required
def checkout(shop_id):
    """Checkout page for a specific shop."""
    shop = Shop.query.get_or_404(shop_id)

    # Get cart items
    cart = Cart.query.filter_by(
        user_id=current_user.id,
        shop_id=shop_id
    ).first()

    if not cart or not cart.items:
        flash('Your cart is empty.', 'error')
        return redirect(url_for('shop.view', slug=shop.slug))

    # Validate stock availability
    for item in cart.items:
        if item.quantity > item.product.stock_quantity:
            flash(f'Insufficient stock for {item.product.name}. Only {item.product.stock_quantity} available.', 'error')
            return redirect(url_for('cart.view_cart', shop_id=shop_id))

    # Calculate totals
    subtotal = cart.get_total_price()
    shipping_cost = Decimal('0.00')  # Can be calculated based on shop settings
    tax_amount = Decimal('0.00')  # Can be calculated based on location
    commission_amount = calculate_commission(subtotal, shop.owner.tier.value)
    total = subtotal + shipping_cost + tax_amount

    return render_template('cart/checkout.html',
                         shop=shop,
                         cart=cart,
                         subtotal=subtotal,
                         shipping_cost=shipping_cost,
                         tax_amount=tax_amount,
                         commission_amount=commission_amount,
                         total=total)

@cart_bp.route('/place-order/<int:shop_id>', methods=['POST'])
@login_required
def place_order(shop_id):
    """Place an order for a specific shop."""
    shop = Shop.query.get_or_404(shop_id)

    # Get cart
    cart = Cart.query.filter_by(
        user_id=current_user.id,
        shop_id=shop_id
    ).first()

    if not cart or not cart.items:
        flash('Your cart is empty.', 'error')
        return redirect(url_for('shop.view', slug=shop.slug))

    # Get shipping address from form
    shipping_address = {
        'first_name': request.form.get('first_name'),
        'last_name': request.form.get('last_name'),
        'address_line_1': request.form.get('address_line_1'),
        'address_line_2': request.form.get('address_line_2'),
        'city': request.form.get('city'),
        'state': request.form.get('state'),
        'postal_code': request.form.get('postal_code'),
        'country': request.form.get('country'),
        'phone': request.form.get('phone')
    }

    # Validate required fields
    required_fields = ['first_name', 'last_name', 'address_line_1', 'city', 'state', 'country', 'phone']
    if not all(shipping_address.get(field) for field in required_fields):
        flash('Please fill in all required shipping information.', 'error')
        return redirect(url_for('cart.checkout', shop_id=shop_id))

    try:
        # Calculate totals
        subtotal = cart.get_total_price()
        shipping_cost = Decimal('0.00')
        tax_amount = Decimal('0.00')
        commission_amount = calculate_commission(subtotal, shop.owner.tier.value)
        total = subtotal + shipping_cost + tax_amount

        # Create order
        order = Order(
            order_number=generate_order_number(),
            user_id=current_user.id,
            shop_id=shop_id,
            subtotal=subtotal,
            shipping_cost=shipping_cost,
            tax_amount=tax_amount,
            commission_amount=commission_amount,
            total=total,
            shipping_address=shipping_address,
            customer_notes=request.form.get('notes', '')
        )

        db.session.add(order)
        db.session.flush()  # Get order ID

        # Create order items and update stock
        for cart_item in cart.items:
            # Check stock again
            if cart_item.quantity > cart_item.product.stock_quantity:
                raise Exception(f'Insufficient stock for {cart_item.product.name}')

            order_item = OrderItem(
                order_id=order.id,
                product_id=cart_item.product_id,
                quantity=cart_item.quantity,
                price=cart_item.product.get_display_price(),
                total=cart_item.get_total_price()
            )

            # Update product stock
            cart_item.product.stock_quantity -= cart_item.quantity

            db.session.add(order_item)

        # Clear cart
        for cart_item in cart.items:
            db.session.delete(cart_item)
        db.session.delete(cart)

        db.session.commit()

        flash(f'Order {order.order_number} placed successfully!', 'success')
        return redirect(url_for('cart.order_confirmation', order_number=order.order_number))

    except Exception as e:
        db.session.rollback()
        flash('An error occurred while placing your order. Please try again.', 'error')
        return redirect(url_for('cart.checkout', shop_id=shop_id))

@cart_bp.route('/order-confirmation/<order_number>')
@login_required
def order_confirmation(order_number):
    """Order confirmation page."""
    order = Order.query.filter_by(
        order_number=order_number,
        user_id=current_user.id
    ).first_or_404()

    return render_template('cart/order_confirmation.html', order=order)

@cart_bp.route('/my-carts')
@login_required
def my_carts():
    """View all user's active carts."""
    carts = Cart.query.filter_by(user_id=current_user.id).all()

    # Filter out empty carts
    active_carts = [cart for cart in carts if cart.items]

    return render_template('cart/my_carts.html', carts=active_carts)
