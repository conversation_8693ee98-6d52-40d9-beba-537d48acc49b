from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from flask_login import login_user, logout_user, login_required, current_user
from urllib.parse import urlparse
from models import db, User, UserRole, UserTier
import re

auth_bp = Blueprint('auth', __name__)

def is_valid_email(email):
    """Validate email format."""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def is_valid_password(password):
    """Validate password strength."""
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    if not re.search(r'[A-Za-z]', password):
        return False, "Password must contain at least one letter"
    if not re.search(r'\d', password):
        return False, "Password must contain at least one number"
    return True, "Valid password"

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login."""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))

    if request.method == 'POST':
        email = request.form.get('email', '').strip().lower()
        password = request.form.get('password', '')
        remember_me = bool(request.form.get('remember_me'))

        # Validation
        if not email or not password:
            flash('Please fill in all fields.', 'error')
            return render_template('auth/login.html')

        if not is_valid_email(email):
            flash('Please enter a valid email address.', 'error')
            return render_template('auth/login.html')

        # Find user
        user = User.query.filter_by(email=email).first()

        if user is None or not user.check_password(password):
            flash('Invalid email or password.', 'error')
            return render_template('auth/login.html')

        if not user.is_active:
            flash('Your account has been deactivated. Please contact support.', 'error')
            return render_template('auth/login.html')

        # Login user
        login_user(user, remember=remember_me)
        flash(f'Welcome back, {user.get_full_name()}!', 'success')

        # Redirect to next page or dashboard
        next_page = request.args.get('next')
        if not next_page or urlparse(next_page).netloc != '':
            if user.role == UserRole.ADMIN:
                next_page = url_for('admin.dashboard')
            elif user.role == UserRole.VENDOR:
                next_page = url_for('shop.vendor_dashboard')
            else:
                next_page = url_for('main.index')

        return redirect(next_page)

    return render_template('auth/login.html')

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """User registration."""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))

    if request.method == 'POST':
        email = request.form.get('email', '').strip().lower()
        password = request.form.get('password', '')
        confirm_password = request.form.get('confirm_password', '')
        first_name = request.form.get('first_name', '').strip()
        last_name = request.form.get('last_name', '').strip()
        phone = request.form.get('phone', '').strip()
        user_type = request.form.get('user_type', 'shopper')

        # Validation
        errors = []

        if not all([email, password, confirm_password, first_name, last_name]):
            errors.append('Please fill in all required fields.')

        if not is_valid_email(email):
            errors.append('Please enter a valid email address.')

        if password != confirm_password:
            errors.append('Passwords do not match.')

        is_valid_pwd, pwd_msg = is_valid_password(password)
        if not is_valid_pwd:
            errors.append(pwd_msg)

        if len(first_name) < 2 or len(last_name) < 2:
            errors.append('First name and last name must be at least 2 characters long.')

        if phone and len(phone) < 10:
            errors.append('Please enter a valid phone number.')

        # Check if email already exists
        if User.query.filter_by(email=email).first():
            errors.append('An account with this email already exists.')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('auth/register.html')

        # Create user
        role = UserRole.VENDOR if user_type == 'vendor' else UserRole.SHOPPER
        user = User(
            email=email,
            first_name=first_name,
            last_name=last_name,
            phone=phone,
            role=role,
            tier=UserTier.FREE,
            credit_balance=0,  # Users start with 0 credits - must purchase credits
            is_active=True,
            email_verified=False  # In production, implement email verification
        )
        user.set_password(password)

        try:
            db.session.add(user)
            db.session.commit()

            # Auto-login the user
            login_user(user)

            flash(f'Welcome to AfroMall, {user.get_full_name()}! Your account has been created successfully.', 'success')

            if role == UserRole.VENDOR:
                flash('You can now create your first shop!', 'info')
                return redirect(url_for('shop.create'))
            else:
                return redirect(url_for('main.index'))

        except Exception as e:
            db.session.rollback()
            flash('An error occurred while creating your account. Please try again.', 'error')
            return render_template('auth/register.html')

    return render_template('auth/register.html')

@auth_bp.route('/logout')
@login_required
def logout():
    """User logout."""
    logout_user()
    flash('You have been logged out successfully.', 'info')
    return redirect(url_for('main.index'))

@auth_bp.route('/profile')
@login_required
def profile():
    """User profile page."""
    return render_template('auth/profile.html')

@auth_bp.route('/profile/edit', methods=['GET', 'POST'])
@login_required
def edit_profile():
    """Edit user profile."""
    if request.method == 'POST':
        first_name = request.form.get('first_name', '').strip()
        last_name = request.form.get('last_name', '').strip()
        phone = request.form.get('phone', '').strip()

        # Validation
        errors = []

        if not first_name or not last_name:
            errors.append('First name and last name are required.')

        if len(first_name) < 2 or len(last_name) < 2:
            errors.append('First name and last name must be at least 2 characters long.')

        if phone and len(phone) < 10:
            errors.append('Please enter a valid phone number.')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('auth/edit_profile.html')

        # Update user
        try:
            current_user.first_name = first_name
            current_user.last_name = last_name
            current_user.phone = phone
            db.session.commit()

            flash('Your profile has been updated successfully.', 'success')
            return redirect(url_for('auth.profile'))

        except Exception as e:
            db.session.rollback()
            flash('An error occurred while updating your profile. Please try again.', 'error')

    return render_template('auth/edit_profile.html')

@auth_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change user password."""
    if request.method == 'POST':
        current_password = request.form.get('current_password', '')
        new_password = request.form.get('new_password', '')
        confirm_password = request.form.get('confirm_password', '')

        # Validation
        errors = []

        if not all([current_password, new_password, confirm_password]):
            errors.append('Please fill in all fields.')

        if not current_user.check_password(current_password):
            errors.append('Current password is incorrect.')

        if new_password != confirm_password:
            errors.append('New passwords do not match.')

        is_valid_pwd, pwd_msg = is_valid_password(new_password)
        if not is_valid_pwd:
            errors.append(pwd_msg)

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('auth/change_password.html')

        # Update password
        try:
            current_user.set_password(new_password)
            db.session.commit()

            flash('Your password has been changed successfully.', 'success')
            return redirect(url_for('auth.profile'))

        except Exception as e:
            db.session.rollback()
            flash('An error occurred while changing your password. Please try again.', 'error')

    return render_template('auth/change_password.html')
