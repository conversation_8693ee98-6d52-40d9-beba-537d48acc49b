from flask import Blueprint, render_template, request, jsonify, redirect, url_for, abort
from flask_login import current_user
from models import (db, Shop, Product, Category, Review, ShopStatus, ProductStatus, ShortUrl,
                   ProductBoost, Advertisement, AdStatus)
from sqlalchemy import func, desc
from config import Config
from datetime import datetime

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """Homepage with featured shops and products."""
    # Cleanup expired boosts (simple cron-like functionality)
    try:
        ProductBoost.cleanup_expired_boosts()
        db.session.commit()
    except Exception:
        pass  # Silently fail to not affect user experience

    # Get featured shops (active shops with good ratings)
    featured_shops = db.session.query(Shop).filter(
        Shop.status == ShopStatus.ACTIVE
    ).order_by(desc(Shop.created_at)).limit(8).all()

    # Get new shops
    new_shops = db.session.query(Shop).filter(
        Shop.status == ShopStatus.ACTIVE
    ).order_by(desc(Shop.created_at)).limit(6).all()

    # Get featured products
    featured_products = db.session.query(Product).filter(
        Product.status == ProductStatus.ACTIVE,
        Product.featured == True
    ).order_by(desc(Product.created_at)).limit(12).all()

    # Get popular products (based on reviews)
    popular_products = db.session.query(Product).join(Review).filter(
        Product.status == ProductStatus.ACTIVE
    ).group_by(Product.id).order_by(
        desc(func.avg(Review.rating))
    ).limit(8).all()

    # Get categories
    categories = Category.query.filter_by(is_active=True, parent_id=None).all()

    return render_template('index.html',
                         featured_shops=featured_shops,
                         new_shops=new_shops,
                         featured_products=featured_products,
                         popular_products=popular_products,
                         categories=categories)

@main_bp.route('/search')
def search():
    """Global search for products and shops."""
    query = request.args.get('q', '').strip()
    category_id = request.args.get('category', type=int)
    min_price = request.args.get('min_price', type=float)
    max_price = request.args.get('max_price', type=float)
    sort_by = request.args.get('sort', 'relevance')
    page = request.args.get('page', 1, type=int)

    # Build product search query
    products_query = db.session.query(Product).filter(
        Product.status == ProductStatus.ACTIVE
    )

    if query:
        products_query = products_query.filter(
            Product.name.contains(query) |
            Product.description.contains(query) |
            Product.short_description.contains(query)
        )

    if category_id:
        products_query = products_query.filter(Product.category_id == category_id)

    if min_price:
        products_query = products_query.filter(Product.price >= min_price)

    if max_price:
        products_query = products_query.filter(Product.price <= max_price)

    # Apply sorting
    if sort_by == 'price_low':
        products_query = products_query.order_by(Product.price.asc())
    elif sort_by == 'price_high':
        products_query = products_query.order_by(Product.price.desc())
    elif sort_by == 'newest':
        products_query = products_query.order_by(Product.created_at.desc())
    elif sort_by == 'rating':
        # Join with reviews and order by average rating
        products_query = products_query.outerjoin(Review).group_by(Product.id).order_by(
            desc(func.avg(Review.rating))
        )
    else:  # relevance (default)
        products_query = products_query.order_by(Product.created_at.desc())

    # Paginate results
    products = products_query.paginate(
        page=page,
        per_page=Config.PRODUCTS_PER_PAGE,
        error_out=False
    )

    # Build shop search query
    shops_query = db.session.query(Shop).filter(
        Shop.status == ShopStatus.ACTIVE
    )

    if query:
        shops_query = shops_query.filter(
            Shop.name.contains(query) |
            Shop.description.contains(query)
        )

    shops = shops_query.order_by(Shop.created_at.desc()).limit(6).all()

    # Get categories for filter
    categories = Category.query.filter_by(is_active=True, parent_id=None).all()

    return render_template('search.html',
                         products=products,
                         shops=shops,
                         categories=categories,
                         query=query,
                         category_id=category_id,
                         min_price=min_price,
                         max_price=max_price,
                         sort_by=sort_by)

@main_bp.route('/categories')
def categories():
    """Display all categories."""
    categories = Category.query.filter_by(is_active=True, parent_id=None).all()
    return render_template('categories.html', categories=categories)

@main_bp.route('/category/<slug>')
def category_products(slug):
    """Display products in a specific category."""
    category = Category.query.filter_by(slug=slug, is_active=True).first_or_404()
    page = request.args.get('page', 1, type=int)
    sort_by = request.args.get('sort', 'newest')

    # Get products in this category
    products_query = db.session.query(Product).filter(
        Product.category_id == category.id,
        Product.status == ProductStatus.ACTIVE
    )

    # Apply sorting with boosted products priority
    if sort_by == 'price_low':
        products_query = products_query.outerjoin(ProductBoost,
            db.and_(
                ProductBoost.product_id == Product.id,
                ProductBoost.is_active == True,
                ProductBoost.expires_at > datetime.utcnow()
            )
        ).order_by(
            ProductBoost.id.desc().nullslast(),  # Boosted products first
            Product.price.asc()
        )
    elif sort_by == 'price_high':
        products_query = products_query.outerjoin(ProductBoost,
            db.and_(
                ProductBoost.product_id == Product.id,
                ProductBoost.is_active == True,
                ProductBoost.expires_at > datetime.utcnow()
            )
        ).order_by(
            ProductBoost.id.desc().nullslast(),  # Boosted products first
            Product.price.desc()
        )
    elif sort_by == 'rating':
        products_query = products_query.outerjoin(Review).outerjoin(ProductBoost,
            db.and_(
                ProductBoost.product_id == Product.id,
                ProductBoost.is_active == True,
                ProductBoost.expires_at > datetime.utcnow()
            )
        ).group_by(Product.id).order_by(
            ProductBoost.id.desc().nullslast(),  # Boosted products first
            desc(func.avg(Review.rating))
        )
    else:  # newest (default)
        products_query = products_query.outerjoin(ProductBoost,
            db.and_(
                ProductBoost.product_id == Product.id,
                ProductBoost.is_active == True,
                ProductBoost.expires_at > datetime.utcnow()
            )
        ).order_by(
            ProductBoost.id.desc().nullslast(),  # Boosted products first
            Product.created_at.desc()
        )

    products = products_query.paginate(
        page=page,
        per_page=Config.PRODUCTS_PER_PAGE,
        error_out=False
    )

    return render_template('category_products.html',
                         category=category,
                         products=products,
                         sort_by=sort_by)

@main_bp.route('/about')
def about():
    """About page."""
    return render_template('about.html')

@main_bp.route('/contact')
def contact():
    """Contact page."""
    return render_template('contact.html')

@main_bp.route('/privacy')
def privacy():
    """Privacy policy page."""
    return render_template('privacy.html')

@main_bp.route('/terms')
def terms():
    """Terms of service page."""
    return render_template('terms.html')

@main_bp.route('/pricing')
def pricing():
    """Pricing page."""
    return render_template('pricing.html')

@main_bp.route('/help')
def help():
    """Help center/FAQ page."""
    return render_template('help/index.html')

@main_bp.route('/aide')
def help_center():
    """Help center page (French alias)."""
    return render_template('help/index.html')

@main_bp.route('/guide-visuel')
def visual_guide():
    """Visual guide page."""
    return render_template('help/visual_guide.html')

@main_bp.route('/payment/premium')
def payment_premium():
    """Premium payment page."""
    return render_template('payment/premium.html')

@main_bp.route('/payment/gold')
def payment_gold():
    """Gold payment page."""
    return render_template('payment/gold.html')

@main_bp.route('/s/<short_code>')
def short_url_redirect(short_code):
    """Redirect short URL to original URL."""
    short_url = ShortUrl.query.filter_by(short_code=short_code).first_or_404()

    # Update click count and last accessed time
    short_url.clicks += 1
    short_url.last_accessed = datetime.utcnow()
    db.session.commit()

    # If it's a shop URL, redirect to the shop page directly
    if short_url.shop_id:
        shop = Shop.query.get(short_url.shop_id)
        if shop and shop.status == ShopStatus.ACTIVE:
            return redirect(url_for('shop.view', slug=shop.slug))
        else:
            abort(404)

    # For other URLs, redirect to the original URL
    return redirect(short_url.original_url)
