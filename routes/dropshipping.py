"""
Dropshipping routes for AliExpress integration
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models import db, Product, Shop, Category, UserTier, ProductStatus
from services.aliexpress_service import AliExpressService
from services.ebay_service import EbayService
from utils.helpers import generate_slug
from utils.decorators import vendor_required
import logging

logger = logging.getLogger(__name__)

dropshipping_bp = Blueprint('dropshipping', __name__)

@dropshipping_bp.route('/dashboard')
@login_required
@vendor_required
def dashboard():
    """Dropshipping dashboard for vendors"""
    # Check if user has premium or gold tier for dropshipping
    if current_user.tier not in [UserTier.PREMIUM, UserTier.GOLD]:
        flash('Le dropshipping est disponible uniquement pour les utilisateurs Premium et Gold.', 'warning')
        return redirect(url_for('main.pricing'))

    # Get user's shops
    user_shops = Shop.query.filter_by(owner_id=current_user.id).all()

    if not user_shops:
        flash('Vous devez créer une boutique avant d\'utiliser le dropshipping.', 'warning')
        return redirect(url_for('shop.create'))

    return render_template('dropshipping/dashboard.html', shops=user_shops)

@dropshipping_bp.route('/browse')
@login_required
@vendor_required
def browse_products():
    """Browse AliExpress products for dropshipping"""
    logger.info(f"🔐 User {current_user.email} accessing browse (tier: {current_user.tier})")

    if current_user.tier not in [UserTier.PREMIUM, UserTier.GOLD]:
        logger.warning(f"❌ Access denied for user {current_user.email} (tier: {current_user.tier})")
        flash('Le dropshipping est disponible uniquement pour les utilisateurs Premium et Gold.', 'warning')
        return redirect(url_for('main.pricing'))

    # Get search parameters
    keywords = request.args.get('keywords', '')
    page = request.args.get('page', 1, type=int)
    category = request.args.get('category', '')

    # Initialize AliExpress service
    aliexpress_service = AliExpressService()

    products = []
    promos = []

    try:
        # Always use the new Free AliExpress API search_products method
        # It will get hot products from category 15 (electronics) by default
        search_keywords = keywords if keywords else "electronics"  # Default search

        print(f"🔍 Route: Searching for products with keywords: {search_keywords}")
        products = aliexpress_service.search_products(
            keywords=search_keywords,
            page_no=page,
            page_size=20,
            target_currency="USD",
            target_language="EN"
        ) or []
        print(f"📦 Route: Search returned {len(products)} products")

    except Exception as e:
        logger.error(f"Error browsing AliExpress products: {str(e)}")
        print(f"❌ Route error: {str(e)}")
        flash('Erreur lors de la récupération des produits. Veuillez réessayer.', 'error')

    # Format products for display (add image processing)
    formatted_products = []
    if products:
        logger.info(f"🔧 Processing {len(products)} products for display...")

        for i, product in enumerate(products):
            logger.info(f"📦 Processing product {i+1}: {product.get('product_title', 'Unknown')[:50]}...")

            # Log raw product data for first product to debug
            if i == 0:
                logger.info(f"🔍 Raw product data keys: {list(product.keys())}")
                image_fields = [k for k in product.keys() if 'image' in k.lower()]
                logger.info(f"🖼️ Image fields found: {image_fields}")
                for field in image_fields:
                    logger.info(f"  {field}: {product.get(field)}")

            # Process the product to ensure images are properly formatted
            formatted_product = aliexpress_service.format_product_for_import(product)
            if formatted_product:
                # Merge formatted data back into original product for template compatibility
                product['formatted_images'] = formatted_product.get('images', [])
                product['formatted_name'] = formatted_product.get('name', product.get('product_title', ''))
                product['formatted_price'] = formatted_product.get('price', 0)
                logger.info(f"✅ Product {i+1} formatted successfully with {len(formatted_product.get('images', []))} images")
            else:
                logger.warning(f"❌ Failed to format product {i+1}")
                product['formatted_images'] = ['/static/images/no-image.svg']

            formatted_products.append(product)

        logger.info(f"🎯 Final result: {len(formatted_products)} formatted products")

    # Get user's shops for the import form
    user_shops = Shop.query.filter_by(owner_id=current_user.id).all()

    return render_template('dropshipping/browse.html',
                         products=formatted_products,
                         promos=promos,
                         shops=user_shops,
                         keywords=keywords,
                         current_page=page)

@dropshipping_bp.route('/import-product', methods=['POST'])
@login_required
@vendor_required
def import_product():
    """Import a product from AliExpress"""
    if current_user.tier not in [UserTier.PREMIUM, UserTier.GOLD]:
        return jsonify({'success': False, 'message': 'Dropshipping disponible uniquement pour Premium/Gold'})

    try:
        # Get form data (now expecting JSON with product data)
        data = request.get_json() if request.is_json else request.form
        logger.info(f"🔧 Import request data: {data}")

        aliexpress_id = data.get('aliexpress_id')
        shop_id = data.get('shop_id')
        if shop_id:
            shop_id = int(shop_id)
        custom_name = data.get('custom_name', '')
        custom_price = data.get('custom_price')
        if custom_price:
            custom_price = float(custom_price)
        markup_percentage = data.get('markup_percentage', 50)
        if markup_percentage:
            markup_percentage = float(markup_percentage)
        product_data = data.get('product_data', {})

        logger.info(f"📦 Processing import: ID={aliexpress_id}, Shop={shop_id}, Name={custom_name}")

        if not aliexpress_id or not shop_id:
            return jsonify({'success': False, 'message': 'Données manquantes'})

        # Verify shop ownership
        shop = Shop.query.filter_by(id=shop_id, owner_id=current_user.id).first()
        if not shop:
            return jsonify({'success': False, 'message': 'Boutique non trouvée'})

        # Check if shop can add more products
        if not shop.can_add_product():
            return jsonify({'success': False, 'message': 'Limite de produits atteinte pour votre plan'})

        # Use product data from frontend instead of fetching details
        aliexpress_service = AliExpressService()

        # If we have product_data from frontend, use it; otherwise try to format from basic info
        if product_data:
            formatted_product = product_data
        else:
            # Fallback: create basic product info from what we have
            formatted_product = {
                'id': aliexpress_id,
                'name': custom_name or f'Produit AliExpress {aliexpress_id}',
                'price': custom_price or 10.0,
                'images': ['/static/images/no-image.svg'],
                'description': f'Produit importé depuis AliExpress (ID: {aliexpress_id})'
            }

        if not formatted_product:
            return jsonify({'success': False, 'message': 'Erreur lors du formatage du produit'})

        # Calculate final price
        original_price = formatted_product.get('price', 0)
        if custom_price:
            final_price = custom_price
        else:
            final_price = original_price * (1 + markup_percentage / 100)

        # Convert to EUR (assuming AliExpress prices are in USD)
        final_price_eur = aliexpress_service.convert_currency(final_price, "USD", "EUR")

        # Use custom name or original name
        product_name = custom_name if custom_name else formatted_product.get('name', '')

        # Generate slug
        slug = generate_slug(product_name)

        # Check if slug exists and make it unique
        existing_product = Product.query.filter_by(slug=slug).first()
        if existing_product:
            slug = f"{slug}-{aliexpress_id}"

        # Get default category (you might want to add category mapping)
        default_category = Category.query.first()

        # Create new product
        new_product = Product(
            shop_id=shop.id,
            category_id=default_category.id if default_category else None,
            name=product_name,
            slug=slug,
            description=f"Produit importé depuis AliExpress.\n\nID AliExpress: {aliexpress_id}\nPrix original: ${original_price}\nMarge appliquée: {markup_percentage}%",
            price=final_price_eur,
            images=formatted_product.get('images', []),
            stock_quantity=100,  # Default stock for dropshipping
            status=ProductStatus.ACTIVE,
            featured=False,
            digital=False
        )

        db.session.add(new_product)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Produit importé avec succès!',
            'product_id': new_product.id,
            'product_url': url_for('product.view', slug=new_product.slug)
        })

    except Exception as e:
        logger.error(f"Error importing product: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Erreur lors de l\'importation du produit'})

@dropshipping_bp.route('/api/search')
@login_required
@vendor_required
def api_search():
    """API endpoint for searching AliExpress products"""
    if current_user.tier not in [UserTier.PREMIUM, UserTier.GOLD]:
        return jsonify({'success': False, 'message': 'Accès non autorisé'})

    keywords = request.args.get('keywords', '')
    page = request.args.get('page', 1, type=int)

    if not keywords:
        return jsonify({'success': False, 'message': 'Mots-clés requis'})

    try:
        aliexpress_service = AliExpressService()
        products = aliexpress_service.search_products(
            keywords=keywords,
            page_no=page,
            page_size=20
        ) or []

        # Format products for frontend
        formatted_products = []
        for product in products:
            formatted = aliexpress_service.format_product_for_import(product)
            if formatted:
                formatted_products.append(formatted)

        return jsonify({
            'success': True,
            'products': formatted_products,
            'page': page,
            'has_more': len(products) == 20  # Assume more if we got full page
        })

    except Exception as e:
        logger.error(f"Error in API search: {str(e)}")
        return jsonify({'success': False, 'message': 'Erreur lors de la recherche'})

@dropshipping_bp.route('/api/promos')
@login_required
@vendor_required
def api_promos():
    """API endpoint for getting featured promos"""
    if current_user.tier not in [UserTier.PREMIUM, UserTier.GOLD]:
        return jsonify({'success': False, 'message': 'Accès non autorisé'})

    try:
        aliexpress_service = AliExpressService()
        promos = aliexpress_service.get_featured_promos() or []

        return jsonify({
            'success': True,
            'promos': promos
        })

    except Exception as e:
        logger.error(f"Error getting promos: {str(e)}")
        return jsonify({'success': False, 'message': 'Erreur lors de la récupération des promotions'})

@dropshipping_bp.route('/api/promo-products/<promo_name>')
@login_required
@vendor_required
def api_promo_products(promo_name):
    """API endpoint for getting products from a specific promo"""
    if current_user.tier not in [UserTier.PREMIUM, UserTier.GOLD]:
        return jsonify({'success': False, 'message': 'Accès non autorisé'})

    page = request.args.get('page', 1, type=int)

    try:
        aliexpress_service = AliExpressService()
        products = aliexpress_service.get_promo_products(
            promo_name=promo_name,
            page_no=page,
            page_size=20
        ) or []

        # Format products for frontend
        formatted_products = []
        for product in products:
            formatted = aliexpress_service.format_product_for_import(product)
            if formatted:
                formatted_products.append(formatted)

        return jsonify({
            'success': True,
            'products': formatted_products,
            'promo_name': promo_name,
            'page': page,
            'has_more': len(products) == 20
        })

    except Exception as e:
        logger.error(f"Error getting promo products: {str(e)}")
        return jsonify({'success': False, 'message': 'Erreur lors de la récupération des produits'})

@dropshipping_bp.route('/browse-ebay')
@login_required
@vendor_required
def browse_ebay():
    """Browse eBay products for dropshipping"""
    if current_user.tier not in [UserTier.PREMIUM, UserTier.GOLD]:
        flash('Le dropshipping est disponible uniquement pour les utilisateurs Premium et Gold.', 'warning')
        return redirect(url_for('main.pricing'))

    # Get search parameters
    keywords = request.args.get('keywords', '')
    page = request.args.get('page', 1, type=int)
    category = request.args.get('category', '')

    # Initialize eBay service
    ebay_service = EbayService()

    products = []
    categories = ebay_service.get_product_categories()
    trending_searches = ebay_service.get_trending_searches()

    try:
        if keywords:
            # Search for specific products
            products = ebay_service.search_products(
                keywords=keywords,
                page=page,
                limit=20
            ) or []
        else:
            # Get products for trending searches
            if trending_searches:
                default_search = trending_searches[0]
                products = ebay_service.search_products(
                    keywords=default_search,
                    page=page,
                    limit=20
                ) or []

    except Exception as e:
        logger.error(f"Error browsing eBay products: {str(e)}")
        flash('Erreur lors de la récupération des produits eBay. Veuillez réessayer.', 'error')

    # Get user's shops for the import form
    user_shops = Shop.query.filter_by(owner_id=current_user.id).all()

    return render_template('dropshipping/browse_ebay.html',
                         products=products,
                         categories=categories,
                         trending_searches=trending_searches,
                         shops=user_shops,
                         keywords=keywords,
                         current_page=page)

@dropshipping_bp.route('/import-ebay-product', methods=['POST'])
@login_required
@vendor_required
def import_ebay_product():
    """Import a product from eBay"""
    if current_user.tier not in [UserTier.PREMIUM, UserTier.GOLD]:
        return jsonify({'success': False, 'message': 'Dropshipping disponible uniquement pour Premium/Gold'})

    try:
        # Get form data
        ebay_data = request.get_json()
        logger.info(f"Received eBay import data: {ebay_data}")

        shop_id = ebay_data.get('shop_id')
        if shop_id:
            shop_id = int(shop_id)
        custom_name = ebay_data.get('custom_name') or ''
        custom_price = ebay_data.get('custom_price')
        if custom_price:
            custom_price = float(custom_price)
        markup_percentage = ebay_data.get('markup_percentage') or 50
        if markup_percentage:
            markup_percentage = float(markup_percentage)
        product_data = ebay_data.get('product_data') or {}

        logger.info(f"Parsed data - shop_id: {shop_id}, product_data: {product_data}")

        if not product_data or not shop_id:
            return jsonify({'success': False, 'message': 'Données manquantes'})

        # Verify shop ownership
        shop = Shop.query.filter_by(id=shop_id, owner_id=current_user.id).first()
        if not shop:
            return jsonify({'success': False, 'message': 'Boutique non trouvée'})

        # Check if shop can add more products
        if not shop.can_add_product():
            return jsonify({'success': False, 'message': 'Limite de produits atteinte pour votre plan'})

        # Format product for import
        ebay_service = EbayService()
        formatted_product = ebay_service.format_product_for_import(product_data)

        if not formatted_product:
            return jsonify({'success': False, 'message': 'Erreur lors du formatage du produit'})

        # Calculate final price
        original_price = formatted_product.get('price', 0)
        if custom_price:
            final_price = custom_price
        else:
            final_price = original_price * (1 + markup_percentage / 100)

        # Convert to EUR (assuming eBay prices are in USD)
        final_price_eur = ebay_service.convert_currency(final_price, "USD", "EUR")

        # Use custom name or original name
        product_name = custom_name if custom_name else formatted_product.get('name', '')

        # Generate slug
        slug = generate_slug(product_name)

        # Check if slug exists and make it unique
        existing_product = Product.query.filter_by(slug=slug).first()
        if existing_product:
            slug = f"{slug}-{formatted_product.get('ebay_id', 'ebay')}"

        # Get default category
        default_category = Category.query.first()

        # Create new product
        new_product = Product(
            shop_id=shop.id,
            category_id=default_category.id if default_category else None,
            name=product_name,
            slug=slug,
            description=formatted_product.get('description', ''),
            price=final_price_eur,
            images=formatted_product.get('images', []),
            stock_quantity=100,  # Default stock for dropshipping
            status=ProductStatus.ACTIVE,
            featured=False,
            digital=False
        )

        db.session.add(new_product)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Produit eBay importé avec succès!',
            'product_id': new_product.id,
            'product_url': url_for('product.view', slug=new_product.slug)
        })

    except Exception as e:
        logger.error(f"Error importing eBay product: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Erreur lors de l\'importation du produit eBay'})

@dropshipping_bp.route('/api/ebay-search')
@login_required
@vendor_required
def api_ebay_search():
    """API endpoint for searching eBay products"""
    if current_user.tier not in [UserTier.PREMIUM, UserTier.GOLD]:
        return jsonify({'success': False, 'message': 'Accès non autorisé'})

    keywords = request.args.get('keywords', '')
    page = request.args.get('page', 1, type=int)

    if not keywords:
        return jsonify({'success': False, 'message': 'Mots-clés requis'})

    try:
        ebay_service = EbayService()
        products = ebay_service.search_products(
            keywords=keywords,
            page=page,
            limit=20
        ) or []

        # Format products for frontend
        formatted_products = []
        for product in products:
            formatted = ebay_service.format_product_for_import(product)
            if formatted:
                # Add validation info
                validation = ebay_service.validate_product_for_dropshipping(formatted)
                formatted['validation'] = validation
                formatted_products.append(formatted)

        return jsonify({
            'success': True,
            'products': formatted_products,
            'page': page,
            'has_more': len(products) == 20  # Assume more if we got full page
        })

    except Exception as e:
        logger.error(f"Error in eBay API search: {str(e)}")
        return jsonify({'success': False, 'message': 'Erreur lors de la recherche eBay'})

@dropshipping_bp.route('/test-api')
@login_required
@vendor_required
def test_api():
    """Test AliExpress and eBay API connections"""
    if current_user.tier not in [UserTier.PREMIUM, UserTier.GOLD]:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('main.index'))

    # Test AliExpress API
    try:
        aliexpress_service = AliExpressService()
        promos = aliexpress_service.get_featured_promos()

        if promos:
            flash(f'✅ API AliExpress fonctionne! Trouvé {len(promos)} promotions.', 'success')
        else:
            flash('⚠️ API AliExpress connectée mais aucune promotion trouvée.', 'warning')

    except Exception as e:
        flash(f'❌ Erreur API AliExpress: {str(e)}', 'error')

    # Test eBay API
    try:
        ebay_service = EbayService()
        products = ebay_service.search_products("phone case", page=1, limit=5)

        if products:
            flash(f'✅ API eBay fonctionne! Trouvé {len(products)} produits de test.', 'success')
        else:
            flash('⚠️ API eBay connectée mais aucun produit trouvé.', 'warning')

    except Exception as e:
        flash(f'❌ Erreur API eBay: {str(e)}', 'error')

    return redirect(url_for('dropshipping.dashboard'))
