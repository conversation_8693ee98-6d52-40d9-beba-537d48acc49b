from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import db, Product, Shop, Category, Review, ProductStatus, ShopStatus
from utils.decorators import vendor_required
from utils.helpers import generate_slug, allowed_file, save_uploaded_file, get_file_url
from services.ai_service import AIService
from config import Config
import os

product_bp = Blueprint('product', __name__)

@product_bp.route('/<slug>')
def view(slug):
    """View a specific product."""
    product = Product.query.filter_by(slug=slug, status=ProductStatus.ACTIVE).first_or_404()

    # Get related products from the same shop
    related_products = Product.query.filter(
        Product.shop_id == product.shop_id,
        Product.id != product.id,
        Product.status == ProductStatus.ACTIVE
    ).limit(4).all()

    # Get product reviews
    reviews = Review.query.filter_by(
        product_id=product.id,
        is_approved=True
    ).order_by(Review.created_at.desc()).limit(10).all()

    # Check if current user has purchased this product (for review eligibility)
    can_review = False
    if current_user.is_authenticated:
        from models import Order, OrderItem, OrderStatus
        user_purchased = db.session.query(Order).join(OrderItem).filter(
            Order.user_id == current_user.id,
            OrderItem.product_id == product.id,
            Order.status.in_([OrderStatus.DELIVERED, OrderStatus.SHIPPED])
        ).first()

        # Check if user hasn't already reviewed this product
        existing_review = Review.query.filter_by(
            user_id=current_user.id,
            product_id=product.id
        ).first()

        can_review = user_purchased and not existing_review

    return render_template('product/view.html',
                         product=product,
                         related_products=related_products,
                         reviews=reviews,
                         can_review=can_review)

@product_bp.route('/create/<int:shop_id>', methods=['GET', 'POST'])
@login_required
@vendor_required
def create(shop_id):
    """Create a new product."""
    shop = Shop.query.filter_by(id=shop_id, owner_id=current_user.id).first_or_404()

    # Check if shop can add more products
    if not shop.can_add_product():
        flash('Vous avez atteint le nombre maximum de produits pour votre niveau. Veuillez passer à un niveau supérieur pour ajouter plus de produits.', 'error')
        return redirect(url_for('shop.manage_products', shop_id=shop.id))

    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()
        short_description = request.form.get('short_description', '').strip()
        price = request.form.get('price', type=float)
        sale_price = request.form.get('sale_price', type=float)
        category_id = request.form.get('category_id', type=int)
        sku = request.form.get('sku', '').strip()
        stock_quantity = request.form.get('stock_quantity', type=int)
        weight = request.form.get('weight', type=float)
        digital = bool(request.form.get('digital'))

        # Validation
        errors = []

        if not name:
            errors.append('Product name is required.')
        elif len(name) < 3:
            errors.append('Product name must be at least 3 characters long.')

        if not description:
            errors.append('Product description is required.')
        elif len(description) < 20:
            errors.append('Product description must be at least 20 characters long.')

        if not price or price <= 0:
            errors.append('Please enter a valid price.')

        if sale_price and sale_price >= price:
            errors.append('Sale price must be less than regular price.')

        if not category_id:
            errors.append('Please select a category.')

        if stock_quantity is None or stock_quantity < 0:
            errors.append('Please enter a valid stock quantity.')

        # Generate unique slug
        slug = generate_slug(f"{name}-{shop.name}")
        counter = 1
        original_slug = slug
        while Product.query.filter_by(slug=slug).first():
            slug = f"{original_slug}-{counter}"
            counter += 1

        if errors:
            for error in errors:
                flash(error, 'error')
            categories = Category.query.filter_by(is_active=True).all()
            return render_template('product/create.html', shop=shop, categories=categories)

        # Handle image uploads
        images = []
        for i in range(5):  # Allow up to 5 images
            file_key = f'image_{i}'
            if file_key in request.files:
                image_file = request.files[file_key]
                if image_file and allowed_file(image_file.filename):
                    filename = save_uploaded_file(image_file, 'products')
                    if filename:
                        images.append(get_file_url(filename, 'products'))

        # Create product
        try:
            product = Product(
                shop_id=shop.id,
                category_id=category_id,
                name=name,
                slug=slug,
                description=description,
                short_description=short_description,
                price=price,
                sale_price=sale_price,
                sku=sku,
                stock_quantity=stock_quantity,
                weight=weight,
                digital=digital,
                images=images,
                status=ProductStatus.PENDING
            )

            db.session.add(product)
            db.session.commit()

            flash('Your product has been created and is pending approval. You will be notified once it\'s approved.', 'success')
            return redirect(url_for('shop.manage_products', shop_id=shop.id))

        except Exception as e:
            db.session.rollback()
            flash('An error occurred while creating your product. Please try again.', 'error')

    categories = Category.query.filter_by(is_active=True).all()
    return render_template('product/create.html', shop=shop, categories=categories)

@product_bp.route('/<int:product_id>/edit', methods=['GET', 'POST'])
@login_required
@vendor_required
def edit(product_id):
    """Edit a product."""
    product = Product.query.join(Shop).filter(
        Product.id == product_id,
        Shop.owner_id == current_user.id
    ).first_or_404()

    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()
        short_description = request.form.get('short_description', '').strip()
        price = request.form.get('price', type=float)
        sale_price = request.form.get('sale_price', type=float)
        category_id = request.form.get('category_id', type=int)
        sku = request.form.get('sku', '').strip()
        stock_quantity = request.form.get('stock_quantity', type=int)
        weight = request.form.get('weight', type=float)
        digital = bool(request.form.get('digital'))

        # Validation
        errors = []

        if not name:
            errors.append('Product name is required.')
        elif len(name) < 3:
            errors.append('Product name must be at least 3 characters long.')

        if not description:
            errors.append('Product description is required.')
        elif len(description) < 20:
            errors.append('Product description must be at least 20 characters long.')

        if not price or price <= 0:
            errors.append('Please enter a valid price.')

        if sale_price and sale_price >= price:
            errors.append('Sale price must be less than regular price.')

        if not category_id:
            errors.append('Please select a category.')

        if stock_quantity is None or stock_quantity < 0:
            errors.append('Please enter a valid stock quantity.')

        # Check if name changed and generate new slug
        if name != product.name:
            slug = generate_slug(f"{name}-{product.shop.name}")
            counter = 1
            original_slug = slug
            while Product.query.filter(Product.slug == slug, Product.id != product.id).first():
                slug = f"{original_slug}-{counter}"
                counter += 1
        else:
            slug = product.slug

        if errors:
            for error in errors:
                flash(error, 'error')
            categories = Category.query.filter_by(is_active=True).all()
            return render_template('product/edit.html', product=product, categories=categories)

        # Handle new image uploads
        new_images = list(product.images) if product.images else []

        for i in range(5):  # Allow up to 5 images
            file_key = f'image_{i}'
            if file_key in request.files:
                image_file = request.files[file_key]
                if image_file and allowed_file(image_file.filename):
                    filename = save_uploaded_file(image_file, 'products')
                    if filename:
                        new_images.append(get_file_url(filename, 'products'))

        # Remove selected images
        remove_images = request.form.getlist('remove_images')
        if remove_images:
            new_images = [img for img in new_images if img not in remove_images]
            # Delete removed image files from disk
            for img_url in remove_images:
                if img_url.startswith('/static/uploads/'):
                    img_path = img_url[1:]  # Remove leading slash
                    if os.path.exists(img_path):
                        os.remove(img_path)

        # Check if only images were changed (no approval needed for image changes)
        images_only_change = (
            name == product.name and
            description == product.description and
            short_description == product.short_description and
            price == product.price and
            sale_price == product.sale_price and
            category_id == product.category_id and
            sku == product.sku and
            stock_quantity == product.stock_quantity and
            weight == product.weight and
            digital == product.digital
        )

        # Update product
        try:
            product.name = name
            product.slug = slug
            product.description = description
            product.short_description = short_description
            product.price = price
            product.sale_price = sale_price
            product.category_id = category_id
            product.sku = sku
            product.stock_quantity = stock_quantity
            product.weight = weight
            product.digital = digital
            product.images = new_images

            # Only require re-approval if content changed, not just images
            if not images_only_change:
                product.status = ProductStatus.PENDING  # Require re-approval after content edit

            db.session.commit()

            if images_only_change:
                flash('Product images updated successfully.', 'success')
            else:
                flash('Your product has been updated and is pending approval.', 'success')
            return redirect(url_for('shop.manage_products', shop_id=product.shop_id))

        except Exception as e:
            db.session.rollback()
            flash('An error occurred while updating your product. Please try again.', 'error')

    categories = Category.query.filter_by(is_active=True).all()
    return render_template('product/edit.html', product=product, categories=categories)

@product_bp.route('/<int:product_id>/delete', methods=['POST'])
@login_required
@vendor_required
def delete(product_id):
    """Delete a product."""
    product = Product.query.join(Shop).filter(
        Product.id == product_id,
        Shop.owner_id == current_user.id
    ).first_or_404()

    try:
        shop_id = product.shop_id

        # Delete product images
        if product.images:
            for image_url in product.images:
                if image_url.startswith('/static/uploads/'):
                    image_path = image_url[1:]  # Remove leading slash
                    if os.path.exists(image_path):
                        os.remove(image_path)

        db.session.delete(product)
        db.session.commit()

        # Return JSON response for AJAX requests
        if request.is_json or request.headers.get('Content-Type') == 'application/json':
            return jsonify({'success': True, 'message': 'Product deleted successfully'})

        flash('Product has been deleted successfully.', 'success')
        return redirect(url_for('shop.manage_products', shop_id=shop_id))

    except Exception as e:
        db.session.rollback()

        # Return JSON error response for AJAX requests
        if request.is_json or request.headers.get('Content-Type') == 'application/json':
            return jsonify({'success': False, 'message': 'Error deleting product'})

        flash('An error occurred while deleting the product. Please try again.', 'error')
        return redirect(url_for('shop.manage_products', shop_id=product.shop_id))

@product_bp.route('/<int:product_id>/review', methods=['POST'])
@login_required
def add_review(product_id):
    """Add a product review."""
    product = Product.query.filter_by(id=product_id, status=ProductStatus.ACTIVE).first_or_404()

    # Check if user has purchased this product
    from models import Order, OrderItem, OrderStatus
    user_purchased = db.session.query(Order).join(OrderItem).filter(
        Order.user_id == current_user.id,
        OrderItem.product_id == product.id,
        Order.status.in_([OrderStatus.DELIVERED, OrderStatus.SHIPPED])
    ).first()

    if not user_purchased:
        flash('You can only review products you have purchased.', 'error')
        return redirect(url_for('product.view', slug=product.slug))

    # Check if user hasn't already reviewed this product
    existing_review = Review.query.filter_by(
        user_id=current_user.id,
        product_id=product.id
    ).first()

    if existing_review:
        flash('You have already reviewed this product.', 'error')
        return redirect(url_for('product.view', slug=product.slug))

    rating = request.form.get('rating', type=int)
    title = request.form.get('title', '').strip()
    comment = request.form.get('comment', '').strip()

    # Validation
    if not rating or rating < 1 or rating > 5:
        flash('Please select a rating between 1 and 5 stars.', 'error')
        return redirect(url_for('product.view', slug=product.slug))

    if not comment:
        flash('Please write a review comment.', 'error')
        return redirect(url_for('product.view', slug=product.slug))

    try:
        review = Review(
            user_id=current_user.id,
            product_id=product.id,
            rating=rating,
            title=title,
            comment=comment,
            is_verified_purchase=True
        )

        db.session.add(review)
        db.session.commit()

        flash('Your review has been submitted successfully.', 'success')

    except Exception as e:
        db.session.rollback()
        flash('An error occurred while submitting your review. Please try again.', 'error')

    return redirect(url_for('product.view', slug=product.slug))

@product_bp.route('/<int:product_id>/ai-rewrite', methods=['POST'])
@login_required
@vendor_required
def ai_rewrite(product_id):
    """AI rewrite product title and description for premium users."""
    product = Product.query.join(Shop).filter(
        Product.id == product_id,
        Shop.owner_id == current_user.id
    ).first_or_404()

    # Check if user has premium or gold tier
    if current_user.tier.value == 'free':
        return jsonify({
            'success': False,
            'message': 'Cette fonctionnalité est réservée aux utilisateurs Premium et Gold.'
        }), 403

    try:
        rewrite_type = request.json.get('type', 'both')  # 'title', 'description', or 'both'

        result = {}

        if rewrite_type in ['title', 'both']:
            # Rewrite title
            category_name = product.category.name if product.category else None
            new_title = AIService.rewrite_product_title(product.name, category_name)
            result['title'] = new_title

        if rewrite_type in ['description', 'both']:
            # Rewrite description
            category_name = product.category.name if product.category else None
            new_description = AIService.rewrite_product_description(
                product.description,
                product.name,
                category_name
            )
            result['description'] = new_description

        return jsonify({
            'success': True,
            'data': result
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erreur lors de la réécriture: {str(e)}'
        }), 500

@product_bp.route('/<int:product_id>/ai-tags', methods=['POST'])
@login_required
@vendor_required
def ai_generate_tags(product_id):
    """Generate AI tags for product."""
    product = Product.query.join(Shop).filter(
        Product.id == product_id,
        Shop.owner_id == current_user.id
    ).first_or_404()

    # Check if user has premium or gold tier
    if current_user.tier.value == 'free':
        return jsonify({
            'success': False,
            'message': 'Cette fonctionnalité est réservée aux utilisateurs Premium et Gold.'
        }), 403

    try:
        category_name = product.category.name if product.category else None
        tags = AIService.generate_product_tags(
            product.name,
            product.description,
            category_name
        )

        return jsonify({
            'success': True,
            'tags': tags
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erreur lors de la génération des tags: {str(e)}'
        }), 500