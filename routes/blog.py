from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import db, Blog<PERSON><PERSON>, User
from utils.decorators import admin_required
from utils.helpers import generate_slug
from services.ai_service import AIService
from datetime import datetime
import csv
import io
from werkzeug.utils import secure_filename

blog_bp = Blueprint('blog', __name__)

@blog_bp.route('/')
def index():
    """Blog homepage with published posts."""
    page = request.args.get('page', 1, type=int)
    category = request.args.get('category', '')
    
    query = BlogPost.query.filter_by(is_published=True)
    
    if category:
        query = query.filter_by(category=category)
    
    posts = query.order_by(BlogPost.published_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    
    # Get featured posts
    featured_posts = BlogPost.query.filter_by(
        is_published=True, is_featured=True
    ).order_by(BlogPost.published_at.desc()).limit(3).all()
    
    # Get categories
    categories = db.session.query(BlogPost.category).filter(
        BlogPost.is_published == True,
        BlogPost.category.isnot(None)
    ).distinct().all()
    categories = [cat[0] for cat in categories if cat[0]]
    
    return render_template('blog/index.html', 
                         posts=posts, 
                         featured_posts=featured_posts,
                         categories=categories,
                         current_category=category)

@blog_bp.route('/<slug>')
def view_post(slug):
    """View a single blog post."""
    post = BlogPost.query.filter_by(slug=slug, is_published=True).first_or_404()
    
    # Increment views
    post.views += 1
    db.session.commit()
    
    # Get related posts
    related_posts = BlogPost.query.filter(
        BlogPost.category == post.category,
        BlogPost.id != post.id,
        BlogPost.is_published == True
    ).order_by(BlogPost.published_at.desc()).limit(3).all()
    
    return render_template('blog/post.html', post=post, related_posts=related_posts)

@blog_bp.route('/admin')
@login_required
@admin_required
def admin_index():
    """Blog admin dashboard."""
    page = request.args.get('page', 1, type=int)
    
    posts = BlogPost.query.order_by(BlogPost.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    # Statistics
    total_posts = BlogPost.query.count()
    published_posts = BlogPost.query.filter_by(is_published=True).count()
    draft_posts = BlogPost.query.filter_by(is_published=False).count()
    total_views = db.session.query(db.func.sum(BlogPost.views)).scalar() or 0
    
    stats = {
        'total_posts': total_posts,
        'published_posts': published_posts,
        'draft_posts': draft_posts,
        'total_views': total_views
    }
    
    return render_template('blog/admin/index.html', posts=posts, stats=stats)

@blog_bp.route('/admin/create', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_create():
    """Create a new blog post."""
    if request.method == 'POST':
        title = request.form.get('title')
        content = request.form.get('content')
        excerpt = request.form.get('excerpt')
        category = request.form.get('category')
        tags = request.form.get('tags')
        is_published = request.form.get('is_published') == 'on'
        is_featured = request.form.get('is_featured') == 'on'
        
        if not title or not content:
            flash('Le titre et le contenu sont requis.', 'error')
            return render_template('blog/admin/create.html')
        
        # Generate slug
        slug = generate_slug(title)
        
        # Check if slug exists
        existing_post = BlogPost.query.filter_by(slug=slug).first()
        if existing_post:
            slug = f"{slug}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        post = BlogPost(
            title=title,
            slug=slug,
            content=content,
            excerpt=excerpt,
            category=category,
            tags=tags,
            author_id=current_user.id,
            is_published=is_published,
            is_featured=is_featured,
            published_at=datetime.utcnow() if is_published else None
        )
        
        db.session.add(post)
        db.session.commit()
        
        flash('Article créé avec succès!', 'success')
        return redirect(url_for('blog.admin_index'))
    
    return render_template('blog/admin/create.html')

@blog_bp.route('/admin/ai-generate', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_ai_generate():
    """Generate blog posts using AI."""
    if request.method == 'POST':
        topic = request.form.get('topic')
        category = request.form.get('category', 'general')
        auto_publish = request.form.get('auto_publish') == 'on'
        
        if not topic:
            flash('Le sujet est requis.', 'error')
            return render_template('blog/admin/ai_generate.html')
        
        try:
            # Generate blog post using AI
            ai_content = AIService.generate_blog_post(topic, category)
            
            # Generate slug
            slug = generate_slug(ai_content['title'])
            
            # Check if slug exists
            existing_post = BlogPost.query.filter_by(slug=slug).first()
            if existing_post:
                slug = f"{slug}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            post = BlogPost(
                title=ai_content['title'],
                slug=slug,
                content=ai_content['content'],
                excerpt=ai_content['excerpt'],
                category=category,
                tags=ai_content['tags'],
                author_id=current_user.id,
                is_published=auto_publish,
                is_featured=False,
                published_at=datetime.utcnow() if auto_publish else None
            )
            
            db.session.add(post)
            db.session.commit()
            
            status = "publié" if auto_publish else "créé en brouillon"
            flash(f'Article généré par IA et {status} avec succès!', 'success')
            return redirect(url_for('blog.admin_index'))
            
        except Exception as e:
            flash(f'Erreur lors de la génération: {str(e)}', 'error')
    
    return render_template('blog/admin/ai_generate.html')

@blog_bp.route('/admin/bulk-import', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_bulk_import():
    """Bulk import blog posts from CSV."""
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('Aucun fichier sélectionné.', 'error')
            return render_template('blog/admin/bulk_import.html')
        
        file = request.files['file']
        if file.filename == '':
            flash('Aucun fichier sélectionné.', 'error')
            return render_template('blog/admin/bulk_import.html')
        
        if not file.filename.lower().endswith(('.csv', '.xlsx')):
            flash('Seuls les fichiers CSV et Excel sont acceptés.', 'error')
            return render_template('blog/admin/bulk_import.html')
        
        try:
            # Read CSV content
            stream = io.StringIO(file.stream.read().decode("UTF8"), newline=None)
            csv_input = csv.DictReader(stream)
            
            imported_count = 0
            for row in csv_input:
                title = row.get('title', '').strip()
                content = row.get('content', '').strip()
                
                if not title or not content:
                    continue
                
                # Generate slug
                slug = generate_slug(title)
                
                # Check if slug exists
                existing_post = BlogPost.query.filter_by(slug=slug).first()
                if existing_post:
                    slug = f"{slug}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
                
                post = BlogPost(
                    title=title,
                    slug=slug,
                    content=content,
                    excerpt=row.get('excerpt', '').strip(),
                    category=row.get('category', 'general').strip(),
                    tags=row.get('tags', '').strip(),
                    author_id=current_user.id,
                    is_published=row.get('is_published', '').lower() in ['true', '1', 'yes'],
                    is_featured=row.get('is_featured', '').lower() in ['true', '1', 'yes'],
                    published_at=datetime.utcnow() if row.get('is_published', '').lower() in ['true', '1', 'yes'] else None
                )
                
                db.session.add(post)
                imported_count += 1
            
            db.session.commit()
            flash(f'{imported_count} articles importés avec succès!', 'success')
            return redirect(url_for('blog.admin_index'))
            
        except Exception as e:
            flash(f'Erreur lors de l\'importation: {str(e)}', 'error')
    
    return render_template('blog/admin/bulk_import.html')

@blog_bp.route('/admin/edit/<int:post_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_edit(post_id):
    """Edit a blog post."""
    post = BlogPost.query.get_or_404(post_id)
    
    if request.method == 'POST':
        post.title = request.form.get('title')
        post.content = request.form.get('content')
        post.excerpt = request.form.get('excerpt')
        post.category = request.form.get('category')
        post.tags = request.form.get('tags')
        post.is_published = request.form.get('is_published') == 'on'
        post.is_featured = request.form.get('is_featured') == 'on'
        post.updated_at = datetime.utcnow()
        
        if post.is_published and not post.published_at:
            post.published_at = datetime.utcnow()
        
        db.session.commit()
        flash('Article mis à jour avec succès!', 'success')
        return redirect(url_for('blog.admin_index'))
    
    return render_template('blog/admin/edit.html', post=post)

@blog_bp.route('/admin/delete/<int:post_id>', methods=['POST'])
@login_required
@admin_required
def admin_delete(post_id):
    """Delete a blog post."""
    post = BlogPost.query.get_or_404(post_id)
    db.session.delete(post)
    db.session.commit()
    flash('Article supprimé avec succès!', 'success')
    return redirect(url_for('blog.admin_index'))
