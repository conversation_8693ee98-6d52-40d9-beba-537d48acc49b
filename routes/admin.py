from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import (db, User, Shop, Product, Order, Category, Review,
                   UserRole, UserTier, ShopStatus, ProductStatus, OrderStatus,
                   Advertisement, AdStatus, AdType, CreditTransaction, CreditTransactionType,
                   ProductBoost, CreditPackage)
from utils.decorators import admin_required
from utils.helpers import generate_slug
from config import Config
from sqlalchemy import func, desc
from datetime import datetime, timedelta

admin_bp = Blueprint('admin', __name__)

@admin_bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    """Admin dashboard with overview statistics."""

    # Get basic counts
    total_users = User.query.count()
    total_vendors = User.query.filter_by(role=UserRole.VENDOR).count()
    total_shops = Shop.query.count()
    active_shops = Shop.query.filter_by(status=ShopStatus.ACTIVE).count()
    pending_shops = Shop.query.filter_by(status=ShopStatus.PENDING).count()
    total_products = Product.query.count()
    active_products = Product.query.filter_by(status=ProductStatus.ACTIVE).count()
    pending_products = Product.query.filter_by(status=ProductStatus.PENDING).count()
    total_orders = Order.query.count()

    # Get recent activity
    recent_users = User.query.order_by(desc(User.created_at)).limit(5).all()
    recent_shops = Shop.query.order_by(desc(Shop.created_at)).limit(5).all()
    recent_orders = Order.query.order_by(desc(Order.created_at)).limit(5).all()

    # Get revenue data (last 30 days)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    revenue_data = db.session.query(
        func.date(Order.created_at).label('date'),
        func.sum(Order.commission_amount).label('commission')
    ).filter(
        Order.created_at >= thirty_days_ago,
        Order.status.in_([OrderStatus.DELIVERED, OrderStatus.SHIPPED])
    ).group_by(func.date(Order.created_at)).all()

    # User tier distribution
    tier_distribution = db.session.query(
        User.tier,
        func.count(User.id).label('count')
    ).filter_by(role=UserRole.VENDOR).group_by(User.tier).all()

    return render_template('admin/dashboard.html',
                         total_users=total_users,
                         total_vendors=total_vendors,
                         total_shops=total_shops,
                         active_shops=active_shops,
                         pending_shops=pending_shops,
                         total_products=total_products,
                         active_products=active_products,
                         pending_products=pending_products,
                         total_orders=total_orders,
                         recent_users=recent_users,
                         recent_shops=recent_shops,
                         recent_orders=recent_orders,
                         revenue_data=revenue_data,
                         tier_distribution=tier_distribution)

@admin_bp.route('/users')
@login_required
@admin_required
def manage_users():
    """Manage users."""
    # Check and update expired users on page load (natural cron)
    expired_count = User.check_all_expirations()
    if expired_count > 0:
        flash(f'{expired_count} utilisateurs expirés ont été désactivés automatiquement.', 'info')

    page = request.args.get('page', 1, type=int)
    role_filter = request.args.get('role', 'all')
    tier_filter = request.args.get('tier', 'all')
    status_filter = request.args.get('status', 'all')
    search = request.args.get('search', '').strip()

    # Build query
    users_query = User.query

    if role_filter != 'all':
        users_query = users_query.filter_by(role=UserRole(role_filter))

    if tier_filter != 'all':
        users_query = users_query.filter_by(tier=UserTier(tier_filter))

    if status_filter == 'active':
        users_query = users_query.filter_by(is_active=True)
    elif status_filter == 'inactive':
        users_query = users_query.filter_by(is_active=False)
    elif status_filter == 'expired':
        users_query = users_query.filter(
            db.and_(
                User.expiration_date != None,
                User.expiration_date < datetime.utcnow()
            )
        )

    if search:
        users_query = users_query.filter(
            User.email.contains(search) |
            User.first_name.contains(search) |
            User.last_name.contains(search)
        )

    users = users_query.order_by(desc(User.created_at)).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('admin/users.html',
                         users=users,
                         role_filter=role_filter,
                         tier_filter=tier_filter,
                         status_filter=status_filter,
                         search=search)

@admin_bp.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user(user_id):
    """Edit user details."""
    user = User.query.get_or_404(user_id)

    if request.method == 'POST':
        user.role = UserRole(request.form.get('role'))
        user.tier = UserTier(request.form.get('tier'))
        user.is_active = bool(request.form.get('is_active'))
        user.email_verified = bool(request.form.get('email_verified'))

        # Handle expiration date
        expiration_date_str = request.form.get('expiration_date')
        if expiration_date_str:
            try:
                user.expiration_date = datetime.strptime(expiration_date_str, '%Y-%m-%d')
            except ValueError:
                flash('Format de date invalide. Utilisez YYYY-MM-DD.', 'error')
                return render_template('admin/edit_user.html', user=user)
        else:
            user.expiration_date = None

        try:
            db.session.commit()
            flash(f'User {user.email} updated successfully.', 'success')
            return redirect(url_for('admin.manage_users'))
        except Exception as e:
            db.session.rollback()
            flash('Error updating user.', 'error')

    return render_template('admin/edit_user.html', user=user)

@admin_bp.route('/users/<int:user_id>/update-expiration', methods=['POST'])
@login_required
@admin_required
def update_user_expiration(user_id):
    """Update user expiration date via AJAX."""
    user = User.query.get_or_404(user_id)

    try:
        expiration_date_str = request.json.get('expiration_date')
        if expiration_date_str:
            user.expiration_date = datetime.strptime(expiration_date_str, '%Y-%m-%d')
        else:
            user.expiration_date = None

        db.session.commit()

        # Check if user should be deactivated
        if user.is_expired() and user.is_active:
            user.is_active = False
            db.session.commit()
            return jsonify({
                'success': True,
                'message': 'Date d\'expiration mise à jour. Utilisateur désactivé car expiré.',
                'expired': True
            })

        return jsonify({'success': True, 'message': 'Date d\'expiration mise à jour avec succès.'})
    except ValueError:
        return jsonify({'success': False, 'message': 'Format de date invalide.'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Erreur lors de la mise à jour.'})

@admin_bp.route('/shops')
@login_required
@admin_required
def manage_shops():
    """Manage shops."""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', 'all')
    search = request.args.get('search', '').strip()

    # Build query
    shops_query = Shop.query

    if status_filter != 'all':
        shops_query = shops_query.filter_by(status=ShopStatus(status_filter))

    if search:
        shops_query = shops_query.filter(
            Shop.name.contains(search) |
            Shop.description.contains(search)
        )

    shops = shops_query.order_by(desc(Shop.created_at)).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('admin/shops.html',
                         shops=shops,
                         status_filter=status_filter,
                         search=search)

@admin_bp.route('/shops/<int:shop_id>/approve', methods=['POST'])
@login_required
@admin_required
def approve_shop(shop_id):
    """Approve a shop."""
    shop = Shop.query.get_or_404(shop_id)

    try:
        shop.status = ShopStatus.ACTIVE
        db.session.commit()
        flash(f'Shop "{shop.name}" approved successfully.', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Error approving shop.', 'error')

    return redirect(url_for('admin.manage_shops'))

@admin_bp.route('/shops/<int:shop_id>/reject', methods=['POST'])
@login_required
@admin_required
def reject_shop(shop_id):
    """Reject a shop."""
    shop = Shop.query.get_or_404(shop_id)

    try:
        shop.status = ShopStatus.REJECTED
        db.session.commit()
        flash(f'Shop "{shop.name}" rejected.', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Error rejecting shop.', 'error')

    return redirect(url_for('admin.manage_shops'))

@admin_bp.route('/shops/<int:shop_id>/suspend', methods=['POST'])
@login_required
@admin_required
def suspend_shop(shop_id):
    """Suspend a shop."""
    shop = Shop.query.get_or_404(shop_id)

    try:
        shop.status = ShopStatus.SUSPENDED
        db.session.commit()
        flash(f'Shop "{shop.name}" suspended.', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Error suspending shop.', 'error')

    return redirect(url_for('admin.manage_shops'))

@admin_bp.route('/shops/<int:shop_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_shop(shop_id):
    """Delete a shop and all its associated data."""
    shop = Shop.query.get_or_404(shop_id)

    try:
        # Import necessary models
        from models import Product, Order, OrderItem, Review, Cart, CartItem
        import os

        # Delete associated files
        if shop.logo:
            logo_path = os.path.join('static/uploads/shops', shop.logo)
            if os.path.exists(logo_path):
                os.remove(logo_path)

        if shop.banner:
            banner_path = os.path.join('static/uploads/shops', shop.banner)
            if os.path.exists(banner_path):
                os.remove(banner_path)

        # Delete all products and their associated data
        for product in shop.products:
            # Delete product images
            if product.images:
                for image in product.images:
                    image_path = os.path.join('static/uploads/products', image)
                    if os.path.exists(image_path):
                        os.remove(image_path)

            # Delete cart items containing this product
            CartItem.query.filter_by(product_id=product.id).delete()

            # Delete reviews for this product
            Review.query.filter_by(product_id=product.id).delete()

            # Delete order items (but keep orders for record keeping)
            OrderItem.query.filter_by(product_id=product.id).delete()

        # Delete all products
        Product.query.filter_by(shop_id=shop.id).delete()

        # Update orders to remove shop reference (keep for records)
        orders = Order.query.filter_by(shop_id=shop.id).all()
        for order in orders:
            order.admin_notes = f"Boutique supprimée: {shop.name}"
            order.shop_id = None

        # Delete the shop
        shop_name = shop.name
        db.session.delete(shop)
        db.session.commit()

        flash(f'Boutique "{shop_name}" et toutes ses données associées ont été supprimées avec succès.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la suppression de la boutique: {str(e)}', 'error')

    return redirect(url_for('admin.manage_shops'))

@admin_bp.route('/products')
@login_required
@admin_required
def manage_products():
    """Manage products."""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', 'all')
    search = request.args.get('search', '').strip()

    # Build query
    products_query = Product.query

    if status_filter != 'all':
        products_query = products_query.filter_by(status=ProductStatus(status_filter))

    if search:
        products_query = products_query.filter(
            Product.name.contains(search) |
            Product.description.contains(search)
        )

    products = products_query.order_by(desc(Product.created_at)).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('admin/products.html',
                         products=products,
                         status_filter=status_filter,
                         search=search)

@admin_bp.route('/products/<int:product_id>/approve', methods=['POST'])
@login_required
@admin_required
def approve_product(product_id):
    """Approve a product."""
    product = Product.query.get_or_404(product_id)

    try:
        product.status = ProductStatus.ACTIVE
        db.session.commit()
        flash(f'Product "{product.name}" approved successfully.', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Error approving product.', 'error')

    return redirect(url_for('admin.manage_products'))

@admin_bp.route('/products/<int:product_id>/reject', methods=['POST'])
@login_required
@admin_required
def reject_product(product_id):
    """Reject a product."""
    product = Product.query.get_or_404(product_id)

    try:
        product.status = ProductStatus.REJECTED
        db.session.commit()
        flash(f'Product "{product.name}" rejected.', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Error rejecting product.', 'error')

    return redirect(url_for('admin.manage_products'))

@admin_bp.route('/products/<int:product_id>/suspend', methods=['POST'])
@login_required
@admin_required
def suspend_product(product_id):
    """Suspend a product."""
    product = Product.query.get_or_404(product_id)

    try:
        product.status = ProductStatus.INACTIVE
        db.session.commit()
        flash(f'Product "{product.name}" has been suspended.', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Error suspending product.', 'error')

    return redirect(url_for('admin.manage_products'))

@admin_bp.route('/products/<int:product_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_product(product_id):
    """Edit a product as admin."""
    product = Product.query.get_or_404(product_id)

    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()
        short_description = request.form.get('short_description', '').strip()
        price = request.form.get('price', type=float)
        sale_price = request.form.get('sale_price', type=float)
        category_id = request.form.get('category_id', type=int)
        sku = request.form.get('sku', '').strip()
        stock_quantity = request.form.get('stock_quantity', type=int)
        weight = request.form.get('weight', type=float)
        digital = bool(request.form.get('digital'))
        status = request.form.get('status')

        # Validation
        errors = []

        if not name:
            errors.append('Product name is required.')
        elif len(name) < 3:
            errors.append('Product name must be at least 3 characters long.')

        if not description:
            errors.append('Product description is required.')
        elif len(description) < 20:
            errors.append('Product description must be at least 20 characters long.')

        if not price or price <= 0:
            errors.append('Please enter a valid price.')

        if sale_price and sale_price >= price:
            errors.append('Sale price must be less than regular price.')

        if not category_id:
            errors.append('Please select a category.')

        if stock_quantity is None or stock_quantity < 0:
            errors.append('Please enter a valid stock quantity.')

        if errors:
            for error in errors:
                flash(error, 'error')
            categories = Category.query.filter_by(is_active=True).all()
            return render_template('admin/edit_product.html', product=product, categories=categories)

        # Check if name changed and generate new slug
        if name != product.name:
            slug = generate_slug(f"{name}-{product.shop.name}")
            counter = 1
            original_slug = slug
            while Product.query.filter(Product.slug == slug, Product.id != product.id).first():
                slug = f"{original_slug}-{counter}"
                counter += 1
        else:
            slug = product.slug

        try:
            product.name = name
            product.slug = slug
            product.description = description
            product.short_description = short_description
            product.price = price
            product.sale_price = sale_price
            product.category_id = category_id
            product.sku = sku
            product.stock_quantity = stock_quantity
            product.weight = weight
            product.digital = digital
            product.status = ProductStatus[status.upper()]

            db.session.commit()

            flash('Product has been updated successfully.', 'success')
            return redirect(url_for('admin.manage_products'))

        except Exception as e:
            db.session.rollback()
            flash('An error occurred while updating the product. Please try again.', 'error')

    categories = Category.query.filter_by(is_active=True).all()
    return render_template('admin/edit_product.html', product=product, categories=categories)

@admin_bp.route('/products/<int:product_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_product(product_id):
    """Delete a product."""
    product = Product.query.get_or_404(product_id)

    try:
        # Delete associated data
        from models import CartItem
        import os

        # Delete product images
        if product.images:
            for image_url in product.images:
                if image_url.startswith('/static/uploads/'):
                    image_path = image_url[1:]  # Remove leading slash
                    if os.path.exists(image_path):
                        os.remove(image_path)

        # Delete cart items containing this product
        CartItem.query.filter_by(product_id=product.id).delete()

        # Delete reviews for this product
        Review.query.filter_by(product_id=product.id).delete()

        # Delete the product
        product_name = product.name
        db.session.delete(product)
        db.session.commit()

        flash(f'Product "{product_name}" has been deleted successfully.', 'success')

    except Exception as e:
        db.session.rollback()
        flash('An error occurred while deleting the product. Please try again.', 'error')

    return redirect(url_for('admin.manage_products'))

@admin_bp.route('/categories')
@login_required
@admin_required
def manage_categories():
    """Manage categories."""
    categories = Category.query.order_by(Category.name).all()
    return render_template('admin/categories.html', categories=categories)

@admin_bp.route('/categories/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_category():
    """Create a new category."""
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()
        parent_id = request.form.get('parent_id', type=int)

        if not name:
            flash('Category name is required.', 'error')
            return render_template('admin/create_category.html')

        slug = generate_slug(name)
        if Category.query.filter_by(slug=slug).first():
            flash('A category with this name already exists.', 'error')
            return render_template('admin/create_category.html')

        try:
            category = Category(
                name=name,
                slug=slug,
                description=description,
                parent_id=parent_id if parent_id else None
            )

            db.session.add(category)
            db.session.commit()

            flash(f'Category "{name}" created successfully.', 'success')
            return redirect(url_for('admin.manage_categories'))

        except Exception as e:
            db.session.rollback()
            flash('Error creating category.', 'error')

    # Get parent categories for dropdown
    parent_categories = Category.query.filter_by(parent_id=None).all()
    return render_template('admin/create_category.html', parent_categories=parent_categories)

@admin_bp.route('/orders')
@login_required
@admin_required
def manage_orders():
    """Manage orders."""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', 'all')
    search = request.args.get('search', '').strip()

    # Build query
    orders_query = Order.query

    if status_filter != 'all':
        orders_query = orders_query.filter_by(status=OrderStatus(status_filter))

    if search:
        orders_query = orders_query.filter(
            Order.order_number.contains(search)
        )

    orders = orders_query.order_by(desc(Order.created_at)).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('admin/orders.html',
                         orders=orders,
                         status_filter=status_filter,
                         search=search)

@admin_bp.route('/statistics')
@login_required
@admin_required
def statistics():
    """Comprehensive platform statistics."""
    from datetime import datetime, timedelta

    # Get date range from query params
    days = request.args.get('days', 30, type=int)
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)

    # Platform-wide statistics
    platform_stats = {
        'total_users': User.query.count(),
        'total_vendors': User.query.filter_by(role=UserRole.VENDOR).count(),
        'total_shoppers': User.query.filter_by(role=UserRole.SHOPPER).count(),
        'total_shops': Shop.query.count(),
        'active_shops': Shop.query.filter_by(status=ShopStatus.ACTIVE).count(),
        'total_products': Product.query.count(),
        'active_products': Product.query.filter_by(status=ProductStatus.ACTIVE).count(),
        'total_orders': Order.query.count(),
        'total_revenue': db.session.query(func.sum(Order.total)).filter(
            Order.status.in_([OrderStatus.DELIVERED, OrderStatus.SHIPPED])
        ).scalar() or 0,
        'total_commission': db.session.query(func.sum(Order.commission_amount)).filter(
            Order.status.in_([OrderStatus.DELIVERED, OrderStatus.SHIPPED])
        ).scalar() or 0
    }

    # Growth statistics (last 30 days vs previous 30 days)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    sixty_days_ago = datetime.utcnow() - timedelta(days=60)

    current_period_users = User.query.filter(User.created_at >= thirty_days_ago).count()
    previous_period_users = User.query.filter(
        User.created_at >= sixty_days_ago,
        User.created_at < thirty_days_ago
    ).count()

    current_period_shops = Shop.query.filter(Shop.created_at >= thirty_days_ago).count()
    previous_period_shops = Shop.query.filter(
        Shop.created_at >= sixty_days_ago,
        Shop.created_at < thirty_days_ago
    ).count()

    current_period_orders = Order.query.filter(Order.created_at >= thirty_days_ago).count()
    previous_period_orders = Order.query.filter(
        Order.created_at >= sixty_days_ago,
        Order.created_at < thirty_days_ago
    ).count()

    growth_stats = {
        'users_growth': calculate_growth_percentage(current_period_users, previous_period_users),
        'shops_growth': calculate_growth_percentage(current_period_shops, previous_period_shops),
        'orders_growth': calculate_growth_percentage(current_period_orders, previous_period_orders)
    }

    # Top performing shops
    top_shops_raw = db.session.query(
        Shop,
        func.count(Order.id).label('order_count'),
        func.sum(Order.total).label('total_revenue')
    ).join(Order).filter(
        Order.created_at >= start_date
    ).group_by(Shop.id).order_by(desc('total_revenue')).limit(10).all()

    # Format top shops data for template
    top_shops = []
    for row in top_shops_raw:
        shop = row[0]  # Shop object
        order_count = row[1]  # order_count
        total_revenue = row[2] or 0  # total_revenue

        top_shops.append({
            'shop': shop,
            'order_count': order_count,
            'total_revenue': total_revenue
        })

    # Daily statistics for charts
    daily_stats = db.session.query(
        func.date(Order.created_at).label('date'),
        func.count(Order.id).label('orders'),
        func.sum(Order.total).label('revenue'),
        func.sum(Order.commission_amount).label('commission')
    ).filter(
        Order.created_at >= start_date
    ).group_by(func.date(Order.created_at)).all()

    return render_template('admin/statistics.html',
                         platform_stats=platform_stats,
                         growth_stats=growth_stats,
                         top_shops=top_shops,
                         daily_stats=daily_stats,
                         days=days)

def calculate_growth_percentage(current, previous):
    """Calculate growth percentage between two periods."""
    if previous == 0:
        return 100 if current > 0 else 0
    return round(((current - previous) / previous) * 100, 1)



@admin_bp.route('/settings', methods=['GET', 'POST'])
@login_required
@admin_required
def settings():
    """Admin settings."""
    from models import SiteSetting

    if request.method == 'POST':
        # Update product limits
        free_limit = request.form.get('free_product_limit', type=int)
        premium_limit = request.form.get('premium_product_limit', type=int)
        gold_limit = request.form.get('gold_product_limit', type=int)

        if free_limit and premium_limit and gold_limit:
            # Update or create settings
            settings_data = {
                'free_product_limit': free_limit,
                'premium_product_limit': premium_limit,
                'gold_product_limit': gold_limit
            }

            for key, value in settings_data.items():
                setting = SiteSetting.query.filter_by(key=key).first()
                if setting:
                    setting.value = str(value)
                else:
                    setting = SiteSetting(key=key, value=str(value))
                    db.session.add(setting)

            db.session.commit()
            flash('Limites de produits mises à jour avec succès.', 'success')
        else:
            flash('Veuillez remplir tous les champs.', 'error')

    # Get current settings
    free_limit = SiteSetting.get_value('free_product_limit', 10)
    premium_limit = SiteSetting.get_value('premium_product_limit', 30)
    gold_limit = SiteSetting.get_value('gold_product_limit', 75)

    # Get all users for management
    users = User.query.order_by(User.created_at.desc()).all()

    # Get blog statistics
    from models import BlogPost
    blog_stats = {
        'total_posts': BlogPost.query.count(),
        'published_posts': BlogPost.query.filter_by(is_published=True).count(),
        'draft_posts': BlogPost.query.filter_by(is_published=False).count(),
        'total_views': db.session.query(func.sum(BlogPost.views)).scalar() or 0
    }

    # Helper function for template
    def get_default_limit(tier):
        limits = {'free': free_limit, 'premium': premium_limit, 'gold': gold_limit}
        return limits.get(tier, free_limit)

    return render_template('admin/settings.html',
                         free_limit=free_limit,
                         premium_limit=premium_limit,
                         gold_limit=gold_limit,
                         users=users,
                         blog_stats=blog_stats,
                         get_default_limit=get_default_limit)

@admin_bp.route('/users/<int:user_id>/update-limit', methods=['POST'])
@login_required
@admin_required
def update_user_limit(user_id):
    """Update custom product limit for a user."""
    user = User.query.get_or_404(user_id)

    try:
        limit = request.json.get('limit')
        if limit is not None:
            user.custom_product_limit = int(limit) if limit != '' else None
            db.session.commit()
            return jsonify({'success': True, 'message': 'Limite mise à jour avec succès.'})
        else:
            return jsonify({'success': False, 'message': 'Limite invalide.'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Erreur lors de la mise à jour.'})

# Blog management routes
@admin_bp.route('/blog/posts/<int:post_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_blog_post(post_id):
    """Edit a blog post."""
    from models import BlogPost
    from utils.helpers import generate_slug

    post = BlogPost.query.get_or_404(post_id)

    if request.method == 'POST':
        title = request.form.get('title')
        content = request.form.get('content')
        excerpt = request.form.get('excerpt')
        category = request.form.get('category')
        tags = request.form.get('tags')
        is_published = request.form.get('is_published') == 'on'
        is_featured = request.form.get('is_featured') == 'on'

        if not title or not content:
            flash('Le titre et le contenu sont requis.', 'error')
            return render_template('admin/edit_blog_post.html', post=post)

        # Update slug if title changed
        if title != post.title:
            slug = generate_slug(title)
            existing_post = BlogPost.query.filter(BlogPost.slug == slug, BlogPost.id != post.id).first()
            if existing_post:
                slug = f"{slug}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        else:
            slug = post.slug

        try:
            post.title = title
            post.slug = slug
            post.content = content
            post.excerpt = excerpt
            post.category = category
            post.tags = tags
            post.is_published = is_published
            post.is_featured = is_featured
            post.updated_at = datetime.utcnow()

            if is_published and not post.published_at:
                post.published_at = datetime.utcnow()

            db.session.commit()
            flash('Article mis à jour avec succès!', 'success')
            return redirect(url_for('admin.manage_blog'))

        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de la mise à jour de l\'article.', 'error')

    return render_template('admin/edit_blog_post.html', post=post)

@admin_bp.route('/blog/posts/<int:post_id>/publish', methods=['POST'])
@login_required
@admin_required
def publish_blog_post(post_id):
    """Publish a blog post."""
    from models import BlogPost

    post = BlogPost.query.get_or_404(post_id)

    try:
        post.is_published = True
        post.published_at = datetime.utcnow()
        db.session.commit()
        flash(f'Article "{post.title}" publié avec succès.', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Erreur lors de la publication.', 'error')

    return redirect(url_for('admin.manage_blog'))

@admin_bp.route('/blog/posts/<int:post_id>/unpublish', methods=['POST'])
@login_required
@admin_required
def unpublish_blog_post(post_id):
    """Unpublish a blog post."""
    from models import BlogPost

    post = BlogPost.query.get_or_404(post_id)

    try:
        post.is_published = False
        db.session.commit()
        flash(f'Article "{post.title}" dépublié avec succès.', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Erreur lors de la dépublication.', 'error')

    return redirect(url_for('admin.manage_blog'))

@admin_bp.route('/blog/posts/<int:post_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_blog_post(post_id):
    """Delete a blog post."""
    from models import BlogPost

    post = BlogPost.query.get_or_404(post_id)

    try:
        post_title = post.title
        db.session.delete(post)
        db.session.commit()
        flash(f'Article "{post_title}" supprimé avec succès.', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Erreur lors de la suppression.', 'error')

    return redirect(url_for('admin.manage_blog'))

@admin_bp.route('/blog')
@login_required
@admin_required
def manage_blog():
    """Manage blog posts."""
    from models import BlogPost

    # Get filters
    search = request.args.get('search', '')
    status_filter = request.args.get('status', 'all')
    page = request.args.get('page', 1, type=int)

    # Build query
    query = BlogPost.query

    if search:
        query = query.filter(BlogPost.title.contains(search))

    if status_filter == 'published':
        query = query.filter_by(is_published=True)
    elif status_filter == 'draft':
        query = query.filter_by(is_published=False)
    elif status_filter == 'featured':
        query = query.filter_by(is_featured=True)

    # Paginate
    posts = query.order_by(BlogPost.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('admin/blog.html',
                         posts=posts,
                         search=search,
                         status_filter=status_filter)

@admin_bp.route('/blog/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_blog_post():
    """Create a new blog post."""
    from models import BlogPost
    from utils.helpers import generate_slug

    if request.method == 'POST':
        title = request.form.get('title')
        content = request.form.get('content')
        excerpt = request.form.get('excerpt')
        category = request.form.get('category')
        tags = request.form.get('tags')
        is_published = request.form.get('is_published') == 'on'
        is_featured = request.form.get('is_featured') == 'on'

        # AI content generation if requested
        if request.form.get('generate_ai'):
            try:
                import openai
                ai_prompt = request.form.get('ai_prompt')
                if ai_prompt:
                    # Set OpenAI API key (you should set this in environment variables)
                    openai.api_key = "your-openai-api-key-here"  # Replace with actual key

                    # Generate content with AI
                    response = openai.ChatCompletion.create(
                        model="gpt-4",
                        messages=[
                            {"role": "system", "content": "Tu es un expert en e-commerce et marketplace africaine. Écris des articles de blog informatifs et engageants en français pour Afroly.org, une marketplace africaine. Utilise un langage accessible et des exemples pertinents pour l'Afrique."},
                            {"role": "user", "content": f"Écris un article de blog sur: {ai_prompt}"}
                        ],
                        max_tokens=2000,
                        temperature=0.7
                    )
                    content = response.choices[0].message.content

                    # Generate title if not provided
                    if not title:
                        title_response = openai.ChatCompletion.create(
                            model="gpt-4",
                            messages=[
                                {"role": "system", "content": "Génère un titre accrocheur et SEO-friendly en français pour cet article de blog."},
                                {"role": "user", "content": content[:500]}
                            ],
                            max_tokens=100,
                            temperature=0.7
                        )
                        title = title_response.choices[0].message.content.strip('"')

                    # Generate excerpt if not provided
                    if not excerpt:
                        excerpt_response = openai.ChatCompletion.create(
                            model="gpt-4",
                            messages=[
                                {"role": "system", "content": "Génère un résumé court et engageant (maximum 150 caractères) pour cet article."},
                                {"role": "user", "content": content[:500]}
                            ],
                            max_tokens=50,
                            temperature=0.7
                        )
                        excerpt = excerpt_response.choices[0].message.content.strip('"')

            except Exception as e:
                flash(f'Erreur lors de la génération AI: {str(e)}. Veuillez remplir manuellement.', 'warning')

        if not title or not content:
            flash('Le titre et le contenu sont requis.', 'error')
            return render_template('admin/create_blog_post.html')

        # Generate slug
        slug = generate_slug(title)
        existing_post = BlogPost.query.filter_by(slug=slug).first()
        if existing_post:
            slug = f"{slug}-{datetime.now().strftime('%Y%m%d%H%M%S')}"

        try:
            post = BlogPost(
                title=title,
                slug=slug,
                content=content,
                excerpt=excerpt,
                category=category,
                tags=tags,
                is_published=is_published,
                is_featured=is_featured,
                author_id=current_user.id
            )

            if is_published:
                post.published_at = datetime.utcnow()

            db.session.add(post)
            db.session.commit()

            flash('Article créé avec succès!', 'success')
            return redirect(url_for('admin.manage_blog'))

        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de la création de l\'article.', 'error')

    return render_template('admin/create_blog_post.html')

@admin_bp.route('/blog/bulk-import', methods=['GET', 'POST'])
@login_required
@admin_required
def bulk_import_blog():
    """Bulk import blog posts from CSV/Excel."""
    import pandas as pd
    import os
    from werkzeug.utils import secure_filename
    from models import BlogPost
    from utils.helpers import generate_slug

    if request.method == 'POST':
        if 'file' not in request.files:
            flash('Aucun fichier sélectionné', 'error')
            return redirect(request.url)

        file = request.files['file']
        if file.filename == '':
            flash('Aucun fichier sélectionné', 'error')
            return redirect(request.url)

        if file and file.filename.lower().endswith(('.csv', '.xlsx', '.xls')):
            try:
                # Save uploaded file temporarily
                filename = secure_filename(file.filename)
                temp_dir = 'temp'
                os.makedirs(temp_dir, exist_ok=True)
                filepath = os.path.join(temp_dir, filename)
                file.save(filepath)

                # Read file based on extension
                if filename.lower().endswith('.csv'):
                    df = pd.read_csv(filepath)
                else:
                    df = pd.read_excel(filepath)

                # Validate required columns
                required_columns = ['title', 'content']
                if not all(col in df.columns for col in required_columns):
                    flash(f'Le fichier doit contenir les colonnes: {", ".join(required_columns)}', 'error')
                    os.remove(filepath)
                    return redirect(request.url)

                # Import posts
                imported_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        title = str(row['title']).strip()
                        content = str(row['content']).strip()

                        if not title or not content:
                            errors.append(f"Ligne {index + 2}: Titre ou contenu manquant")
                            continue

                        # Generate unique slug
                        slug = generate_slug(title)
                        counter = 1
                        original_slug = slug
                        while BlogPost.query.filter_by(slug=slug).first():
                            slug = f"{original_slug}-{counter}"
                            counter += 1

                        post = BlogPost(
                            title=title,
                            slug=slug,
                            content=content,
                            excerpt=str(row.get('excerpt', content[:200] + "...")).strip(),
                            category=str(row.get('category', '')).strip(),
                            tags=str(row.get('tags', '')).strip(),
                            is_published=str(row.get('is_published', 'False')).lower() in ['true', '1', 'yes', 'oui'],
                            is_featured=str(row.get('is_featured', 'False')).lower() in ['true', '1', 'yes', 'oui'],
                            author_id=current_user.id
                        )

                        if post.is_published:
                            post.published_at = datetime.utcnow()

                        db.session.add(post)
                        imported_count += 1

                    except Exception as e:
                        errors.append(f"Ligne {index + 2}: {str(e)}")
                        continue

                db.session.commit()
                os.remove(filepath)

                if imported_count > 0:
                    flash(f'{imported_count} articles importés avec succès!', 'success')

                if errors:
                    flash(f'Erreurs rencontrées: {"; ".join(errors[:5])}', 'warning')

                return redirect(url_for('admin.manage_blog'))

            except Exception as e:
                flash(f'Erreur lors de l\'importation: {str(e)}', 'error')
                if os.path.exists(filepath):
                    os.remove(filepath)
        else:
            flash('Format de fichier non supporté. Utilisez CSV, XLS ou XLSX.', 'error')

    return render_template('admin/bulk_import_blog.html')

# Ad Management Routes
@admin_bp.route('/ads')
@login_required
@admin_required
def manage_ads():
    """Manage advertisements."""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', 'all')
    search = request.args.get('search', '').strip()

    # Build query
    ads_query = Advertisement.query

    if status_filter != 'all':
        ads_query = ads_query.filter_by(status=AdStatus(status_filter))

    if search:
        ads_query = ads_query.filter(
            Advertisement.name.contains(search) |
            Advertisement.title.contains(search)
        )

    ads = ads_query.order_by(desc(Advertisement.created_at)).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('admin/ads.html',
                         ads=ads,
                         status_filter=status_filter,
                         search=search)

@admin_bp.route('/ads/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_ad():
    """Create a new advertisement."""
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        ad_type = request.form.get('ad_type')
        title = request.form.get('title', '').strip()
        content = request.form.get('content', '').strip()
        image_url = request.form.get('image_url', '').strip()
        link_url = request.form.get('link_url', '').strip()
        status = request.form.get('status', 'draft')
        display_order = request.form.get('display_order', 0, type=int)
        max_impressions = request.form.get('max_impressions', type=int)

        # Date handling
        start_date_str = request.form.get('start_date')
        end_date_str = request.form.get('end_date')

        start_date = None
        end_date = None

        if start_date_str:
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            except ValueError:
                flash('Format de date de début invalide.', 'error')
                return render_template('admin/ad_form.html')

        if end_date_str:
            try:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            except ValueError:
                flash('Format de date de fin invalide.', 'error')
                return render_template('admin/ad_form.html')

        # Validation
        errors = []
        if not name:
            errors.append('Le nom de la publicité est requis.')
        if not ad_type or ad_type not in ['text', 'image', 'html']:
            errors.append('Type de publicité invalide.')

        if ad_type == 'image' and not image_url:
            errors.append('URL de l\'image requise pour les publicités image.')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('admin/ad_form.html')

        try:
            ad = Advertisement(
                name=name,
                ad_type=AdType(ad_type),
                title=title,
                content=content,
                image_url=image_url,
                link_url=link_url,
                status=AdStatus(status),
                display_order=display_order,
                max_impressions=max_impressions,
                start_date=start_date,
                end_date=end_date
            )

            db.session.add(ad)
            db.session.commit()

            flash(f'Publicité "{name}" créée avec succès.', 'success')
            return redirect(url_for('admin.manage_ads'))

        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de la création de la publicité.', 'error')

    return render_template('admin/ad_form.html')

@admin_bp.route('/ads/<int:ad_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_ad(ad_id):
    """Edit an advertisement."""
    ad = Advertisement.query.get_or_404(ad_id)

    if request.method == 'POST':
        ad.name = request.form.get('name', '').strip()
        ad.ad_type = AdType(request.form.get('ad_type'))
        ad.title = request.form.get('title', '').strip()
        ad.content = request.form.get('content', '').strip()
        ad.image_url = request.form.get('image_url', '').strip()
        ad.link_url = request.form.get('link_url', '').strip()
        ad.status = AdStatus(request.form.get('status', 'draft'))
        ad.display_order = request.form.get('display_order', 0, type=int)
        ad.max_impressions = request.form.get('max_impressions', type=int)

        # Date handling
        start_date_str = request.form.get('start_date')
        end_date_str = request.form.get('end_date')

        if start_date_str:
            try:
                ad.start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            except ValueError:
                flash('Format de date de début invalide.', 'error')
                return render_template('admin/ad_form.html', ad=ad)
        else:
            ad.start_date = None

        if end_date_str:
            try:
                ad.end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            except ValueError:
                flash('Format de date de fin invalide.', 'error')
                return render_template('admin/ad_form.html', ad=ad)
        else:
            ad.end_date = None

        try:
            db.session.commit()
            flash(f'Publicité "{ad.name}" mise à jour avec succès.', 'success')
            return redirect(url_for('admin.manage_ads'))

        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de la mise à jour de la publicité.', 'error')

    return render_template('admin/ad_form.html', ad=ad)

@admin_bp.route('/ads/<int:ad_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_ad(ad_id):
    """Delete an advertisement."""
    ad = Advertisement.query.get_or_404(ad_id)

    try:
        ad_name = ad.name
        db.session.delete(ad)
        db.session.commit()
        flash(f'Publicité "{ad_name}" supprimée avec succès.', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Erreur lors de la suppression de la publicité.', 'error')

    return redirect(url_for('admin.manage_ads'))

@admin_bp.route('/ads/<int:ad_id>/toggle-status', methods=['POST'])
@login_required
@admin_required
def toggle_ad_status(ad_id):
    """Toggle advertisement status between active and inactive."""
    ad = Advertisement.query.get_or_404(ad_id)

    try:
        if ad.status == AdStatus.ACTIVE:
            ad.status = AdStatus.INACTIVE
            message = f'Publicité "{ad.name}" désactivée.'
        else:
            ad.status = AdStatus.ACTIVE
            message = f'Publicité "{ad.name}" activée.'

        db.session.commit()
        flash(message, 'success')
    except Exception as e:
        db.session.rollback()
        flash('Erreur lors du changement de statut.', 'error')

    return redirect(url_for('admin.manage_ads'))

# Credit Package Management Routes
@admin_bp.route('/credit-packages')
@login_required
@admin_required
def manage_credit_packages():
    """Manage credit packages."""
    packages = CreditPackage.query.order_by(CreditPackage.display_order, CreditPackage.credits).all()
    return render_template('admin/credit_packages.html', packages=packages)

@admin_bp.route('/credit-packages/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_credit_package():
    """Create a new credit package."""
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        credits = request.form.get('credits', type=int)
        price_eur = request.form.get('price_eur', type=float)
        price_fcfa = request.form.get('price_fcfa', type=float)
        bonus_credits = request.form.get('bonus_credits', 0, type=int)
        description = request.form.get('description', '').strip()
        display_order = request.form.get('display_order', 0, type=int)
        is_active = bool(request.form.get('is_active'))

        # Validation
        errors = []
        if not name:
            errors.append('Le nom du package est requis.')
        if not credits or credits <= 0:
            errors.append('Le nombre de crédits doit être positif.')
        if not price_eur or price_eur <= 0:
            errors.append('Le prix en EUR doit être positif.')
        if not price_fcfa or price_fcfa <= 0:
            errors.append('Le prix en FCFA doit être positif.')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('admin/credit_package_form.html')

        try:
            package = CreditPackage(
                name=name,
                credits=credits,
                price_eur=price_eur,
                price_fcfa=price_fcfa,
                bonus_credits=bonus_credits,
                description=description,
                display_order=display_order,
                is_active=is_active
            )

            db.session.add(package)
            db.session.commit()

            flash(f'Package de crédits "{name}" créé avec succès.', 'success')
            return redirect(url_for('admin.manage_credit_packages'))

        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de la création du package.', 'error')

    return render_template('admin/credit_package_form.html')

@admin_bp.route('/credit-packages/<int:package_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_credit_package(package_id):
    """Edit a credit package."""
    package = CreditPackage.query.get_or_404(package_id)

    if request.method == 'POST':
        package.name = request.form.get('name', '').strip()
        package.credits = request.form.get('credits', type=int)
        package.price_eur = request.form.get('price_eur', type=float)
        package.price_fcfa = request.form.get('price_fcfa', type=float)
        package.bonus_credits = request.form.get('bonus_credits', 0, type=int)
        package.description = request.form.get('description', '').strip()
        package.display_order = request.form.get('display_order', 0, type=int)
        package.is_active = bool(request.form.get('is_active'))

        try:
            db.session.commit()
            flash(f'Package "{package.name}" mis à jour avec succès.', 'success')
            return redirect(url_for('admin.manage_credit_packages'))

        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de la mise à jour du package.', 'error')

    return render_template('admin/credit_package_form.html', package=package)

@admin_bp.route('/credit-packages/<int:package_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_credit_package(package_id):
    """Delete a credit package."""
    package = CreditPackage.query.get_or_404(package_id)

    try:
        package_name = package.name
        db.session.delete(package)
        db.session.commit()
        flash(f'Package "{package_name}" supprimé avec succès.', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Erreur lors de la suppression du package.', 'error')

    return redirect(url_for('admin.manage_credit_packages'))

# Credit Management Routes
@admin_bp.route('/credits')
@login_required
@admin_required
def manage_credits():
    """Manage credit transactions and approvals."""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', 'all')
    search = request.args.get('search', '').strip()

    # Build query without join first, then add search filter if needed
    transactions_query = CreditTransaction.query

    if status_filter == 'pending':
        transactions_query = transactions_query.filter(
            CreditTransaction.payment_status == 'pending',
            CreditTransaction.is_approved == False
        )
    elif status_filter == 'approved':
        transactions_query = transactions_query.filter(
            CreditTransaction.is_approved == True
        )
    elif status_filter == 'rejected':
        transactions_query = transactions_query.filter(
            CreditTransaction.payment_status == 'rejected'
        )

    # Add search filter with explicit join only when needed
    if search:
        transactions_query = transactions_query.join(User, CreditTransaction.user_id == User.id).filter(
            or_(
                User.email.contains(search),
                User.first_name.contains(search),
                User.last_name.contains(search),
                CreditTransaction.description.contains(search)
            )
        )

    transactions = transactions_query.order_by(desc(CreditTransaction.created_at)).paginate(
        page=page, per_page=20, error_out=False
    )

    # Get summary statistics
    pending_count = CreditTransaction.query.filter(
        CreditTransaction.payment_status == 'pending',
        CreditTransaction.is_approved == False
    ).count()

    total_pending_amount = db.session.query(func.sum(CreditTransaction.amount)).filter(
        CreditTransaction.payment_status == 'pending',
        CreditTransaction.is_approved == False
    ).scalar() or 0

    return render_template('admin/credits.html',
                         transactions=transactions,
                         status_filter=status_filter,
                         search=search,
                         pending_count=pending_count,
                         total_pending_amount=total_pending_amount)

@admin_bp.route('/credits/<int:transaction_id>/approve', methods=['POST'])
@login_required
@admin_required
def approve_credit_transaction(transaction_id):
    """Approve a credit transaction."""
    transaction = CreditTransaction.query.get_or_404(transaction_id)

    if transaction.is_approved:
        flash('Cette transaction a déjà été approuvée.', 'warning')
        return redirect(url_for('admin.manage_credits'))

    admin_notes = request.form.get('admin_notes', '').strip()

    try:
        if transaction.approve(current_user, admin_notes):
            db.session.commit()
            flash(f'Transaction approuvée. {transaction.amount} crédits ajoutés au compte de {transaction.user.get_full_name()}.', 'success')
        else:
            flash('Erreur lors de l\'approbation de la transaction.', 'error')
    except Exception as e:
        db.session.rollback()
        flash('Erreur lors de l\'approbation de la transaction.', 'error')

    return redirect(url_for('admin.manage_credits'))

@admin_bp.route('/credits/<int:transaction_id>/reject', methods=['POST'])
@login_required
@admin_required
def reject_credit_transaction(transaction_id):
    """Reject a credit transaction."""
    transaction = CreditTransaction.query.get_or_404(transaction_id)

    if transaction.is_approved:
        flash('Cette transaction a déjà été approuvée et ne peut pas être rejetée.', 'warning')
        return redirect(url_for('admin.manage_credits'))

    admin_notes = request.form.get('admin_notes', '').strip()

    try:
        if transaction.reject(current_user, admin_notes):
            db.session.commit()
            flash(f'Transaction rejetée pour {transaction.user.get_full_name()}.', 'success')
        else:
            flash('Erreur lors du rejet de la transaction.', 'error')
    except Exception as e:
        db.session.rollback()
        flash('Erreur lors du rejet de la transaction.', 'error')

    return redirect(url_for('admin.manage_credits'))