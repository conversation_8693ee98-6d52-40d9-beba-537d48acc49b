#!/usr/bin/env python3
"""
Migration script to create blog and statistics tables
"""

from app import create_app
from models import db, BlogPost, SiteStatistic, ShopStatistic

def migrate_blog_tables():
    """Create blog and statistics tables"""
    app = create_app()
    
    with app.app_context():
        try:
            # Create all tables
            db.create_all()
            print("✅ Blog and statistics tables created successfully!")
            
            # Check if tables exist
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            blog_tables = ['blog_post', 'site_statistic', 'shop_statistic']
            for table in blog_tables:
                if table in tables:
                    print(f"✅ Table '{table}' exists")
                else:
                    print(f"❌ Table '{table}' not found")
            
        except Exception as e:
            print(f"❌ Error creating tables: {e}")

if __name__ == "__main__":
    migrate_blog_tables()
