#!/usr/bin/env python3

import sqlite3
import json

def update_artisanat_images():
    """Update images for artisanat products directly in database"""
    try:
        # Connect to database
        conn = sqlite3.connect('instance/afromall.db')
        cursor = conn.cursor()
        
        # Get shop ID
        cursor.execute("SELECT id FROM shop WHERE slug = 'artisanat-traditionnel-senegal'")
        shop_result = cursor.fetchone()
        
        if not shop_result:
            print("❌ Shop not found")
            return
        
        shop_id = shop_result[0]
        print(f"🏪 Found shop ID: {shop_id}")
        
        # Get products for this shop
        cursor.execute("SELECT id, name, images FROM product WHERE shop_id = ?", (shop_id,))
        products = cursor.fetchall()
        
        print(f"📦 Found {len(products)} products")
        
        # Working image URLs
        good_images = [
            'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=500&h=600&fit=crop',
            'https://images.unsplash.com/photo-1573408301185-9146fe634ad0?w=500&h=600&fit=crop',
            'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=500&h=600&fit=crop',
            'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500&h=600&fit=crop',
            'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=500&h=600&fit=crop'
        ]
        
        # Update each product
        for i, (product_id, name, current_images) in enumerate(products):
            print(f"\n📦 Updating: {name}")
            print(f"   Current: {current_images}")
            
            # Create new images array
            new_images = [good_images[i % len(good_images)]]
            new_images_json = json.dumps(new_images)
            
            # Update in database
            cursor.execute("UPDATE product SET images = ? WHERE id = ?", (new_images_json, product_id))
            print(f"   New: {new_images}")
        
        # Commit changes
        conn.commit()
        print(f"\n✅ Successfully updated {len(products)} products!")
        
        # Verify
        cursor.execute("SELECT name, images FROM product WHERE shop_id = ?", (shop_id,))
        updated_products = cursor.fetchall()
        
        print(f"\n📊 Verification:")
        for name, images in updated_products:
            print(f"   - {name}: {images}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == '__main__':
    update_artisanat_images()
