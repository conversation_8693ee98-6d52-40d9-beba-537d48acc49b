#!/usr/bin/env python3
"""
Simple script to add products to Mode Africaine Élégante
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, Shop, Product, Category, ProductStatus
import random

def main():
    app = create_app()
    
    with app.app_context():
        try:
            print("Starting product addition...")
            
            # Get the shop
            shop = Shop.query.filter_by(name='Mode Africaine Élégante').first()
            if not shop:
                print("Shop not found!")
                return
            
            print(f"Found shop: {shop.name}")
            
            # Get or create category
            category = Category.query.first()
            if not category:
                print("No category found, creating one...")
                category = Category(
                    name='Mode & Vêtements',
                    slug='mode-vetements',
                    description='Vêtements africains'
                )
                db.session.add(category)
                db.session.commit()
            
            print(f"Using category: {category.name}")
            
            # Simple product data
            products = [
                {
                    'name': 'Robe Wax Élégante Femme',
                    'price': 45000,
                    'sale_price': 38000,
                    'stock': 15
                },
                {
                    'name': '<PERSON><PERSON><PERSON>zin Brodé',
                    'price': 35000,
                    'stock': 12
                },
                {
                    'name': 'Ensemble Complet Femme Kente',
                    'price': 65000,
                    'sale_price': 55000,
                    'stock': 8
                },
                {
                    'name': 'Pantalon Homme Bogolan',
                    'price': 28000,
                    'stock': 20
                },
                {
                    'name': 'Robe Longue Ankara Moderne',
                    'price': 42000,
                    'sale_price': 36000,
                    'stock': 18
                },
                {
                    'name': 'Veste Homme Wax Casual',
                    'price': 38000,
                    'stock': 10
                },
                {
                    'name': 'Jupe Plissée Tissu Traditionnel',
                    'price': 25000,
                    'sale_price': 22000,
                    'stock': 25
                },
                {
                    'name': 'Combinaison Femme Wax Chic',
                    'price': 48000,
                    'stock': 14
                },
                {
                    'name': 'Costume Traditionnel Complet',
                    'price': 85000,
                    'sale_price': 75000,
                    'stock': 6
                }
            ]
            
            added = 0
            for i, prod_data in enumerate(products):
                try:
                    # Create simple slug
                    slug = f"product-{i+10}-{shop.slug}"
                    
                    # Check if exists
                    existing = Product.query.filter_by(slug=slug).first()
                    if existing:
                        print(f"Product {i+1} already exists, skipping...")
                        continue
                    
                    # Create product
                    product = Product(
                        shop_id=shop.id,
                        category_id=category.id,
                        name=prod_data['name'],
                        slug=slug,
                        description=f"Description pour {prod_data['name']}",
                        short_description=f"Produit de mode africaine - {prod_data['name']}",
                        price=prod_data['price'],
                        sale_price=prod_data.get('sale_price'),
                        stock_quantity=prod_data['stock'],
                        images=[
                            'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500&h=600&fit=crop',
                            'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=500&h=600&fit=crop'
                        ],
                        status=ProductStatus.ACTIVE,
                        sku=f"MAE-{random.randint(1000, 9999)}"
                    )
                    
                    db.session.add(product)
                    added += 1
                    print(f"Added: {prod_data['name']}")
                    
                except Exception as e:
                    print(f"Error adding product {i+1}: {e}")
                    continue
            
            # Commit all changes
            db.session.commit()
            print(f"\nSuccessfully added {added} products!")
            
            # Count total
            total = Product.query.filter_by(shop_id=shop.id).count()
            print(f"Total products in shop: {total}")
            
        except Exception as e:
            print(f"Error: {e}")
            db.session.rollback()

if __name__ == '__main__':
    main()
