from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import enum

db = SQLAlchemy()

class UserRole(enum.Enum):
    SHOPPER = "shopper"
    VENDOR = "vendor"
    ADMIN = "admin"

class UserTier(enum.Enum):
    FREE = "free"
    PREMIUM = "premium"
    GOLD = "gold"

class ShopStatus(enum.Enum):
    PENDING = "pending"
    ACTIVE = "active"
    SUSPENDED = "suspended"
    REJECTED = "rejected"

class ProductStatus(enum.Enum):
    PENDING = "pending"
    ACTIVE = "active"
    REJECTED = "rejected"
    OUT_OF_STOCK = "out_of_stock"

class OrderStatus(enum.Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    SHIPPED = "shipped"
    DELIVERED = "delivered"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"

class AdStatus(enum.Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    DRAFT = "draft"

class AdType(enum.Enum):
    TEXT = "text"
    IMAGE = "image"
    HTML = "html"

class CreditTransactionType(enum.Enum):
    PURCHASE = "purchase"
    BOOST = "boost"
    REFUND = "refund"
    ADMIN_ADJUSTMENT = "admin_adjustment"

class EmailType(enum.Enum):
    WELCOME = "welcome"
    SHOP_CREATED = "shop_created"
    SHOP_APPROVED = "shop_approved"
    SHOP_REJECTED = "shop_rejected"
    PRODUCT_APPROVED = "product_approved"
    PRODUCT_REJECTED = "product_rejected"
    ORDER_CONFIRMATION = "order_confirmation"
    ORDER_SHIPPED = "order_shipped"
    ORDER_DELIVERED = "order_delivered"
    PAYMENT_CONFIRMATION = "payment_confirmation"
    NEWSLETTER = "newsletter"
    SYSTEM_NOTIFICATION = "system_notification"

class EmailStatus(enum.Enum):
    PENDING = "pending"
    SENT = "sent"
    FAILED = "failed"
    CANCELLED = "cancelled"

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    phone = db.Column(db.String(20))
    role = db.Column(db.Enum(UserRole), default=UserRole.SHOPPER, nullable=False)
    tier = db.Column(db.Enum(UserTier), default=UserTier.FREE, nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    email_verified = db.Column(db.Boolean, default=False, nullable=False)
    custom_product_limit = db.Column(db.Integer, nullable=True)  # Custom product limit override
    expiration_date = db.Column(db.DateTime, nullable=True)  # User account expiration date
    credit_balance = db.Column(db.Integer, default=0, nullable=False)  # User credit balance for boosting
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    shops = db.relationship('Shop', backref='owner', lazy=True, cascade='all, delete-orphan')
    orders = db.relationship('Order', backref='customer', lazy=True)
    carts = db.relationship('Cart', backref='user', lazy=True, cascade='all, delete-orphan')
    reviews = db.relationship('Review', backref='user', lazy=True)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def get_full_name(self):
        return f"{self.first_name} {self.last_name}"

    def can_create_shop(self):
        from config import Config
        if self.role != UserRole.VENDOR:
            return False

        shop_limit = Config.SHOP_LIMITS.get(self.tier.value, 1)
        if shop_limit == -1:  # unlimited
            return True

        return len(self.shops) < shop_limit

    def is_expired(self):
        """Check if user account is expired."""
        if not self.expiration_date:
            return False
        return datetime.utcnow() > self.expiration_date

    def check_and_update_expiration(self):
        """Check expiration and update status if needed."""
        if self.is_expired() and self.is_active:
            self.is_active = False
            return True  # Status was changed
        return False  # No change needed

    def get_product_limit(self):
        """Get the product limit for this user's tier."""
        # Check for custom limit first
        if self.custom_product_limit is not None:
            return self.custom_product_limit

        # Get from admin settings
        from models import SiteSetting
        setting_key = f'{self.tier.value}_product_limit'
        default_limits = {'free': 10, 'premium': 30, 'gold': 75}
        return SiteSetting.get_value(setting_key, default_limits.get(self.tier.value, 10))

    def add_credits(self, amount, transaction_type=None, description=None, auto_approve=False):
        """Add credits to user's balance and create transaction record."""
        if amount <= 0:
            return False

        # For purchases, don't add credits immediately unless auto_approve is True
        if transaction_type == CreditTransactionType.PURCHASE and not auto_approve:
            # Create pending transaction without adding credits
            transaction = CreditTransaction(
                user_id=self.id,
                amount=amount,
                transaction_type=transaction_type,
                description=description or f"Achat de {amount} crédits",
                payment_status='pending',
                is_approved=False
            )
            db.session.add(transaction)
            return transaction
        else:
            # For admin adjustments, welcome credits, etc. - add immediately
            self.credit_balance += amount
            transaction = CreditTransaction(
                user_id=self.id,
                amount=amount,
                transaction_type=transaction_type or CreditTransactionType.ADMIN_ADJUSTMENT,
                description=description or f"Added {amount} credits",
                payment_status='completed',
                is_approved=True,
                approved_at=datetime.utcnow()
            )
            db.session.add(transaction)
            return transaction

    def deduct_credits(self, amount, transaction_type=None, description=None):
        """Deduct credits from user's balance if sufficient credits available."""
        if amount <= 0 or self.credit_balance < amount:
            return False

        self.credit_balance -= amount

        # Create transaction record
        transaction = CreditTransaction(
            user_id=self.id,
            amount=-amount,
            transaction_type=transaction_type or CreditTransactionType.BOOST,
            description=description or f"Deducted {amount} credits"
        )
        db.session.add(transaction)
        return True

    def has_sufficient_credits(self, amount):
        """Check if user has sufficient credits."""
        return self.credit_balance >= amount

    @staticmethod
    def check_all_expirations():
        """Check all users for expiration and update status."""
        from sqlalchemy import and_

        # Find all active users with expiration dates that have passed
        expired_users = User.query.filter(
            and_(
                User.is_active == True,
                User.expiration_date != None,
                User.expiration_date < datetime.utcnow()
            )
        ).all()

        updated_count = 0
        for user in expired_users:
            user.is_active = False
            updated_count += 1

        if updated_count > 0:
            db.session.commit()

        return updated_count

    def __repr__(self):
        return f'<User {self.email}>'

class Shop(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    owner_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    slug = db.Column(db.String(100), unique=True, nullable=False, index=True)
    description = db.Column(db.Text)
    logo = db.Column(db.String(255))
    banner = db.Column(db.String(255))
    country = db.Column(db.String(100), nullable=False, default='Sénégal')  # African country
    currency = db.Column(db.String(10), nullable=False, default='FCFA')  # Payment currency
    status = db.Column(db.Enum(ShopStatus), default=ShopStatus.PENDING, nullable=False)
    contact_email = db.Column(db.String(120))
    contact_phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    social_links = db.Column(db.JSON)  # Store social media links as JSON
    policies = db.Column(db.JSON)  # Store shop policies as JSON
    payment_info = db.Column(db.JSON)  # Store payment gateway info as JSON
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    products = db.relationship('Product', backref='shop', lazy=True, cascade='all, delete-orphan')
    orders = db.relationship('Order', backref='shop', lazy=True)
    carts = db.relationship('Cart', backref='shop', lazy=True, cascade='all, delete-orphan')
    reviews = db.relationship('Review', backref='shop', lazy=True)

    def get_active_products_count(self):
        return Product.query.filter_by(shop_id=self.id, status=ProductStatus.ACTIVE).count()

    def can_add_product(self):
        # Use the owner's get_product_limit method which checks admin settings
        product_limit = self.owner.get_product_limit()
        return self.get_active_products_count() < product_limit

    def get_average_rating(self):
        reviews = Review.query.filter_by(shop_id=self.id).all()
        if not reviews:
            return 0
        return sum(review.rating for review in reviews) / len(reviews)

    def get_payment_methods(self):
        """Get configured payment methods for this shop"""
        if not self.payment_info:
            return []
        return self.payment_info.get('methods', [])

    def has_payment_method(self, method_type):
        """Check if shop has a specific payment method configured"""
        methods = self.get_payment_methods()
        return any(method.get('type') == method_type for method in methods)

    def can_use_payment_method(self, method_key):
        """Check if shop owner's tier allows using a specific payment method"""
        from config import Config

        # Find the payment method configuration
        for category, methods in Config.PAYMENT_METHODS.items():
            if method_key in methods:
                method_config = methods[method_key]
                required_tier = method_config.get('tier_required', 'free')

                # Check tier hierarchy: free < premium < gold
                tier_hierarchy = {'free': 0, 'premium': 1, 'gold': 2}
                user_tier_level = tier_hierarchy.get(self.owner.tier.value, 0)
                required_tier_level = tier_hierarchy.get(required_tier, 0)

                return user_tier_level >= required_tier_level

        return False

    def get_available_payment_methods(self):
        """Get payment methods available for this shop based on country and tier"""
        from config import Config
        available_methods = {}

        for category, methods in Config.PAYMENT_METHODS.items():
            available_methods[category] = {}
            for method_key, method_config in methods.items():
                # Check tier requirement
                if not self.can_use_payment_method(method_key):
                    continue

                # Check country availability
                supported_countries = method_config.get('countries', [])
                if supported_countries != 'all' and self.country not in supported_countries:
                    continue

                available_methods[category][method_key] = method_config

        return available_methods

    def get_social_media(self):
        """Get social media settings for this shop"""
        if not self.social_links:
            return {}
        return self.social_links

    def get_whatsapp_number(self):
        """Get WhatsApp number for this shop"""
        social_media = self.get_social_media()
        whatsapp_data = social_media.get('whatsapp', {})
        if whatsapp_data.get('enabled', False):
            return whatsapp_data.get('number', '')
        return None

    def get_whatsapp_link(self, message=None):
        """Get WhatsApp chat link for this shop"""
        number = self.get_whatsapp_number()
        if number:
            # Remove any non-digit characters and format for WhatsApp
            clean_number = ''.join(filter(str.isdigit, number))
            if message:
                import urllib.parse
                encoded_message = urllib.parse.quote(message)
                return f"https://wa.me/{clean_number}?text={encoded_message}"
            return f"https://wa.me/{clean_number}"
        return None

    def has_enabled_social_media(self):
        """Check if shop has any enabled social media"""
        social_media = self.get_social_media()
        for platform, data in social_media.items():
            if isinstance(data, dict) and data.get('enabled', False):
                return True
        return False

    def get_currency_symbol(self):
        """Get currency symbol for this shop"""
        currency_symbols = {
            'FCFA': 'FCFA',
            'EUR': '€',
            'USD': '$',
            'GBP': '£',
            'XOF': 'FCFA',  # West African CFA franc
            'XAF': 'FCFA',  # Central African CFA franc
            'MAD': 'MAD',   # Moroccan Dirham
            'TND': 'TND',   # Tunisian Dinar
            'EGP': 'EGP',   # Egyptian Pound
            'ZAR': 'R',     # South African Rand
            'NGN': '₦',     # Nigerian Naira
            'GHS': 'GH₵',   # Ghanaian Cedi
            'KES': 'KSh',   # Kenyan Shilling
            'UGX': 'USh',   # Ugandan Shilling
            'TZS': 'TSh',   # Tanzanian Shilling
            'RWF': 'RWF',   # Rwandan Franc
            'ETB': 'Br',    # Ethiopian Birr
            'DZD': 'DA',    # Algerian Dinar
        }
        return currency_symbols.get(self.currency, self.currency)

    def __repr__(self):
        return f'<Shop {self.name}>'

class Category(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True)
    slug = db.Column(db.String(50), nullable=False, unique=True, index=True)
    description = db.Column(db.Text)
    icon = db.Column(db.String(255))
    parent_id = db.Column(db.Integer, db.ForeignKey('category.id'))
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    # Self-referential relationship for subcategories
    children = db.relationship('Category', backref=db.backref('parent', remote_side=[id]))
    products = db.relationship('Product', backref='category', lazy=True)

    def __repr__(self):
        return f'<Category {self.name}>'

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    shop_id = db.Column(db.Integer, db.ForeignKey('shop.id'), nullable=False)
    category_id = db.Column(db.Integer, db.ForeignKey('category.id'))
    name = db.Column(db.String(200), nullable=False)
    slug = db.Column(db.String(200), nullable=False, index=True)
    description = db.Column(db.Text)
    short_description = db.Column(db.String(500))
    price = db.Column(db.Numeric(10, 2), nullable=False)
    sale_price = db.Column(db.Numeric(10, 2))
    sku = db.Column(db.String(50))
    stock_quantity = db.Column(db.Integer, default=0, nullable=False)
    weight = db.Column(db.Numeric(8, 2))
    dimensions = db.Column(db.JSON)  # Store as JSON: {"length": 10, "width": 5, "height": 3}
    images = db.Column(db.JSON)  # Store image URLs as JSON array
    status = db.Column(db.Enum(ProductStatus), default=ProductStatus.PENDING, nullable=False)
    featured = db.Column(db.Boolean, default=False, nullable=False)
    digital = db.Column(db.Boolean, default=False, nullable=False)
    meta_title = db.Column(db.String(200))
    meta_description = db.Column(db.String(500))
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    cart_items = db.relationship('CartItem', backref='product', lazy=True, cascade='all, delete-orphan')
    order_items = db.relationship('OrderItem', backref='product', lazy=True)
    reviews = db.relationship('Review', backref='product', lazy=True)

    def get_display_price(self):
        return self.sale_price if self.sale_price else self.price

    def is_on_sale(self):
        return self.sale_price is not None and self.sale_price < self.price

    def get_discount_percentage(self):
        if not self.is_on_sale():
            return 0
        return int(((self.price - self.sale_price) / self.price) * 100)

    def is_in_stock(self):
        return self.stock_quantity > 0

    def get_main_image(self):
        if self.images and len(self.images) > 0:
            return self.images[0]
        return '/static/images/no-image.png'

    def get_average_rating(self):
        reviews = Review.query.filter_by(product_id=self.id).all()
        if not reviews:
            return 0
        return sum(review.rating for review in reviews) / len(reviews)

    def get_review_count(self):
        return Review.query.filter_by(product_id=self.id).count()

    def is_boosted(self):
        """Check if product is currently boosted."""
        active_boost = ProductBoost.query.filter(
            ProductBoost.product_id == self.id,
            ProductBoost.expires_at > datetime.utcnow(),
            ProductBoost.is_active == True
        ).first()
        return active_boost is not None

    def get_active_boost(self):
        """Get the active boost for this product."""
        return ProductBoost.query.filter(
            ProductBoost.product_id == self.id,
            ProductBoost.expires_at > datetime.utcnow(),
            ProductBoost.is_active == True
        ).first()

    def can_be_boosted(self):
        """Check if product can be boosted (is active and not already boosted)."""
        return self.status == ProductStatus.ACTIVE and not self.is_boosted()

    def __repr__(self):
        return f'<Product {self.name}>'

class Cart(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    shop_id = db.Column(db.Integer, db.ForeignKey('shop.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    items = db.relationship('CartItem', backref='cart', lazy=True, cascade='all, delete-orphan')

    # Unique constraint: one cart per user per shop
    __table_args__ = (db.UniqueConstraint('user_id', 'shop_id', name='unique_user_shop_cart'),)

    def get_total_items(self):
        return sum(item.quantity for item in self.items)

    def get_total_price(self):
        return sum(item.get_total_price() for item in self.items)

    def __repr__(self):
        return f'<Cart User:{self.user_id} Shop:{self.shop_id}>'

class CartItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    cart_id = db.Column(db.Integer, db.ForeignKey('cart.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    quantity = db.Column(db.Integer, default=1, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Unique constraint: one item per product per cart
    __table_args__ = (db.UniqueConstraint('cart_id', 'product_id', name='unique_cart_product'),)

    def get_total_price(self):
        return self.product.get_display_price() * self.quantity

    def __repr__(self):
        return f'<CartItem {self.product.name} x{self.quantity}>'

class Order(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(50), unique=True, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    shop_id = db.Column(db.Integer, db.ForeignKey('shop.id'), nullable=False)
    status = db.Column(db.Enum(OrderStatus), default=OrderStatus.PENDING, nullable=False)

    # Pricing
    subtotal = db.Column(db.Numeric(10, 2), nullable=False)
    shipping_cost = db.Column(db.Numeric(10, 2), default=0, nullable=False)
    tax_amount = db.Column(db.Numeric(10, 2), default=0, nullable=False)
    commission_amount = db.Column(db.Numeric(10, 2), nullable=False)
    total = db.Column(db.Numeric(10, 2), nullable=False)

    # Shipping information
    shipping_address = db.Column(db.JSON, nullable=False)
    billing_address = db.Column(db.JSON)

    # Payment information
    payment_method = db.Column(db.String(50))
    payment_status = db.Column(db.String(50), default='pending')
    payment_id = db.Column(db.String(255))

    # Tracking
    tracking_number = db.Column(db.String(100))
    tracking_url = db.Column(db.String(500))

    # Notes
    customer_notes = db.Column(db.Text)
    admin_notes = db.Column(db.Text)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    shipped_at = db.Column(db.DateTime)
    delivered_at = db.Column(db.DateTime)

    # Relationships
    items = db.relationship('OrderItem', backref='order', lazy=True, cascade='all, delete-orphan')

    def get_total_items(self):
        return sum(item.quantity for item in self.items)

    def __repr__(self):
        return f'<Order {self.order_number}>'

class OrderItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    price = db.Column(db.Numeric(10, 2), nullable=False)  # Price at time of order
    total = db.Column(db.Numeric(10, 2), nullable=False)  # price * quantity

    def __repr__(self):
        return f'<OrderItem {self.product.name} x{self.quantity}>'

class Review(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=True)
    shop_id = db.Column(db.Integer, db.ForeignKey('shop.id'), nullable=True)
    rating = db.Column(db.Integer, nullable=False)  # 1-5 stars
    title = db.Column(db.String(200))
    comment = db.Column(db.Text)
    is_verified_purchase = db.Column(db.Boolean, default=False, nullable=False)
    is_approved = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Constraints: user can only review a product once, and either product_id or shop_id must be set
    __table_args__ = (
        db.UniqueConstraint('user_id', 'product_id', name='unique_user_product_review'),
        db.UniqueConstraint('user_id', 'shop_id', name='unique_user_shop_review'),
        db.CheckConstraint('(product_id IS NOT NULL) OR (shop_id IS NOT NULL)', name='review_target_check')
    )

    def __repr__(self):
        target = f"Product:{self.product_id}" if self.product_id else f"Shop:{self.shop_id}"
        return f'<Review {target} by User:{self.user_id}>'

class ShortUrl(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    short_code = db.Column(db.String(10), unique=True, nullable=False, index=True)
    original_url = db.Column(db.String(500), nullable=False)
    shop_id = db.Column(db.Integer, db.ForeignKey('shop.id'), nullable=True)  # Optional, for shop URLs
    clicks = db.Column(db.Integer, default=0, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    last_accessed = db.Column(db.DateTime)

    # Relationship
    shop = db.relationship('Shop', backref='short_urls', lazy=True)

    def __repr__(self):
        return f'<ShortUrl {self.short_code} -> {self.original_url}>'

class BlogPost(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    slug = db.Column(db.String(250), unique=True, nullable=False)
    content = db.Column(db.Text, nullable=False)
    excerpt = db.Column(db.Text, nullable=True)
    featured_image = db.Column(db.String(255), nullable=True)
    author_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    category = db.Column(db.String(100), nullable=True)
    tags = db.Column(db.String(500), nullable=True)  # Comma-separated tags
    is_published = db.Column(db.Boolean, default=False)
    is_featured = db.Column(db.Boolean, default=False)
    views = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    published_at = db.Column(db.DateTime, nullable=True)

    # Relationships
    author = db.relationship('User', backref='blog_posts')

    def get_tags_list(self):
        """Return tags as a list."""
        if self.tags:
            return [tag.strip() for tag in self.tags.split(',')]
        return []

    def get_reading_time(self):
        """Estimate reading time in minutes."""
        word_count = len(self.content.split())
        return max(1, word_count // 200)  # Assume 200 words per minute

class SiteStatistic(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.Date, nullable=False, unique=True)
    page_views = db.Column(db.Integer, default=0)
    unique_visitors = db.Column(db.Integer, default=0)
    new_users = db.Column(db.Integer, default=0)
    new_shops = db.Column(db.Integer, default=0)
    new_products = db.Column(db.Integer, default=0)
    orders_count = db.Column(db.Integer, default=0)
    revenue = db.Column(db.Float, default=0.0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class ShopStatistic(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    shop_id = db.Column(db.Integer, db.ForeignKey('shop.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    views = db.Column(db.Integer, default=0)
    unique_visitors = db.Column(db.Integer, default=0)
    product_views = db.Column(db.Integer, default=0)
    orders_count = db.Column(db.Integer, default=0)
    revenue = db.Column(db.Float, default=0.0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    shop = db.relationship('Shop', backref='statistics')

    __table_args__ = (db.UniqueConstraint('shop_id', 'date', name='_shop_date_uc'),)

class SiteSetting(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text, nullable=False)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    @staticmethod
    def get_value(key, default=None):
        """Get setting value by key."""
        setting = SiteSetting.query.filter_by(key=key).first()
        if setting:
            # Try to convert to appropriate type
            value = setting.value
            if value.isdigit():
                return int(value)
            try:
                return float(value)
            except ValueError:
                return value
        return default

    @staticmethod
    def set_value(key, value, description=None):
        """Set setting value by key."""
        setting = SiteSetting.query.filter_by(key=key).first()
        if setting:
            setting.value = str(value)
            setting.updated_at = datetime.utcnow()
        else:
            setting = SiteSetting(key=key, value=str(value), description=description)
            db.session.add(setting)
        return setting

    def __repr__(self):
        return f'<SiteSetting {self.key}: {self.value}>'

class Advertisement(db.Model):
    """Model for admin-managed advertisements."""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)  # Internal identifier
    ad_type = db.Column(db.Enum(AdType), nullable=False)
    status = db.Column(db.Enum(AdStatus), default=AdStatus.DRAFT, nullable=False)

    # Content fields
    title = db.Column(db.String(200))
    content = db.Column(db.Text)  # For text/HTML ads
    image_url = db.Column(db.String(500))  # For image ads
    link_url = db.Column(db.String(500))  # Target URL when clicked

    # Targeting
    target_pages = db.Column(db.JSON)  # Pages where ad should appear
    target_categories = db.Column(db.JSON)  # Category IDs to target

    # Display settings
    display_order = db.Column(db.Integer, default=0)
    max_impressions = db.Column(db.Integer)  # Optional limit
    current_impressions = db.Column(db.Integer, default=0)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    start_date = db.Column(db.DateTime)
    end_date = db.Column(db.DateTime)

    def is_active(self):
        """Check if ad is currently active and within date range."""
        if self.status != AdStatus.ACTIVE:
            return False

        now = datetime.utcnow()
        if self.start_date and now < self.start_date:
            return False
        if self.end_date and now > self.end_date:
            return False
        if self.max_impressions and self.current_impressions >= self.max_impressions:
            return False

        return True

    def increment_impressions(self):
        """Increment impression count."""
        self.current_impressions += 1

    def __repr__(self):
        return f'<Advertisement {self.name}>'

class CreditTransaction(db.Model):
    """Model for tracking credit transactions."""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    amount = db.Column(db.Integer, nullable=False)  # Positive for credit, negative for debit
    transaction_type = db.Column(db.Enum(CreditTransactionType), nullable=False)
    description = db.Column(db.String(500))

    # Payment information (for purchases)
    payment_method = db.Column(db.String(50))
    payment_id = db.Column(db.String(255))
    payment_status = db.Column(db.String(50), default='pending')

    # Approval system
    is_approved = db.Column(db.Boolean, default=False, nullable=False)
    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    approved_at = db.Column(db.DateTime, nullable=True)
    admin_notes = db.Column(db.Text)

    # Related records
    product_boost_id = db.Column(db.Integer, db.ForeignKey('product_boost.id'), nullable=True)

    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    user = db.relationship('User', backref='credit_transactions', foreign_keys=[user_id])
    approver = db.relationship('User', foreign_keys=[approved_by])

    def approve(self, admin_user, notes=None):
        """Approve the credit transaction and add credits to user."""
        if self.is_approved:
            return False

        self.is_approved = True
        self.approved_by = admin_user.id
        self.approved_at = datetime.utcnow()
        self.admin_notes = notes
        self.payment_status = 'completed'

        # Add credits to user balance (only for purchase transactions)
        if self.transaction_type == CreditTransactionType.PURCHASE and self.amount > 0:
            self.user.credit_balance += self.amount

        return True

    def reject(self, admin_user, notes=None):
        """Reject the credit transaction."""
        if self.is_approved:
            return False

        self.payment_status = 'rejected'
        self.approved_by = admin_user.id
        self.approved_at = datetime.utcnow()
        self.admin_notes = notes

        return True

    def __repr__(self):
        return f'<CreditTransaction User:{self.user_id} Amount:{self.amount} Status:{self.payment_status}>'

class ProductBoost(db.Model):
    """Model for tracking boosted products."""
    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    # Boost details
    credits_spent = db.Column(db.Integer, nullable=False)
    duration_days = db.Column(db.Integer, nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    expires_at = db.Column(db.DateTime, nullable=False)

    # Performance tracking
    impressions = db.Column(db.Integer, default=0)
    clicks = db.Column(db.Integer, default=0)

    # Relationships
    product = db.relationship('Product', backref='boosts')
    user = db.relationship('User', backref='product_boosts')
    credit_transaction = db.relationship('CreditTransaction', backref='product_boost', uselist=False)

    def is_expired(self):
        """Check if boost has expired."""
        return datetime.utcnow() > self.expires_at

    def is_currently_active(self):
        """Check if boost is currently active (not expired and active flag is True)."""
        return self.is_active and not self.is_expired()

    def get_remaining_time(self):
        """Get remaining time for the boost."""
        if self.is_expired():
            return None
        return self.expires_at - datetime.utcnow()

    def increment_impressions(self):
        """Increment impression count."""
        self.impressions += 1

    def increment_clicks(self):
        """Increment click count."""
        self.clicks += 1

    def get_ctr(self):
        """Calculate click-through rate."""
        if self.impressions == 0:
            return 0
        return (self.clicks / self.impressions) * 100

    @staticmethod
    def cleanup_expired_boosts():
        """Deactivate expired boosts."""
        expired_boosts = ProductBoost.query.filter(
            ProductBoost.expires_at < datetime.utcnow(),
            ProductBoost.is_active == True
        ).all()

        for boost in expired_boosts:
            boost.is_active = False

        return len(expired_boosts)

    def __repr__(self):
        return f'<ProductBoost Product:{self.product_id} User:{self.user_id}>'

class CreditPackage(db.Model):
    """Model for admin-configured credit packages."""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    credits = db.Column(db.Integer, nullable=False)
    price_eur = db.Column(db.Numeric(10, 2), nullable=False)
    price_fcfa = db.Column(db.Numeric(10, 2), nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    display_order = db.Column(db.Integer, default=0)
    bonus_credits = db.Column(db.Integer, default=0)  # Extra credits for bulk purchases
    description = db.Column(db.String(500))

    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def get_total_credits(self):
        """Get total credits including bonus."""
        return self.credits + self.bonus_credits

    def get_price_per_credit_eur(self):
        """Calculate price per credit in EUR."""
        total_credits = self.get_total_credits()
        if total_credits == 0:
            return 0
        return float(self.price_eur) / total_credits

    def __repr__(self):
        return f'<CreditPackage {self.name}: {self.get_total_credits()} credits>'

class EmailTemplate(db.Model):
    """Model for email templates."""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    email_type = db.Column(db.Enum(EmailType), nullable=False)
    subject = db.Column(db.String(200), nullable=False)
    html_content = db.Column(db.Text, nullable=False)
    text_content = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    variables = db.Column(db.JSON)  # Available template variables

    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<EmailTemplate {self.name}>'

class EmailQueue(db.Model):
    """Model for email queue."""
    id = db.Column(db.Integer, primary_key=True)
    recipient_email = db.Column(db.String(120), nullable=False)
    recipient_name = db.Column(db.String(100))
    subject = db.Column(db.String(200), nullable=False)
    html_content = db.Column(db.Text, nullable=False)
    text_content = db.Column(db.Text)
    email_type = db.Column(db.Enum(EmailType), nullable=False)
    status = db.Column(db.Enum(EmailStatus), default=EmailStatus.PENDING, nullable=False)

    # Optional relationships
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    shop_id = db.Column(db.Integer, db.ForeignKey('shop.id'), nullable=True)
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'), nullable=True)

    # Sending details
    attempts = db.Column(db.Integer, default=0, nullable=False)
    max_attempts = db.Column(db.Integer, default=3, nullable=False)
    error_message = db.Column(db.Text)
    sent_at = db.Column(db.DateTime)

    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref='email_queue', lazy=True)
    shop = db.relationship('Shop', backref='email_queue', lazy=True)
    order = db.relationship('Order', backref='email_queue', lazy=True)

    def __repr__(self):
        return f'<EmailQueue {self.recipient_email} - {self.email_type.value}>'

class NewsletterSubscriber(db.Model):
    """Model for newsletter subscribers."""
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), nullable=False, unique=True, index=True)
    name = db.Column(db.String(100))
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)  # Optional link to user

    # Subscription preferences
    interests = db.Column(db.JSON)  # Categories of interest
    frequency = db.Column(db.String(20), default='weekly')  # weekly, monthly

    subscribed_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    unsubscribed_at = db.Column(db.DateTime)

    # Relationships
    user = db.relationship('User', backref='newsletter_subscription', lazy=True)

    def __repr__(self):
        return f'<NewsletterSubscriber {self.email}>'

class Newsletter(db.Model):
    """Model for newsletter campaigns."""
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    subject = db.Column(db.String(200), nullable=False)
    html_content = db.Column(db.Text, nullable=False)
    text_content = db.Column(db.Text)

    # Campaign details
    status = db.Column(db.String(20), default='draft')  # draft, scheduled, sending, sent
    target_audience = db.Column(db.String(50), default='all')  # all, vendors, shoppers, specific
    scheduled_at = db.Column(db.DateTime)
    sent_at = db.Column(db.DateTime)

    # Statistics
    total_recipients = db.Column(db.Integer, default=0)
    sent_count = db.Column(db.Integer, default=0)
    failed_count = db.Column(db.Integer, default=0)

    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    creator = db.relationship('User', backref='newsletters', lazy=True)

    def __repr__(self):
        return f'<Newsletter {self.title}>'
