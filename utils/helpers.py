import os
import uuid
import re
from werkzeug.utils import secure_filename
from PIL import Image
from config import Config

def generate_slug(text):
    """Generate a URL-friendly slug from text."""
    # Convert to lowercase and replace spaces with hyphens
    slug = re.sub(r'[^\w\s-]', '', text.lower())
    slug = re.sub(r'[-\s]+', '-', slug)
    return slug.strip('-')

def allowed_file(filename):
    """Check if file extension is allowed."""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in Config.ALLOWED_EXTENSIONS

def save_uploaded_file(file, folder, max_size=(800, 800)):
    """Save uploaded file with resizing."""
    if file and allowed_file(file.filename):
        # Generate unique filename
        filename = secure_filename(file.filename)
        name, ext = os.path.splitext(filename)
        unique_filename = f"{name}_{uuid.uuid4().hex[:8]}{ext}"

        # Create upload directory if it doesn't exist
        upload_dir = os.path.join('static/uploads', folder)
        os.makedirs(upload_dir, exist_ok=True)

        # Save file
        file_path = os.path.join(upload_dir, unique_filename)
        file.save(file_path)

        # Resize image if it's an image file
        try:
            with Image.open(file_path) as img:
                # Convert RGBA to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')

                # Resize maintaining aspect ratio
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
                img.save(file_path, optimize=True, quality=85)
        except Exception as e:
            # If it's not an image or resize fails, keep original
            pass

        return unique_filename

    return None

def format_currency(amount, currency='USD'):
    """Format currency amount."""
    if currency == 'USD':
        return f"${amount:.2f}"
    elif currency == 'NGN':
        return f"₦{amount:,.2f}"
    elif currency == 'KES':
        return f"KSh {amount:,.2f}"
    elif currency == 'GHS':
        return f"GH₵ {amount:.2f}"
    elif currency == 'ZAR':
        return f"R {amount:.2f}"
    elif currency == 'EGP':
        return f"E£ {amount:.2f}"
    else:
        return f"{currency} {amount:.2f}"

def generate_order_number():
    """Generate unique order number."""
    import time
    timestamp = str(int(time.time()))
    random_part = uuid.uuid4().hex[:6].upper()
    return f"AFM-{timestamp[-6:]}-{random_part}"

def calculate_commission(amount, tier):
    """Calculate commission based on tier."""
    commission_rate = Config.COMMISSION_RATES.get(tier, 0.15)
    return float(amount) * commission_rate

def get_file_url(filename, folder):
    """Get URL for uploaded file."""
    if filename:
        return f"/static/uploads/{folder}/{filename}"
    return None

def truncate_text(text, length=100):
    """Truncate text to specified length."""
    if len(text) <= length:
        return text
    return text[:length].rsplit(' ', 1)[0] + '...'

def generate_next_short_code():
    """Generate the next sequential numeric short code starting from 1."""
    from models import db, ShortUrl

    # Get the highest existing numeric short code
    existing_codes = db.session.query(ShortUrl.short_code).all()

    max_number = 0
    for (code,) in existing_codes:
        try:
            # Try to convert to integer
            num = int(code)
            if num > max_number:
                max_number = num
        except ValueError:
            # Skip non-numeric codes (legacy codes)
            continue

    # Return the next number
    return str(max_number + 1)

def create_short_url(original_url, shop_id=None):
    """Create a short URL for the given original URL."""
    from models import db, ShortUrl

    # Check if short URL already exists for this shop
    if shop_id:
        existing = ShortUrl.query.filter_by(shop_id=shop_id).first()
        if existing:
            return existing.short_code

    # Generate next sequential numeric code
    short_code = generate_next_short_code()

    # Create short URL record
    short_url = ShortUrl(
        short_code=short_code,
        original_url=original_url,
        shop_id=shop_id
    )

    db.session.add(short_url)
    db.session.commit()

    return short_code

def get_short_url_for_shop(shop):
    """Get or create a short URL for a shop."""
    from flask import url_for

    # Check if shop already has a short URL
    if shop.short_urls:
        return shop.short_urls[0].short_code

    # Create new short URL
    original_url = url_for('shop.view', slug=shop.slug, _external=True)
    return create_short_url(original_url, shop.id)

def get_star_rating_html(rating, max_rating=5):
    """Generate HTML for star rating display."""
    full_stars = int(rating)
    half_star = rating - full_stars >= 0.5
    empty_stars = max_rating - full_stars - (1 if half_star else 0)

    html = ''

    # Full stars
    for _ in range(full_stars):
        html += '<i class="fas fa-star text-warning"></i>'

    # Half star
    if half_star:
        html += '<i class="fas fa-star-half-alt text-warning"></i>'

    # Empty stars
    for _ in range(empty_stars):
        html += '<i class="far fa-star text-warning"></i>'

    return html

def validate_african_phone(phone, country_code=None):
    """Validate African phone numbers."""
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone)

    # Common African country codes and their patterns
    patterns = {
        'NG': r'^(234|0)?[789][01]\d{8}$',  # Nigeria
        'KE': r'^(254|0)?[17]\d{8}$',       # Kenya
        'GH': r'^(233|0)?[2459]\d{8}$',     # Ghana
        'ZA': r'^(27|0)?[1-8]\d{8}$',      # South Africa
        'EG': r'^(20|0)?1[0125]\d{8}$',    # Egypt
    }

    if country_code and country_code in patterns:
        return bool(re.match(patterns[country_code], digits_only))

    # Check against all patterns if no specific country code
    for pattern in patterns.values():
        if re.match(pattern, digits_only):
            return True

    return False

def get_pagination_info(pagination):
    """Get pagination information for templates."""
    return {
        'has_prev': pagination.has_prev,
        'prev_num': pagination.prev_num,
        'has_next': pagination.has_next,
        'next_num': pagination.next_num,
        'page': pagination.page,
        'pages': pagination.pages,
        'per_page': pagination.per_page,
        'total': pagination.total,
        'items': pagination.items
    }
