from functools import wraps
from flask import abort, flash, redirect, url_for
from flask_login import current_user
from models import UserRole

def admin_required(f):
    """Decorator to require admin role."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('auth.login'))
        
        if current_user.role != UserRole.ADMIN:
            flash('You do not have permission to access this page.', 'error')
            abort(403)
        
        return f(*args, **kwargs)
    return decorated_function

def vendor_required(f):
    """Decorator to require vendor role."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('auth.login'))
        
        if current_user.role not in [UserRole.VENDOR, UserRole.ADMIN]:
            flash('You need to be a vendor to access this page.', 'error')
            return redirect(url_for('main.index'))
        
        return f(*args, **kwargs)
    return decorated_function

def tier_required(required_tier):
    """Decorator to require specific user tier."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return redirect(url_for('auth.login'))
            
            tier_hierarchy = {'free': 0, 'premium': 1, 'gold': 2}
            user_tier_level = tier_hierarchy.get(current_user.tier.value, 0)
            required_tier_level = tier_hierarchy.get(required_tier, 0)
            
            if user_tier_level < required_tier_level:
                flash(f'This feature requires {required_tier.title()} tier or higher.', 'error')
                return redirect(url_for('main.index'))
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator
