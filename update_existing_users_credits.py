#!/usr/bin/env python3
"""
Update existing users to have 100 credits if they have 0 credits
"""

import os
import sys

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, User, CreditTransaction, CreditTransactionType

def update_existing_users_credits():
    """Give 100 credits to existing users who have 0 credits."""
    app = create_app()
    
    with app.app_context():
        print("💰 Updating Existing Users with Welcome Credits...")
        print("=" * 50)
        
        try:
            # Get all users with 0 credits
            users_with_zero_credits = User.query.filter_by(credit_balance=0).all()
            
            if not users_with_zero_credits:
                print("✓ All users already have credits!")
                return
            
            print(f"Found {len(users_with_zero_credits)} users with 0 credits")
            
            updated_count = 0
            for user in users_with_zero_credits:
                # Update credit balance
                user.credit_balance = 100
                
                # Create welcome credit transaction
                welcome_transaction = CreditTransaction(
                    user_id=user.id,
                    amount=100,
                    transaction_type=CreditTransactionType.PURCHASE,
                    description="Crédits de bienvenue gratuits (mise à jour)",
                    payment_status="completed"
                )
                db.session.add(welcome_transaction)
                
                updated_count += 1
                print(f"✓ Updated {user.email} - Added 100 credits")
            
            db.session.commit()
            
            print(f"\n🎉 Successfully updated {updated_count} users!")
            print(f"All users now have at least 100 credits to test the boost system.")
            
            # Display summary
            total_users = User.query.count()
            users_with_credits = User.query.filter(User.credit_balance > 0).count()
            
            print(f"\n📊 Summary:")
            print(f"• Total users: {total_users}")
            print(f"• Users with credits: {users_with_credits}")
            print(f"• Users updated: {updated_count}")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error updating users: {e}")
            raise

if __name__ == '__main__':
    update_existing_users_credits()
