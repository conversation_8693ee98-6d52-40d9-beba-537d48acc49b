#!/usr/bin/env python3
"""
Quick database fix script for deployment
Run this script to fix the "no such table: user" error
"""

import os
import sys
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def fix_database():
    """Fix database issues by recreating tables and essential data"""
    try:
        print("🔄 Importing application modules...")
        from app import create_app
        from models import (
            db, User, UserRole, UserTier, Shop, ShopStatus, Product, ProductStatus,
            Category, Order, OrderStatus, CreditPackage, EmailTemplate, EmailType,
            NewsletterSubscriber, SiteSetting
        )
        
        print("✅ Modules imported successfully!")
        
        app = create_app()
        
        with app.app_context():
            print("🔄 Creating database tables...")
            
            # Create all tables
            db.create_all()
            print("✅ Database tables created!")
            
            # Check if admin user exists
            admin = User.query.filter_by(email='<EMAIL>').first()
            if not admin:
                print("🔄 Creating admin user...")
                admin = User(
                    email='<EMAIL>',
                    password_hash=generate_password_hash('admin123'),
                    first_name='Admin',
                    last_name='Afroly',
                    phone='+44 7951 658211',
                    role=UserRole.ADMIN,
                    tier=UserTier.GOLD,
                    is_active=True,
                    email_verified=True,
                    credit_balance=10000,
                    expiration_date=datetime.utcnow() + timedelta(days=365)
                )
                db.session.add(admin)
                print("✅ Admin user created!")
            else:
                print("ℹ️ Admin user already exists")
            
            # Create basic categories if they don't exist
            basic_categories = [
                {'name': 'Mode Africaine', 'description': 'Vêtements traditionnels africains'},
                {'name': 'Artisanat', 'description': 'Objets d\'art africain'},
                {'name': 'Beauté', 'description': 'Produits de beauté naturels'},
                {'name': 'Alimentation', 'description': 'Produits alimentaires africains'},
                {'name': 'Électronique', 'description': 'Appareils électroniques'}
            ]
            
            for cat_data in basic_categories:
                category = Category.query.filter_by(name=cat_data['name']).first()
                if not category:
                    category = Category(**cat_data)
                    db.session.add(category)
            
            print("✅ Basic categories created!")
            
            # Create basic site settings
            basic_settings = [
                {'key': 'site_name', 'value': 'Afroly.org'},
                {'key': 'contact_email', 'value': '<EMAIL>'},
                {'key': 'whatsapp_number', 'value': '+44 7951 658211'},
                {'key': 'whatsapp_enabled', 'value': 'true'}
            ]
            
            for setting_data in basic_settings:
                setting = SiteSetting.query.filter_by(key=setting_data['key']).first()
                if not setting:
                    setting = SiteSetting(**setting_data)
                    db.session.add(setting)
            
            print("✅ Basic settings created!")
            
            # Commit changes
            db.session.commit()
            print("✅ All changes committed!")
            
            print("\n🎉 Database fix completed successfully!")
            print("\n🔐 Admin login: <EMAIL> / admin123")
            print("🌐 Your application should now work properly!")
            
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        print("Make sure you're in the correct directory and all dependencies are installed.")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False
    
    return True

if __name__ == '__main__':
    print("🔧 Afroly.org Database Fix Script")
    print("=" * 40)
    
    if fix_database():
        print("\n✅ Database fix completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Database fix failed!")
        sys.exit(1)
