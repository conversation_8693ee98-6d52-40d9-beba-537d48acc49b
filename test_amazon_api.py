#!/usr/bin/env python3

import requests
import json
import time

def test_amazon_endpoint(endpoint, params=None):
    """Test a specific Amazon API endpoint"""

    base_url = "https://amazon-product-search-api.p.rapidapi.com"
    url = f"{base_url}{endpoint}"

    headers = {
        'x-rapidapi-host': 'amazon-product-search-api.p.rapidapi.com',
        'x-rapidapi-key': '**************************************************'
    }

    print(f"🔍 Testing endpoint: {endpoint}")
    print(f"📡 URL: {url}")
    if params:
        print(f"📋 Params: {params}")
    print()

    try:
        start_time = time.time()

        response = requests.get(url, headers=headers, params=params, timeout=10)

        end_time = time.time()
        duration = end_time - start_time

        print(f"✅ Response received in {duration:.2f} seconds")
        print(f"📡 Status Code: {response.status_code}")
        print()

        if response.status_code == 200:
            print("🎉 SUCCESS!")
            try:
                data = response.json()
                print(f"📦 JSON Response: {json.dumps(data, indent=2)[:500]}...")
                return True
            except:
                print(f"📄 Text Response: {response.text[:200]}...")
                return True
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"📄 Response: {response.text[:200]}...")
            return False

    except requests.exceptions.Timeout:
        print("⏰ Request timed out")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"🔌 Connection error: {e}")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
        return False
    except Exception as e:
        print(f"💥 Unexpected error: {e}")
        return False

def test_amazon_api():
    """Test various Amazon API endpoints"""

    print("🔍 Testing Amazon Product Search API endpoints...")
    print("=" * 50)

    # Test common endpoints
    endpoints_to_test = [
        "/",
        "/search",
        "/products",
        "/product",
        "/api/search",
        "/api/products",
        "/v1/search",
        "/v1/products"
    ]

    working_endpoints = []

    for endpoint in endpoints_to_test:
        print(f"\n📍 Testing: {endpoint}")
        print("-" * 30)

        # Test basic endpoint
        success = test_amazon_endpoint(endpoint)
        if success:
            working_endpoints.append(endpoint)

        # If it's a search endpoint, also test with parameters
        if "search" in endpoint and success:
            print(f"\n🔍 Testing {endpoint} with search parameters...")
            test_params = {"q": "phone", "page": 1}
            test_amazon_endpoint(endpoint, test_params)

    print("\n" + "=" * 50)
    print("📊 SUMMARY:")
    print(f"✅ Working endpoints: {working_endpoints}")
    print(f"📈 Success rate: {len(working_endpoints)}/{len(endpoints_to_test)}")

if __name__ == "__main__":
    test_amazon_api()
