#!/usr/bin/env python3
"""
Script to fix currency issues - convert EUR prices to FCFA and update display
"""

from app import create_app
from models import db, Product, Shop

def fix_currency_issues():
    """Fix currency display and conversion issues"""
    
    app = create_app()
    
    with app.app_context():
        print("💰 Fixing currency issues...")
        
        # Conversion rate: 1 EUR = 655 FCFA (as defined in config)
        EUR_TO_FCFA = 655
        
        # Get all products
        products = Product.query.all()
        shops = Shop.query.all()
        
        print(f"\n📊 Current Status:")
        print(f"Products: {len(products)}")
        print(f"Shops: {len(shops)}")
        
        # First, ensure all shops use FCFA as default currency
        print(f"\n🏪 Updating shop currencies to FCFA...")
        for shop in shops:
            if shop.currency != 'FCFA':
                print(f"  {shop.name}: {shop.currency} → FCFA")
                shop.currency = 'FCFA'
            else:
                print(f"  {shop.name}: Already FCFA ✓")
        
        # Convert product prices from EUR to FCFA if needed
        print(f"\n💰 Converting product prices to FCFA...")
        for product in products:
            # Check if price seems to be in EUR (typically < 1000)
            if product.price < 1000:
                # Convert to FCFA
                old_price = product.price
                product.price = round(product.price * EUR_TO_FCFA, 2)
                
                # Convert sale price if exists
                if product.sale_price:
                    old_sale_price = product.sale_price
                    product.sale_price = round(product.sale_price * EUR_TO_FCFA, 2)
                    print(f"  {product.name}:")
                    print(f"    Price: {old_price} EUR → {product.price} FCFA")
                    print(f"    Sale: {old_sale_price} EUR → {product.sale_price} FCFA")
                else:
                    print(f"  {product.name}:")
                    print(f"    Price: {old_price} EUR → {product.price} FCFA")
            else:
                print(f"  {product.name}: Already in FCFA ({product.price}) ✓")
        
        # Commit changes
        db.session.commit()
        print(f"\n✅ Currency fixes applied!")
        
        # Show final status
        print(f"\n📋 FINAL PRICES:")
        print("=" * 50)
        for product in products:
            shop = product.shop
            if product.sale_price:
                print(f"📦 {product.name}")
                print(f"   Shop: {shop.name} ({shop.currency})")
                print(f"   Price: {product.price:,.0f} {shop.currency}")
                print(f"   Sale: {product.sale_price:,.0f} {shop.currency}")
            else:
                print(f"📦 {product.name}")
                print(f"   Shop: {shop.name} ({shop.currency})")
                print(f"   Price: {product.price:,.0f} {shop.currency}")
            print()

if __name__ == "__main__":
    fix_currency_issues()
