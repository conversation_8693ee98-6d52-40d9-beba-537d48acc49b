#!/usr/bin/env python3
"""
Add payment methods using Flask app context.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def add_payment_methods():
    """Add payment methods to shops using Flask app context."""
    try:
        from app import create_app
        from models import db, Shop
        
        app = create_app()
        
        with app.app_context():
            # Get all shops
            shops = Shop.query.all()
            print(f"Found {len(shops)} shops in the database")
            
            if not shops:
                print("No shops found. Creating a demo shop first...")
                # You might want to create a demo shop here
                return
            
            for shop in shops:
                print(f"\nUpdating shop: {shop.name} ({shop.country})")
                
                # Initialize payment_info if it doesn't exist
                if not shop.payment_info:
                    shop.payment_info = {}
                
                # Create payment methods based on country
                if shop.country == 'Sénégal':
                    payment_methods = [
                        {
                            'type': 'orange_money',
                            'details': {
                                'phone_number': '+************',
                                'account_name': shop.name
                            }
                        },
                        {
                            'type': 'wave',
                            'details': {
                                'phone_number': '+************',
                                'account_name': shop.name
                            }
                        },
                        {
                            'type': 'bank_account',
                            'details': {
                                'bank_name': 'Banque Atlantique Sénégal',
                                'account_number': '***********',
                                'account_name': shop.name,
                                'swift_code': 'CBAOSNDA'
                            }
                        }
                    ]
                elif shop.country == 'Cameroun':
                    payment_methods = [
                        {
                            'type': 'mtn_money',
                            'details': {
                                'phone_number': '+************',
                                'account_name': shop.name
                            }
                        },
                        {
                            'type': 'orange_money',
                            'details': {
                                'phone_number': '+************',
                                'account_name': shop.name
                            }
                        },
                        {
                            'type': 'bank_account',
                            'details': {
                                'bank_name': 'Afriland First Bank',
                                'account_number': '***********',
                                'account_name': shop.name,
                                'swift_code': 'AFRCCMCX'
                            }
                        }
                    ]
                else:
                    # Default payment methods for other countries
                    payment_methods = [
                        {
                            'type': 'mtn_money',
                            'details': {
                                'phone_number': '+************',
                                'account_name': shop.name
                            }
                        },
                        {
                            'type': 'bank_account',
                            'details': {
                                'bank_name': 'Banque Locale',
                                'account_number': '***********',
                                'account_name': shop.name,
                                'swift_code': 'LOCAAFXX'
                            }
                        }
                    ]
                
                # Add payment methods to shop
                shop.payment_info['methods'] = payment_methods
                
                print(f"  Added {len(payment_methods)} payment methods:")
                for method in payment_methods:
                    print(f"    - {method['type']}")
            
            # Commit all changes
            db.session.commit()
            print(f"\n✅ Successfully updated payment methods for {len(shops)} shops!")
            
            # Verify the update
            print("\n📊 Verification:")
            for shop in shops:
                methods = shop.get_payment_methods()
                print(f"  {shop.name}: {len(methods)} payment methods")
                for method in methods:
                    print(f"    - {method.get('type', 'Unknown')}")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    add_payment_methods()
