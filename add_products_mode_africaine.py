#!/usr/bin/env python3
"""
<PERSON>ript to add 9 new products to Mode Africaine Élégante shop
"""

from app import create_app
from models import db, Shop, Product, Category, ProductStatus
from utils.helpers import generate_slug
import random

def add_products_mode_africaine():
    """Add 9 new products to Mode Africaine Élégante shop"""
    app = create_app()
    
    with app.app_context():
        try:
            # Get the Mode Africaine Élégante shop
            shop = Shop.query.filter_by(name='Mode Africaine Élégante').first()
            if not shop:
                print("❌ Shop 'Mode Africaine Élégante' not found")
                return
            
            # Get or create category
            category = Category.query.filter_by(name='Mode & Vêtements').first()
            if not category:
                category = Category(
                    name='Mode & Vêtements',
                    slug='mode-vetements',
                    description='Vêtements et accessoires de mode africaine'
                )
                db.session.add(category)
                db.session.commit()
            
            # New products data
            products_data = [
                {
                    'name': 'Robe Wax Élégante Femme',
                    'description': 'Magnifique robe en tissu wax authentique, parfaite pour les occasions spéciales. Coupe moderne avec motifs traditionnels africains.',
                    'short_description': 'Robe wax élégante avec motifs traditionnels africains',
                    'price': 45000,  # 45,000 FCFA
                    'sale_price': 38000,  # 38,000 FCFA
                    'stock_quantity': 15,
                    'images': [
                        'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500&h=600&fit=crop',
                        'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=500&h=600&fit=crop'
                    ]
                },
                {
                    'name': 'Chemise Homme Bazin Brodé',
                    'description': 'Chemise homme en bazin riche avec broderies dorées. Idéale pour les cérémonies et événements traditionnels.',
                    'short_description': 'Chemise bazin brodé pour homme, style traditionnel',
                    'price': 35000,
                    'stock_quantity': 12,
                    'images': [
                        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=600&fit=crop',
                        'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=500&h=600&fit=crop'
                    ]
                },
                {
                    'name': 'Ensemble Complet Femme Kente',
                    'description': 'Ensemble deux pièces en tissu Kente authentique du Ghana. Comprend une blouse et une jupe assortie.',
                    'short_description': 'Ensemble Kente authentique, blouse et jupe',
                    'price': 65000,
                    'sale_price': 55000,
                    'stock_quantity': 8,
                    'images': [
                        'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=500&h=600&fit=crop',
                        'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=500&h=600&fit=crop'
                    ]
                },
                {
                    'name': 'Pantalon Homme Bogolan',
                    'description': 'Pantalon traditionnel en tissu bogolan du Mali. Confortable et stylé pour un look afro-contemporain.',
                    'short_description': 'Pantalon bogolan traditionnel du Mali',
                    'price': 28000,
                    'stock_quantity': 20,
                    'images': [
                        'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=500&h=600&fit=crop',
                        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=600&fit=crop'
                    ]
                },
                {
                    'name': 'Robe Longue Ankara Moderne',
                    'description': 'Robe longue en tissu Ankara avec coupe moderne. Parfaite pour un style afro-chic au quotidien.',
                    'short_description': 'Robe Ankara moderne, style afro-chic',
                    'price': 42000,
                    'sale_price': 36000,
                    'stock_quantity': 18,
                    'images': [
                        'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=500&h=600&fit=crop',
                        'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500&h=600&fit=crop'
                    ]
                },
                {
                    'name': 'Veste Homme Wax Casual',
                    'description': 'Veste décontractée en tissu wax pour homme. Style moderne avec touches traditionnelles africaines.',
                    'short_description': 'Veste wax casual, style moderne africain',
                    'price': 38000,
                    'stock_quantity': 10,
                    'images': [
                        'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=500&h=600&fit=crop',
                        'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=500&h=600&fit=crop'
                    ]
                },
                {
                    'name': 'Jupe Plissée Tissu Traditionnel',
                    'description': 'Jupe plissée en tissu traditionnel africain. Élégante et confortable pour toutes occasions.',
                    'short_description': 'Jupe plissée en tissu traditionnel africain',
                    'price': 25000,
                    'sale_price': 22000,
                    'stock_quantity': 25,
                    'images': [
                        'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=500&h=600&fit=crop',
                        'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=500&h=600&fit=crop'
                    ]
                },
                {
                    'name': 'Combinaison Femme Wax Chic',
                    'description': 'Combinaison élégante en tissu wax pour femme. Design moderne avec imprimés africains authentiques.',
                    'short_description': 'Combinaison wax chic, design moderne',
                    'price': 48000,
                    'stock_quantity': 14,
                    'images': [
                        'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500&h=600&fit=crop',
                        'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=500&h=600&fit=crop'
                    ]
                },
                {
                    'name': 'Costume Traditionnel Complet',
                    'description': 'Costume traditionnel complet pour homme avec pantalon, chemise et veste assortis en tissu africain.',
                    'short_description': 'Costume traditionnel complet, 3 pièces',
                    'price': 85000,
                    'sale_price': 75000,
                    'stock_quantity': 6,
                    'featured': True,
                    'images': [
                        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=600&fit=crop',
                        'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=500&h=600&fit=crop'
                    ]
                }
            ]
            
            # Add products
            added_count = 0
            for product_data in products_data:
                # Generate unique slug
                base_slug = generate_slug(product_data['name'])
                slug = f"{base_slug}-{shop.slug}"
                
                # Check if product already exists
                existing = Product.query.filter_by(slug=slug).first()
                if existing:
                    print(f"⚠️  Product '{product_data['name']}' already exists, skipping...")
                    continue
                
                # Create product
                product = Product(
                    shop_id=shop.id,
                    category_id=category.id,
                    name=product_data['name'],
                    slug=slug,
                    description=product_data['description'],
                    short_description=product_data['short_description'],
                    price=product_data['price'],
                    sale_price=product_data.get('sale_price'),
                    stock_quantity=product_data['stock_quantity'],
                    images=product_data['images'],
                    status=ProductStatus.ACTIVE,
                    featured=product_data.get('featured', False),
                    sku=f"MAE-{random.randint(1000, 9999)}"
                )
                
                db.session.add(product)
                added_count += 1
                
                print(f"✅ Added: {product_data['name']}")
                print(f"   Price: {product_data['price']:,} FCFA" + 
                      (f" (Sale: {product_data['sale_price']:,} FCFA)" if product_data.get('sale_price') else ""))
                print(f"   Stock: {product_data['stock_quantity']} units")
                print(f"   Images: {len(product_data['images'])} images")
                print()
            
            db.session.commit()
            
            # Final summary
            total_products = Product.query.filter_by(shop_id=shop.id).count()
            print(f"🎉 Successfully added {added_count} new products!")
            print(f"📊 Total products in {shop.name}: {total_products}")
            
            # Display all products in the shop
            print(f"\n📋 All products in {shop.name}:")
            all_products = Product.query.filter_by(shop_id=shop.id).all()
            for i, product in enumerate(all_products, 1):
                status_icon = "🔥" if product.featured else "📦"
                sale_info = f" (Sale: {product.sale_price:,})" if product.sale_price else ""
                print(f"   {i:2d}. {status_icon} {product.name}")
                print(f"       💰 {product.price:,} FCFA{sale_info}")
                print(f"       📦 Stock: {product.stock_quantity}")
                
        except Exception as e:
            print(f"❌ Error adding products: {e}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    add_products_mode_africaine()
