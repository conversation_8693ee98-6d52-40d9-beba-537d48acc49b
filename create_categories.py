#!/usr/bin/env python3
"""
<PERSON>ript to create product categories for Afroly.org
"""

from app import create_app
from models import db, Category
from utils.helpers import generate_slug

def create_categories():
    """Create product categories for the African marketplace"""
    
    app = create_app()
    
    with app.app_context():
        print("🏷️  Creating product categories...")
        
        # Define categories with African market focus
        categories_data = [
            {
                'name': 'Mode & Vêtements',
                'description': 'Vêtements traditionnels et modernes africains',
                'icon': 'fas fa-tshirt'
            },
            {
                'name': 'Électronique',
                'description': 'Smartphones, ordinateurs, accessoires tech',
                'icon': 'fas fa-mobile-alt'
            },
            {
                'name': 'Arts & Artisanat',
                'description': 'Artisanat traditionnel africain, sculptures, bijoux',
                'icon': 'fas fa-palette'
            },
            {
                'name': 'Beauté & Cosmétiques',
                'description': 'Produits de beauté, soins naturels africains',
                'icon': 'fas fa-spa'
            },
            {
                'name': '<PERSON>son & Décoration',
                'description': 'Mobilier, décoration, objets pour la maison',
                'icon': 'fas fa-home'
            },
            {
                'name': 'Alimentation',
                'description': 'Produits alimentaires africains, épices, thés',
                'icon': 'fas fa-utensils'
            },
            {
                'name': 'Livres & Éducation',
                'description': 'Livres, matériel éducatif, littérature africaine',
                'icon': 'fas fa-book'
            },
            {
                'name': 'Sports & Loisirs',
                'description': 'Équipements sportifs, jeux, loisirs',
                'icon': 'fas fa-futbol'
            },
            {
                'name': 'Santé & Bien-être',
                'description': 'Produits de santé, médecine traditionnelle',
                'icon': 'fas fa-heartbeat'
            },
            {
                'name': 'Enfants & Bébés',
                'description': 'Vêtements, jouets, produits pour enfants',
                'icon': 'fas fa-baby'
            },
            {
                'name': 'Automobile',
                'description': 'Pièces auto, accessoires véhicules',
                'icon': 'fas fa-car'
            },
            {
                'name': 'Services',
                'description': 'Services numériques, consultations, formations',
                'icon': 'fas fa-handshake'
            },
            {
                'name': 'Autres',
                'description': 'Produits divers et autres articles',
                'icon': 'fas fa-box'
            }
        ]
        
        created_count = 0
        
        for cat_data in categories_data:
            # Check if category already exists
            existing = Category.query.filter_by(name=cat_data['name']).first()
            if existing:
                print(f"⚠️  Category '{cat_data['name']}' already exists")
                continue
            
            # Create new category
            category = Category(
                name=cat_data['name'],
                slug=generate_slug(cat_data['name']),
                description=cat_data['description'],
                icon=cat_data['icon'],
                is_active=True
            )
            
            db.session.add(category)
            created_count += 1
            print(f"✅ Created category: {cat_data['name']}")
        
        # Commit all changes
        db.session.commit()
        
        print(f"\n🎉 Successfully created {created_count} categories!")
        
        # Show all categories
        all_categories = Category.query.all()
        print(f"\n📋 ALL CATEGORIES ({len(all_categories)} total):")
        print("=" * 50)
        for cat in all_categories:
            print(f"🏷️  {cat.name}")
            print(f"   Slug: {cat.slug}")
            print(f"   Icon: {cat.icon}")
            print(f"   Active: {cat.is_active}")
            print()

if __name__ == "__main__":
    create_categories()
