#!/usr/bin/env python3
"""
Add currency column to shops table and set default FCFA for existing shops
"""

import sqlite3
import os

def add_currency_column():
    """Add currency column to shops table"""
    
    # Database path
    db_path = 'instance/afromall.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file not found!")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if currency column already exists
        cursor.execute("PRAGMA table_info(shop)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'currency' in columns:
            print("✅ Currency column already exists!")
            
            # Check current currency values
            cursor.execute("SELECT DISTINCT currency FROM shop")
            currencies = cursor.fetchall()
            print(f"📊 Current currency values: {currencies}")
            
            # Count shops by currency
            cursor.execute("SELECT currency, COUNT(*) FROM shop GROUP BY currency")
            currency_counts = cursor.fetchall()
            print(f"📈 Shop counts by currency: {currency_counts}")
            
            conn.close()
            return True
        
        print("🔧 Adding currency column to shop table...")
        
        # Add currency column with default value FCFA
        cursor.execute("ALTER TABLE shop ADD COLUMN currency VARCHAR(10) NOT NULL DEFAULT 'FCFA'")
        
        # Update all existing shops to have FCFA currency (default for French-speaking Africa)
        cursor.execute("UPDATE shop SET currency = 'FCFA' WHERE currency IS NULL OR currency = ''")
        
        # Commit changes
        conn.commit()
        
        # Verify the addition
        cursor.execute("PRAGMA table_info(shop)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"📊 Shop table columns: {columns}")
        
        # Check currency values
        cursor.execute("SELECT DISTINCT currency FROM shop")
        currencies = cursor.fetchall()
        print(f"📊 Currency values: {currencies}")
        
        # Count shops by currency
        cursor.execute("SELECT currency, COUNT(*) FROM shop GROUP BY currency")
        currency_counts = cursor.fetchall()
        print(f"📈 Shop counts by currency: {currency_counts}")
        
        conn.close()
        
        print("\n🎉 Successfully added currency column!")
        print("✅ All existing shops set to FCFA (default for French-speaking Africa)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error adding currency column: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 Adding Currency Support to Shops")
    print("=" * 40)
    
    success = add_currency_column()
    
    if success:
        print("\n✅ Migration completed successfully!")
        print("🚀 Shops can now set their payment currency.")
        print("💰 FCFA is the default for French-speaking African countries.")
    else:
        print("\n❌ Migration failed!")
        print("🔍 Please check the error messages above.")
