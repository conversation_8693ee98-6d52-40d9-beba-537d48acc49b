#!/usr/bin/env python3
"""
Test script to verify AI rewriting outputs in French even with English input
"""

from services.ai_service import AIService

def test_ai_french_output():
    """Test that AI service outputs in French regardless of input language"""
    
    print("🧪 Testing AI French Output")
    print("=" * 50)
    
    # Test English product title
    english_title = "Women's Balance Hair Growth Supplements Ages 45 and Up 120 Count Free Shipping"
    print(f"\n📝 Original English Title:")
    print(f"   {english_title}")
    
    try:
        french_title = AIService.rewrite_product_title(english_title, "Health & Beauty")
        print(f"\n🇫🇷 AI Rewritten French Title:")
        print(f"   {french_title}")
        
        # Check if output contains French words
        french_indicators = ['de', 'pour', 'et', 'avec', 'des', 'les', 'la', 'le', 'du', 'croissance', 'cheveux', 'femmes', 'suppléments']
        has_french = any(word.lower() in french_title.lower() for word in french_indicators)
        
        if has_french:
            print("✅ Title appears to be in French!")
        else:
            print("⚠️  Title might not be in French")
            
    except Exception as e:
        print(f"❌ Error rewriting title: {e}")
    
    # Test English product description
    english_description = "Product imported from eBay. Original Title: Women's Balance Hair Growth Supplements Ages 45 and Up 120 Count Free Shipping. Shipping: Free shipping. Rating: 4.5 stars"
    print(f"\n📝 Original English Description:")
    print(f"   {english_description[:100]}...")
    
    try:
        french_description = AIService.rewrite_product_description(english_description, english_title, "Health & Beauty")
        print(f"\n🇫🇷 AI Rewritten French Description:")
        print(f"   {french_description}")
        
        # Check if output contains French words
        french_indicators = ['produit', 'importé', 'depuis', 'pour', 'femmes', 'croissance', 'cheveux', 'suppléments', 'livraison', 'gratuite', 'étoiles']
        has_french = any(word.lower() in french_description.lower() for word in french_indicators)
        
        if has_french:
            print("✅ Description appears to be in French!")
        else:
            print("⚠️  Description might not be in French")
            
    except Exception as e:
        print(f"❌ Error rewriting description: {e}")
    
    # Test English product tags
    print(f"\n📝 Generating French Tags for English Product:")
    
    try:
        french_tags = AIService.generate_product_tags(english_title, english_description, "Health & Beauty")
        print(f"\n🏷️  AI Generated French Tags:")
        print(f"   {french_tags}")
        
        # Check if tags contain French words
        french_indicators = ['santé', 'beauté', 'cheveux', 'croissance', 'femmes', 'suppléments', 'vitamines', 'soins']
        has_french = any(word.lower() in french_tags.lower() for word in french_indicators)
        
        if has_french:
            print("✅ Tags appear to be in French!")
        else:
            print("⚠️  Tags might not be in French")
            
    except Exception as e:
        print(f"❌ Error generating tags: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Test Summary:")
    print("   - AI should now output in French regardless of input language")
    print("   - This ensures consistent French content for afroly.org")
    print("   - Imported eBay/AliExpress products will have French descriptions")
    print("✅ French localization test completed!")

if __name__ == "__main__":
    test_ai_french_output()
