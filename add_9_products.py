#!/usr/bin/env python3
"""
Add 9 products to Mode Africaine Élégante shop
"""

from app import create_app
from models import db, Shop, Product, Category, ProductStatus
from datetime import datetime
import random

def add_nine_products():
    app = create_app()
    
    with app.app_context():
        # Get the shop
        shop = Shop.query.filter_by(name='Mode Africaine Élégante').first()
        category = Category.query.first()
        
        if not shop or not category:
            print("Shop or category not found")
            return
        
        # Product data with working image URLs
        products = [
            {
                'name': 'Robe Wax Élégante Femme',
                'description': 'Magnifique robe en tissu wax authentique avec motifs traditionnels africains.',
                'price': 45000,
                'sale_price': 38000,
                'stock': 15,
                'images': [
                    'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500&h=600&fit=crop&auto=format',
                    'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=500&h=600&fit=crop&auto=format'
                ]
            },
            {
                'name': 'Chemise Homme Bazin Brodé',
                'description': 'Chemise homme en bazin riche avec broderies dorées.',
                'price': 35000,
                'stock': 12,
                'images': [
                    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=600&fit=crop&auto=format',
                    'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=500&h=600&fit=crop&auto=format'
                ]
            },
            {
                'name': 'Ensemble Complet Femme Kente',
                'description': 'Ensemble deux pièces en tissu Kente authentique du Ghana.',
                'price': 65000,
                'sale_price': 55000,
                'stock': 8,
                'images': [
                    'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=500&h=600&fit=crop&auto=format',
                    'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=500&h=600&fit=crop&auto=format'
                ]
            },
            {
                'name': 'Pantalon Homme Bogolan',
                'description': 'Pantalon traditionnel en tissu bogolan du Mali.',
                'price': 28000,
                'stock': 20,
                'images': [
                    'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=500&h=600&fit=crop&auto=format'
                ]
            },
            {
                'name': 'Robe Longue Ankara Moderne',
                'description': 'Robe longue en tissu Ankara avec coupe moderne.',
                'price': 42000,
                'sale_price': 36000,
                'stock': 18,
                'images': [
                    'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=500&h=600&fit=crop&auto=format'
                ]
            },
            {
                'name': 'Veste Homme Wax Casual',
                'description': 'Veste décontractée en tissu wax pour homme.',
                'price': 38000,
                'stock': 10,
                'images': [
                    'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=500&h=600&fit=crop&auto=format'
                ]
            },
            {
                'name': 'Jupe Plissée Tissu Traditionnel',
                'description': 'Jupe plissée en tissu traditionnel africain.',
                'price': 25000,
                'sale_price': 22000,
                'stock': 25,
                'images': [
                    'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=500&h=600&fit=crop&auto=format'
                ]
            },
            {
                'name': 'Combinaison Femme Wax Chic',
                'description': 'Combinaison élégante en tissu wax pour femme.',
                'price': 48000,
                'stock': 14,
                'images': [
                    'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500&h=600&fit=crop&auto=format'
                ]
            },
            {
                'name': 'Costume Traditionnel Complet',
                'description': 'Costume traditionnel complet pour homme avec pantalon, chemise et veste.',
                'price': 85000,
                'sale_price': 75000,
                'stock': 6,
                'featured': True,
                'images': [
                    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=600&fit=crop&auto=format'
                ]
            }
        ]
        
        added = 0
        for i, prod in enumerate(products):
            slug = f"product-new-{i+1}-mode-africaine-elegante"
            
            # Check if exists
            if Product.query.filter_by(slug=slug).first():
                continue
            
            product = Product(
                shop_id=shop.id,
                category_id=category.id,
                name=prod['name'],
                slug=slug,
                description=prod['description'],
                short_description=prod['description'][:100] + '...',
                price=prod['price'],
                sale_price=prod.get('sale_price'),
                stock_quantity=prod['stock'],
                images=prod['images'],
                status=ProductStatus.ACTIVE,
                featured=prod.get('featured', False),
                sku=f"MAE-{random.randint(1000, 9999)}",
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            db.session.add(product)
            added += 1
            print(f"Added: {prod['name']}")
        
        db.session.commit()
        print(f"Successfully added {added} products!")
        
        # Show total
        total = Product.query.filter_by(shop_id=shop.id).count()
        print(f"Total products in shop: {total}")

if __name__ == '__main__':
    add_nine_products()
