#!/usr/bin/env python3
"""
Debug AliExpress API response to understand image structure
"""

import requests
import json

# RapidAPI configuration
RAPID_API_KEY = '**************************************************'
RAPID_API_HOST = 'aliexpress-true-api.p.rapidapi.com'

def debug_aliexpress_images():
    """Debug AliExpress API response to see image structure"""
    
    headers = {
        "X-RapidAPI-Key": RAPID_API_KEY,
        "X-RapidAPI-Host": RAPID_API_HOST
    }
    
    # Search for products
    url = f"https://{RAPID_API_HOST}/api/v3/products"
    params = {
        "keywords": "phone case",
        "page_no": "1",
        "page_size": "3"
    }
    
    try:
        print("🔍 Searching for products...")
        response = requests.get(url, headers=headers, params=params, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            products = data.get('products', [])
            
            print(f"✅ Found {len(products)} products")
            
            for i, product in enumerate(products):
                print(f"\n📦 PRODUCT {i+1}:")
                print(f"ID: {product.get('product_id', 'N/A')}")
                print(f"Title: {product.get('product_title', 'N/A')[:50]}...")
                print(f"Price: ${product.get('target_sale_price', 'N/A')}")
                
                # Debug image fields
                print("\n🖼️  IMAGE FIELDS:")
                
                # Main image
                main_image = product.get('product_main_image_url', '')
                print(f"Main image: {main_image}")
                
                # Small images
                small_images = product.get('product_small_image_urls', {})
                print(f"Small images structure: {type(small_images)}")
                print(f"Small images content: {small_images}")
                
                # Video
                video = product.get('product_video_url', '')
                if video:
                    print(f"Video: {video}")
                
                # All image-related fields
                image_fields = [key for key in product.keys() if 'image' in key.lower() or 'photo' in key.lower() or 'pic' in key.lower()]
                print(f"All image fields: {image_fields}")
                
                for field in image_fields:
                    print(f"  {field}: {product.get(field)}")
                
                # Test image URLs
                print("\n🔗 TESTING IMAGE URLS:")
                if main_image:
                    print(f"Testing main image: {main_image}")
                    try:
                        img_response = requests.head(main_image, timeout=10)
                        print(f"  Status: {img_response.status_code}")
                        print(f"  Content-Type: {img_response.headers.get('content-type', 'N/A')}")
                    except Exception as e:
                        print(f"  Error: {str(e)}")
                
                print("-" * 60)
                
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"💥 Exception: {str(e)}")

if __name__ == "__main__":
    print("🚀 Debugging AliExpress Image URLs")
    print("=" * 60)
    debug_aliexpress_images()
