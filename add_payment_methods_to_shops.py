#!/usr/bin/env python3
"""
Script to add payment methods to existing shops for demo purposes.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, Shop

def add_payment_methods():
    """Add payment methods to existing shops."""
    app = create_app()
    
    with app.app_context():
        try:
            # Get all shops
            shops = Shop.query.all()
            
            if not shops:
                print("❌ No shops found in the database.")
                return
            
            print(f"📊 Found {len(shops)} shops. Adding payment methods...")
            
            # Payment methods data for different countries
            payment_methods_by_country = {
                'Sénégal': [
                    {
                        'type': 'orange_money',
                        'details': {
                            'phone_number': '+************',
                            'account_name': 'Boutique Sénégal'
                        }
                    },
                    {
                        'type': 'wave',
                        'details': {
                            'phone_number': '+************',
                            'account_name': 'Boutique Sénégal'
                        }
                    },
                    {
                        'type': 'bank_account',
                        'details': {
                            'bank_name': 'Banque Atlantique Sénégal',
                            'account_number': '***********',
                            'account_name': 'Boutique SARL',
                            'swift_code': 'CBAOSNDA'
                        }
                    }
                ],
                'Cameroun': [
                    {
                        'type': 'mtn_money',
                        'details': {
                            'phone_number': '+************',
                            'account_name': 'Boutique Cameroun'
                        }
                    },
                    {
                        'type': 'orange_money',
                        'details': {
                            'phone_number': '+************',
                            'account_name': 'Boutique Cameroun'
                        }
                    },
                    {
                        'type': 'bank_account',
                        'details': {
                            'bank_name': 'Afriland First Bank',
                            'account_number': '***********',
                            'account_name': 'Boutique Cameroun SARL',
                            'swift_code': 'AFRCCMCX'
                        }
                    }
                ],
                'Ghana': [
                    {
                        'type': 'mtn_momo',
                        'details': {
                            'phone_number': '+************',
                            'account_name': 'Ghana Shop',
                            'momo_name': 'Ghana Shop MoMo'
                        }
                    },
                    {
                        'type': 'bank_account',
                        'details': {
                            'bank_name': 'Ghana Commercial Bank',
                            'account_number': '***********',
                            'account_name': 'Ghana Shop Ltd',
                            'swift_code': 'GCBLGHAC'
                        }
                    }
                ],
                'default': [
                    {
                        'type': 'bank_account',
                        'details': {
                            'bank_name': 'Banque Locale',
                            'account_number': '***********',
                            'account_name': 'Boutique Africaine',
                            'swift_code': 'LOCAAFXX'
                        }
                    },
                    {
                        'type': 'custom',
                        'details': {
                            'method_name': 'Paiement Mobile Local',
                            'account_details': '+XXX XX XXX XXXX',
                            'instructions': 'Contactez-nous pour les détails de paiement mobile local.'
                        }
                    }
                ]
            }
            
            # Update each shop with payment methods
            for shop in shops:
                print(f"\n🏪 Updating {shop.name} ({shop.country})...")
                
                # Get payment methods for this country or use default
                country_methods = payment_methods_by_country.get(shop.country, payment_methods_by_country['default'])
                
                # Customize the payment methods for this specific shop
                shop_methods = []
                for method in country_methods:
                    shop_method = method.copy()
                    # Update account names to include shop name
                    if 'account_name' in shop_method['details']:
                        shop_method['details']['account_name'] = shop.name
                    if 'momo_name' in shop_method['details']:
                        shop_method['details']['momo_name'] = f"{shop.name} MoMo"
                    shop_methods.append(shop_method)
                
                # Initialize payment_info if it doesn't exist
                if not shop.payment_info:
                    shop.payment_info = {}
                
                # Add the payment methods
                shop.payment_info['methods'] = shop_methods
                
                print(f"   ✅ Added {len(shop_methods)} payment methods:")
                for method in shop_methods:
                    method_type = method.get('type', 'Unknown')
                    details = method.get('details', {})
                    if method_type in ['orange_money', 'mtn_money', 'mtn_momo', 'wave']:
                        phone = details.get('phone_number', 'N/A')
                        print(f"      💳 {method_type}: {phone}")
                    elif method_type == 'bank_account':
                        bank = details.get('bank_name', 'N/A')
                        print(f"      🏦 {method_type}: {bank}")
                    else:
                        print(f"      💰 {method_type}")
            
            # Commit all changes
            db.session.commit()
            print(f"\n✅ Successfully updated payment methods for {len(shops)} shops!")
            
            # Display summary
            print("\n📊 Payment Methods Summary:")
            for shop in shops:
                print(f"\n🏪 {shop.name} ({shop.country}):")
                if shop.payment_info and shop.payment_info.get('methods'):
                    for method in shop.payment_info['methods']:
                        method_type = method.get('type', 'Unknown')
                        print(f"   💳 {method_type}")
                else:
                    print("   ❌ No payment methods configured")
            
        except Exception as e:
            print(f"❌ Error adding payment methods: {e}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    add_payment_methods()
