#!/usr/bin/env python3
"""
Script to initialize default email templates
"""

from app import create_app
from models import db, EmailTemplate, EmailType
import os

def init_email_templates():
    """Initialize default email templates"""
    app = create_app()
    
    with app.app_context():
        # Default email templates
        templates = [
            {
                'name': 'Bienvenue Utilisateur',
                'email_type': EmailType.WELCOME,
                'subject': '🎉 Bienvenue sur Afroly.org, {{ user_name }} !',
                'html_content': open('templates/emails/welcome.html', 'r', encoding='utf-8').read(),
                'text_content': '''
Bienvenue {{ user_name }} !

Nous sommes ravis de vous accueillir sur Afroly.org, la marketplace africaine qui connecte entrepreneurs et consommateurs.

Votre compte {{ user_role }} est maintenant actif !
Email: {{ user_email }}

{% if user_role == 'vendor' %}
Prochaines étapes:
1. Créez votre boutique
2. Ajoutez vos produits  
3. Configurez vos paiements
4. Partagez votre boutique

Créer ma boutique: {{ platform_name }}/shop/create
{% else %}
Découvrez nos boutiques africaines authentiques et commencez vos achats !

Commencer mes achats: {{ platform_name }}
{% endif %}

Connexion: {{ login_url }}

L'équipe Afroly.org
                ''',
                'variables': {
                    'user_name': 'Nom complet de l\'utilisateur',
                    'user_email': 'Email de l\'utilisateur', 
                    'user_role': 'Rôle (vendor/shopper)',
                    'platform_name': 'Nom de la plateforme',
                    'login_url': 'URL de connexion'
                }
            },
            {
                'name': 'Boutique Créée',
                'email_type': EmailType.SHOP_CREATED,
                'subject': '🏪 Boutique "{{ shop_name }}" créée avec succès !',
                'html_content': open('templates/emails/shop_created.html', 'r', encoding='utf-8').read(),
                'text_content': '''
Félicitations {{ shop_owner }} !

Votre boutique "{{ shop_name }}" a été créée avec succès sur Afroly.org !

Votre boutique est en cours de validation par notre équipe (24-48h).

Détails:
- Nom: {{ shop_name }}
- URL: {{ shop_url }}
- Statut: En attente d'approbation

Pendant l'attente:
1. Préparez vos produits
2. Configurez vos paiements
3. Rédigez vos politiques
4. Planifiez votre marketing

Tableau de bord: {{ platform_name }}/shop/dashboard

L'équipe Afroly.org
                ''',
                'variables': {
                    'shop_name': 'Nom de la boutique',
                    'shop_owner': 'Nom du propriétaire',
                    'shop_url': 'URL de la boutique',
                    'platform_name': 'Nom de la plateforme'
                }
            },
            {
                'name': 'Boutique Approuvée',
                'email_type': EmailType.SHOP_APPROVED,
                'subject': '✅ Boutique "{{ shop_name }}" approuvée et active !',
                'html_content': open('templates/emails/shop_approved.html', 'r', encoding='utf-8').read(),
                'text_content': '''
Excellente nouvelle {{ shop_owner }} !

Votre boutique "{{ shop_name }}" a été APPROUVÉE et est maintenant ACTIVE sur Afroly.org !

Votre boutique: {{ shop_url }}
Tableau de bord: {{ dashboard_url }}

Prochaines étapes:
1. Ajoutez vos premiers produits (jusqu'à 10)
2. Configurez vos méthodes de paiement
3. Partagez votre boutique

Avantages de démarrage:
✅ 100 crédits publicitaires gratuits
✅ 0% de commission
✅ Support prioritaire 30 jours
✅ Lien court personnalisé

Commencer maintenant: {{ dashboard_url }}

Bonne vente !
L'équipe Afroly.org
                ''',
                'variables': {
                    'shop_name': 'Nom de la boutique',
                    'shop_owner': 'Nom du propriétaire',
                    'shop_url': 'URL de la boutique',
                    'dashboard_url': 'URL du tableau de bord',
                    'platform_name': 'Nom de la plateforme'
                }
            },
            {
                'name': 'Boutique Rejetée',
                'email_type': EmailType.SHOP_REJECTED,
                'subject': '❌ Boutique "{{ shop_name }}" - Révision nécessaire',
                'html_content': '''
{% extends "emails/base.html" %}
{% block content %}
<h2>Révision nécessaire pour votre boutique</h2>

<p>Bonjour {{ shop_owner }},</p>

<p>Nous avons examiné votre boutique <strong>"{{ shop_name }}"</strong> et malheureusement, elle ne peut pas être approuvée dans son état actuel.</p>

<div class="highlight">
    <h3>Raisons du rejet :</h3>
    <p>{{ rejection_reason|default("Veuillez consulter nos critères d'approbation.") }}</p>
</div>

<h3>Pour corriger et soumettre à nouveau :</h3>
<ol>
    <li>Connectez-vous à votre tableau de bord</li>
    <li>Modifiez les éléments mentionnés</li>
    <li>Soumettez à nouveau pour révision</li>
</ol>

<div style="text-align: center; margin: 25px 0;">
    <a href="{{ dashboard_url }}" class="btn">Modifier Ma Boutique</a>
</div>

<p>Notre équipe reste disponible pour vous aider !</p>
{% endblock %}
                ''',
                'text_content': '''
Bonjour {{ shop_owner }},

Votre boutique "{{ shop_name }}" nécessite des modifications avant approbation.

Raison: {{ rejection_reason|default("Consultez nos critères d'approbation") }}

Pour corriger:
1. Connectez-vous à votre tableau de bord
2. Modifiez les éléments mentionnés  
3. Soumettez à nouveau

Tableau de bord: {{ dashboard_url }}

L'équipe Afroly.org
                ''',
                'variables': {
                    'shop_name': 'Nom de la boutique',
                    'shop_owner': 'Nom du propriétaire',
                    'rejection_reason': 'Raison du rejet',
                    'dashboard_url': 'URL du tableau de bord'
                }
            },
            {
                'name': 'Commande Confirmée',
                'email_type': EmailType.ORDER_CONFIRMATION,
                'subject': '✅ Commande {{ order_number }} confirmée - Afroly.org',
                'html_content': '''
{% extends "emails/base.html" %}
{% block content %}
<h2>Commande confirmée !</h2>

<p>Bonjour {{ customer_name }},</p>

<p>Merci pour votre commande ! Votre commande <strong>{{ order_number }}</strong> a été confirmée et est en cours de traitement.</p>

<div class="order-details">
    <h3>Détails de la commande :</h3>
    <p><strong>Numéro :</strong> {{ order_number }}</p>
    <p><strong>Boutique :</strong> {{ shop_name }}</p>
    <p><strong>Total :</strong> {{ total_amount }}</p>
    <p><strong>Date :</strong> {{ moment().format('DD/MM/YYYY') }}</p>
</div>

<h3>Articles commandés :</h3>
{% for item in order_items %}
<div class="order-item">
    <strong>{{ item.product.name }}</strong><br>
    Quantité: {{ item.quantity }} × {{ item.price }} FCFA = {{ item.total }} FCFA
</div>
{% endfor %}

<div style="text-align: center; margin: 25px 0;">
    <a href="{{ order_url }}" class="btn">Suivre Ma Commande</a>
</div>

<p>Le vendeur vous contactera bientôt pour la livraison.</p>
{% endblock %}
                ''',
                'text_content': '''
Commande confirmée !

Bonjour {{ customer_name }},

Votre commande {{ order_number }} a été confirmée.

Détails:
- Boutique: {{ shop_name }}
- Total: {{ total_amount }}
- Suivi: {{ order_url }}

Le vendeur vous contactera pour la livraison.

Merci !
L'équipe Afroly.org
                ''',
                'variables': {
                    'customer_name': 'Nom du client',
                    'order_number': 'Numéro de commande',
                    'shop_name': 'Nom de la boutique',
                    'total_amount': 'Montant total',
                    'order_url': 'URL de suivi',
                    'order_items': 'Articles commandés'
                }
            }
        ]
        
        # Create templates
        created_count = 0
        for template_data in templates:
            existing = EmailTemplate.query.filter_by(
                email_type=template_data['email_type']
            ).first()
            
            if not existing:
                template = EmailTemplate(**template_data)
                db.session.add(template)
                created_count += 1
                print(f"✅ Template créé: {template_data['name']}")
            else:
                print(f"⚠️  Template existe déjà: {template_data['name']}")
        
        db.session.commit()
        print(f"\n🎉 {created_count} templates email créés avec succès !")

if __name__ == '__main__':
    init_email_templates()
