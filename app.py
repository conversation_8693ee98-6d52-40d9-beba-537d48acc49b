from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_login import LoginManager, login_required, current_user
from flask_migrate import Migrate
import os
from config import Config
from models import db, User, UserRole, UserTier

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    # Initialize extensions
    db.init_app(app)
    migrate = Migrate(app, db)

    # Initialize Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Please log in to access this page.'
    login_manager.login_message_category = 'info'

    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))

    # Create upload directories
    upload_dirs = [
        'static/uploads/products',
        'static/uploads/shops',
        'static/uploads/users',
        'static/images'
    ]
    for directory in upload_dirs:
        os.makedirs(directory, exist_ok=True)

    # Register blueprints
    from routes.main import main_bp
    from routes.auth import auth_bp
    from routes.shop import shop_bp
    from routes.product import product_bp
    from routes.cart import cart_bp
    from routes.admin import admin_bp
    from routes.blog import blog_bp
    from routes.dropshipping import dropshipping_bp

    app.register_blueprint(main_bp)
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(shop_bp, url_prefix='/shop')
    app.register_blueprint(product_bp, url_prefix='/product')
    app.register_blueprint(cart_bp, url_prefix='/cart')
    app.register_blueprint(admin_bp, url_prefix='/admin')
    app.register_blueprint(blog_bp, url_prefix='/blog')
    app.register_blueprint(dropshipping_bp, url_prefix='/dropshipping')

    # Global template variables
    @app.context_processor
    def inject_global_vars():
        from models import Cart, SiteSetting
        from config import Config

        # Get total cart items count for authenticated users
        cart_items_count = 0
        if current_user.is_authenticated:
            carts = Cart.query.filter_by(user_id=current_user.id).all()
            cart_items_count = sum(cart.get_total_items() for cart in carts if cart.items)

        # Helper function to get product limits from admin settings
        def get_product_limit(tier):
            setting_key = f'{tier}_product_limit'
            default_limits = {'free': 10, 'premium': 30, 'gold': 75}
            return SiteSetting.get_value(setting_key, default_limits.get(tier, 10))

        return {
            'UserRole': UserRole,
            'UserTier': UserTier,
            'current_user': current_user,
            'cart_items_count': cart_items_count,
            'config': Config,
            'get_product_limit': get_product_limit
        }

    # Custom template filters
    @app.template_filter('nl2br')
    def nl2br_filter(text):
        """Convert newlines to <br> tags."""
        if not text:
            return text
        return text.replace('\n', '<br>')

    @app.template_filter('truncate_words')
    def truncate_words_filter(text, length=50):
        """Truncate text to specified number of words."""
        if not text:
            return text
        words = text.split()
        if len(words) <= length:
            return text
        return ' '.join(words[:length]) + '...'

    # Error handlers
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500

    @app.errorhandler(403)
    def forbidden_error(error):
        return render_template('errors/403.html'), 403

    # CLI commands for database initialization
    @app.cli.command()
    def init_db():
        """Initialize the database with sample data."""
        db.create_all()

        # Create admin user
        admin = User.query.filter_by(email='<EMAIL>').first()
        if not admin:
            admin = User(
                email='<EMAIL>',
                first_name='Admin',
                last_name='User',
                role=UserRole.ADMIN,
                tier=UserTier.GOLD,
                is_active=True,
                email_verified=True
            )
            admin.set_password('admin123')
            db.session.add(admin)

        # Create sample categories
        from models import Category
        categories = [
            {'name': 'Mode & Vêtements', 'slug': 'mode-vetements', 'icon': 'fas fa-tshirt', 'description': 'Vêtements, chaussures et accessoires de mode'},
            {'name': 'Électronique', 'slug': 'electronique', 'icon': 'fas fa-laptop', 'description': 'Ordinateurs, téléphones et gadgets électroniques'},
            {'name': 'Maison & Jardin', 'slug': 'maison-jardin', 'icon': 'fas fa-home', 'description': 'Décoration, meubles et articles de jardinage'},
            {'name': 'Beauté & Santé', 'slug': 'beaute-sante', 'icon': 'fas fa-heart', 'description': 'Cosmétiques, soins et produits de santé'},
            {'name': 'Sport & Fitness', 'slug': 'sport-fitness', 'icon': 'fas fa-dumbbell', 'description': 'Équipements sportifs et articles de fitness'},
            {'name': 'Livres & Éducation', 'slug': 'livres-education', 'icon': 'fas fa-book', 'description': 'Livres, matériel éducatif et fournitures scolaires'},
            {'name': 'Alimentation & Boissons', 'slug': 'alimentation-boissons', 'icon': 'fas fa-utensils', 'description': 'Produits alimentaires et boissons'},
            {'name': 'Arts & Artisanat', 'slug': 'arts-artisanat', 'icon': 'fas fa-palette', 'description': 'Œuvres d\'art et matériel d\'artisanat'},
        ]

        for cat_data in categories:
            category = Category.query.filter_by(slug=cat_data['slug']).first()
            if not category:
                category = Category(**cat_data)
                db.session.add(category)

        db.session.commit()
        print("Database initialized successfully!")

    # Image proxy route for AliExpress images (handle CORS issues)
    @app.route('/proxy/image')
    def proxy_image():
        """Proxy AliExpress images to avoid CORS issues"""
        import requests

        image_url = request.args.get('url')
        if not image_url:
            return "No URL provided", 400

        # Only allow AliExpress domains for security
        allowed_domains = [
            'ae-pic-a1.aliexpress-media.com',
            'ae01.alicdn.com',
            'ae02.alicdn.com',
            'ae03.alicdn.com',
            'ae04.alicdn.com',
            'aliexpress.com',
            'alicdn.com'
        ]

        # Check if URL is from allowed domain
        from urllib.parse import urlparse
        parsed_url = urlparse(image_url)
        if not any(domain in parsed_url.netloc for domain in allowed_domains):
            return "Domain not allowed", 403

        try:
            # Fetch the image
            response = requests.get(image_url, timeout=10, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })

            if response.status_code == 200:
                # Return the image with proper headers
                return Response(
                    response.content,
                    mimetype=response.headers.get('content-type', 'image/jpeg'),
                    headers={
                        'Cache-Control': 'public, max-age=3600',
                        'Access-Control-Allow-Origin': '*'
                    }
                )
            else:
                return "Image not found", 404

        except Exception as e:
            return f"Error fetching image: {str(e)}", 500

    return app

if __name__ == '__main__':
    app = create_app()
    with app.app_context():
        db.create_all()
    port = int(os.environ.get('PORT', 5002))
    app.run(debug=True, host='0.0.0.0', port=port)
