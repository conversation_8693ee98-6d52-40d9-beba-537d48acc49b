#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to activate WhatsApp by default for all shops.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def activate_whatsapp_default():
    """Activate WhatsApp by default for all shops."""
    try:
        from app import create_app
        from models import db, Shop
        
        app = create_app()
        
        with app.app_context():
            # Get all shops
            shops = Shop.query.all()
            print(f"Found {len(shops)} shops in the database")
            
            if not shops:
                print("No shops found.")
                return
            
            # Default WhatsApp number (the one from memories)
            default_whatsapp = "+44 7951 658211"
            
            for shop in shops:
                print(f"\nUpdating shop: {shop.name}")
                
                # Initialize social_links if it doesn't exist
                if not shop.social_links:
                    shop.social_links = {}
                
                # Check if WhatsApp is already configured
                current_whatsapp = shop.social_links.get('whatsapp', {})
                
                if not current_whatsapp.get('enabled', False):
                    # Activate WhatsApp with default number
                    shop.social_links['whatsapp'] = {
                        'enabled': True,
                        'number': default_whatsapp
                    }
                    print(f"  ✅ Activated WhatsApp with number: {default_whatsapp}")
                else:
                    print(f"  ℹ️  WhatsApp already enabled with: {current_whatsapp.get('number', 'N/A')}")
                
                # Also ensure other social media platforms are initialized (but not enabled by default)
                if 'telegram' not in shop.social_links:
                    shop.social_links['telegram'] = {
                        'enabled': False,
                        'username': ''
                    }
                
                if 'facebook' not in shop.social_links:
                    shop.social_links['facebook'] = {
                        'enabled': False,
                        'page': ''
                    }
            
            # Commit all changes
            db.session.commit()
            print(f"\n✅ Successfully activated WhatsApp for {len(shops)} shops!")
            
            # Verify the update
            print("\n📊 Verification:")
            for shop in shops:
                social_media = shop.get_social_media()
                whatsapp = social_media.get('whatsapp', {})
                if whatsapp.get('enabled', False):
                    print(f"  ✅ {shop.name}: WhatsApp enabled - {whatsapp.get('number', 'N/A')}")
                    print(f"     Link: {shop.get_whatsapp_link()}")
                else:
                    print(f"  ❌ {shop.name}: WhatsApp not enabled")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    activate_whatsapp_default()
