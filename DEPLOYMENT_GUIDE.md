# 🚀 Afroly.org Deployment Guide

This guide will help you deploy Afroly.org to your production server and fix the database issues.

## 🔧 Quick Fix for Current Error

If you're getting the "no such table: user" error, run this immediately:

```bash
# Navigate to your project directory
cd /path/to/your/afroly/project

# Run the database fix script
python fix_database.py
```

## 📋 Complete Deployment Steps

### 1. Prerequisites

Make sure you have:
- Python 3.8+ installed
- Virtual environment activated
- All project files uploaded to your server

### 2. Install Dependencies

```bash
# Install required packages
pip install -r requirements.txt
```

### 3. Fix Database Issues

```bash
# Debug what's wrong (optional)
python debug_database.py

# Fix the database
python fix_database.py
```

### 4. Complete Deployment Setup

```bash
# Run the full deployment script
python deploy.py
```

### 5. Manual Database Initialization (Alternative)

If the scripts don't work, you can manually initialize:

```bash
# Using Flask CLI
export FLASK_APP=app.py
flask init-db

# Or using Python directly
python init_database.py
```

## 🗄️ Database Configuration

### SQLite Database Location

The app looks for the database in these locations:
1. `instance/afromall.db` (recommended for production)
2. `afromall.db` (fallback)
3. `afroly.db` (alternative)

### Environment Variables

Set these environment variables for production:

```bash
export FLASK_ENV=production
export SECRET_KEY=your-secret-key-here
export DATABASE_URL=sqlite:///instance/afromall.db
```

## 🔐 Default Login Credentials

After successful deployment:

- **Admin**: <EMAIL> / admin123
- **Demo Vendor**: <EMAIL> / vendor123

**⚠️ Important**: Change these passwords immediately after first login!

## 🌐 Web Server Configuration

### For Apache (Shared Hosting)

The deployment script creates `.htaccess` and `wsgi.py` files automatically.

### For Nginx + Gunicorn

```bash
# Install Gunicorn
pip install gunicorn

# Run with Gunicorn
gunicorn --bind 0.0.0.0:8000 wsgi:application
```

### For Development Server

```bash
python app.py
```

## 🔍 Troubleshooting

### Common Issues

1. **"No such table: user"**
   ```bash
   python fix_database.py
   ```

2. **Permission denied errors**
   ```bash
   chmod 755 static/uploads
   chmod 755 instance
   ```

3. **Import errors**
   ```bash
   pip install -r requirements.txt
   ```

4. **Database locked**
   ```bash
   # Stop the application first, then:
   python fix_database.py
   ```

### Debug Commands

```bash
# Check what's wrong
python debug_database.py

# Check if app can start
python -c "from app import create_app; app = create_app(); print('App created successfully')"

# Check database tables
python -c "
from app import create_app
from models import db
app = create_app()
with app.app_context():
    print('Tables:', db.engine.table_names())
"
```

## 📁 File Structure

```
afroly.org/
├── app.py                 # Main Flask application
├── models.py             # Database models
├── config.py             # Configuration
├── requirements.txt      # Dependencies
├── wsgi.py              # WSGI entry point (created by deploy.py)
├── .htaccess            # Apache config (created by deploy.py)
├── fix_database.py      # Quick database fix
├── init_database.py     # Complete database setup
├── debug_database.py    # Database debugging
├── deploy.py            # Full deployment script
├── instance/            # Database and config files
│   └── afromall.db     # SQLite database
├── static/              # Static files
│   └── uploads/        # User uploads
├── templates/           # HTML templates
└── routes/             # Route handlers
```

## 🔄 Database Migration

If you need to update the database schema:

```bash
# Backup current database
cp instance/afromall.db instance/afromall.db.backup

# Run migration
python init_database.py
```

## 📧 Email Configuration

After deployment, configure email settings in the admin panel:

1. Login as admin
2. Go to Admin Dashboard → Gérer les Emails
3. Configure SMTP settings
4. Test email functionality

## 🔒 Security Checklist

- [ ] Change default admin password
- [ ] Set strong SECRET_KEY
- [ ] Configure HTTPS
- [ ] Set proper file permissions
- [ ] Configure firewall
- [ ] Regular database backups

## 📞 Support

If you encounter issues:

1. Check the debug output: `python debug_database.py`
2. Review server error logs
3. Ensure all dependencies are installed
4. Verify file permissions
5. Check database file location

## 🎉 Success Verification

After deployment, verify these work:

- [ ] Homepage loads: `https://yourdomain.com`
- [ ] Admin login: `https://yourdomain.com/auth/login`
- [ ] User registration: `https://yourdomain.com/auth/register`
- [ ] Shop creation works
- [ ] Product upload works
- [ ] Email system works

Your Afroly.org marketplace is now ready! 🚀
