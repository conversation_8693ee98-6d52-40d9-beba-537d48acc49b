#!/usr/bin/env python3
"""
Test script to verify all mobile money services are available and working
"""

from app import create_app
from models import db, Shop

def test_all_mobile_money():
    """Test that all mobile money services are available for Sénégal shops"""
    app = create_app()
    
    with app.app_context():
        try:
            # Get a shop in Sénégal
            shop = Shop.query.filter_by(country='Sénégal').first()
            
            if not shop:
                print("❌ No shop found in Sénégal")
                return
            
            print(f"🏪 Testing shop: {shop.name} ({shop.country})")
            
            # Get available payment methods
            available_methods = shop.get_available_payment_methods()
            
            if 'mobile_money' not in available_methods:
                print("❌ No mobile money methods available")
                return
            
            mobile_money_services = available_methods['mobile_money']
            
            # Expected services
            expected_services = [
                'mtn_money',
                'mtn_momo', 
                'orange_money',
                'airtel_money',
                'moov_money',
                'wave'
            ]
            
            print(f"\n📱 Mobile Money Services Available:")
            print(f"Expected: {len(expected_services)} services")
            print(f"Found: {len(mobile_money_services)} services")
            
            # Check each expected service
            for service in expected_services:
                if service in mobile_money_services:
                    config = mobile_money_services[service]
                    countries_count = len(config['countries']) if isinstance(config['countries'], list) else 'all'
                    print(f"✅ {service}: {config['name']} ({countries_count} countries)")
                    
                    # Check if Sénégal is in the countries list
                    if isinstance(config['countries'], list) and 'Sénégal' in config['countries']:
                        print(f"   ✅ Available in Sénégal")
                    elif config['countries'] == 'all':
                        print(f"   ✅ Available worldwide")
                    else:
                        print(f"   ❌ NOT available in Sénégal")
                else:
                    print(f"❌ {service}: NOT FOUND")
            
            # Check for any extra services
            extra_services = set(mobile_money_services.keys()) - set(expected_services)
            if extra_services:
                print(f"\n🆕 Additional services found:")
                for service in extra_services:
                    config = mobile_money_services[service]
                    print(f"   + {service}: {config['name']}")
            
            # Test shop's ability to use each method
            print(f"\n🔐 Permission Tests:")
            for service in expected_services:
                if shop.can_use_payment_method(service):
                    print(f"✅ {service}: Shop can use this method")
                else:
                    print(f"❌ {service}: Shop CANNOT use this method")
            
            # Summary
            available_count = len([s for s in expected_services if s in mobile_money_services])
            print(f"\n📊 Summary:")
            print(f"✅ Available services: {available_count}/{len(expected_services)}")
            print(f"🏪 Shop country: {shop.country}")
            print(f"👤 Shop owner tier: {shop.owner.tier.value}")
            
            if available_count == len(expected_services):
                print(f"\n🎉 SUCCESS: All mobile money services are available!")
            else:
                print(f"\n⚠️  WARNING: {len(expected_services) - available_count} services missing")
            
        except Exception as e:
            print(f"❌ Error during test: {e}")
            raise

if __name__ == '__main__':
    test_all_mobile_money()
