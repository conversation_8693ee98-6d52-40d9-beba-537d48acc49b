#!/usr/bin/env python3
"""
Test the /api/v3/products endpoint with different parameters
"""

import requests
import json

# RapidAPI configuration
RAPID_API_KEY = '**************************************************'
RAPID_API_HOST = 'aliexpress-true-api.p.rapidapi.com'

def test_products_endpoint():
    """Test the /api/v3/products endpoint with different parameters"""
    url = f"https://{RAPID_API_HOST}/api/v3/products"
    
    headers = {
        "X-RapidAPI-Key": RAPID_API_KEY,
        "X-RapidAPI-Host": RAPID_API_HOST
    }
    
    # Test different parameter combinations
    test_cases = [
        {
            "name": "Basic request (no params)",
            "params": {}
        },
        {
            "name": "With keywords",
            "params": {
                "keywords": "phone case",
                "page_no": "1",
                "page_size": "5"
            }
        },
        {
            "name": "With category",
            "params": {
                "category_id": "509",
                "page_no": "1", 
                "page_size": "5"
            }
        },
        {
            "name": "With currency and language",
            "params": {
                "target_currency": "USD",
                "target_language": "EN",
                "page_no": "1",
                "page_size": "5"
            }
        },
        {
            "name": "Search with all params",
            "params": {
                "keywords": "phone",
                "target_currency": "USD",
                "target_language": "EN",
                "page_no": "1",
                "page_size": "3"
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🔍 Testing: {test_case['name']}")
        print(f"📋 Params: {test_case['params']}")
        
        try:
            response = requests.get(url, headers=headers, params=test_case['params'], timeout=30)
            print(f"📊 Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ SUCCESS!")
                print(f"📋 Response keys: {list(data.keys())}")
                
                if 'products' in data:
                    products = data['products']
                    print(f"🛒 Found {len(products)} products")
                    
                    if products:
                        first_product = products[0]
                        print(f"📦 Sample product keys: {list(first_product.keys())}")
                        print(f"🏷️  Title: {first_product.get('product_title', 'No title')[:50]}...")
                        print(f"💰 Price: ${first_product.get('target_sale_price', '0')}")
                else:
                    print("❌ No 'products' key in response")
                    print(f"📋 Full response: {json.dumps(data, indent=2)[:500]}...")
            else:
                print(f"❌ Error: {response.text[:200]}")
                
        except Exception as e:
            print(f"💥 Exception: {str(e)}")

if __name__ == "__main__":
    print("🚀 Testing /api/v3/products endpoint")
    print("=" * 50)
    test_products_endpoint()
    print("\n" + "=" * 50)
    print("✅ Test complete!")
