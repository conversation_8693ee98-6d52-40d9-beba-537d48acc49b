#!/usr/bin/env python3
"""
Script to adjust product prices to realistic African market values
"""

from app import create_app
from models import db, Product

def adjust_prices_to_african_market():
    """Adjust prices to realistic African market values"""
    
    app = create_app()
    
    with app.app_context():
        print("💰 Adjusting prices to African market values...")
        
        # Define realistic price ranges for African market
        price_adjustments = {
            # Fashion items (more affordable)
            'Boubou Grand Boubou Brodé': {'price': 25000, 'sale_price': 20000},
            'Dashiki Moderne Unisexe': {'price': 8500, 'sale_price': None},
            'Ensemble Wax Femme Élégant': {'price': 18000, 'sale_price': 15000},
            
            # Tech items (competitive pricing)
            'Smartphone Samsung Galaxy A54': {'price': 180000, 'sale_price': 165000},
            'Ordinateur Portable HP Pavilion': {'price': 320000, 'sale_price': None},
            'Écouteurs Bluetooth JBL': {'price': 15000, 'sale_price': 12500},
            
            # Artisan crafts (affordable local pricing)
            'Masque Africain Traditionnel': {'price': 12000, 'sale_price': None},
            'Collier Perles Africaines': {'price': 6500, 'sale_price': None},
            'Sac Cuir Maroquinerie Locale': {'price': 18000, 'sale_price': 15000}
        }
        
        # Update product prices
        for product in Product.query.all():
            if product.name in price_adjustments:
                adjustment = price_adjustments[product.name]
                old_price = product.price
                old_sale = product.sale_price
                
                product.price = adjustment['price']
                product.sale_price = adjustment['sale_price']
                
                print(f"📦 {product.name}:")
                print(f"   Price: {old_price:,.0f} → {product.price:,.0f} FCFA")
                if adjustment['sale_price']:
                    print(f"   Sale: {old_sale:,.0f} → {product.sale_price:,.0f} FCFA")
                elif old_sale:
                    print(f"   Sale: {old_sale:,.0f} → Removed")
                print()
        
        # Commit changes
        db.session.commit()
        print("✅ Prices adjusted to African market values!")
        
        # Show final pricing by category
        print(f"\n📋 FINAL AFRICAN MARKET PRICING:")
        print("=" * 60)
        
        categories = {}
        for product in Product.query.all():
            cat_name = product.category.name if product.category else "No Category"
            if cat_name not in categories:
                categories[cat_name] = []
            categories[cat_name].append(product)
        
        for cat_name, products in categories.items():
            print(f"\n🏷️  {cat_name.upper()}:")
            print("-" * 40)
            for product in products:
                if product.sale_price:
                    discount = round((1 - product.sale_price / product.price) * 100)
                    print(f"📦 {product.name}")
                    print(f"   💰 {product.price:,} FCFA")
                    print(f"   🏷️  {product.sale_price:,} FCFA (-{discount}%)")
                else:
                    print(f"📦 {product.name}")
                    print(f"   💰 {product.price:,} FCFA")

if __name__ == "__main__":
    adjust_prices_to_african_market()
