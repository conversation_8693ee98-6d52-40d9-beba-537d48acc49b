#!/usr/bin/env python3
"""
Script to update existing products with proper categories
"""

from app import create_app
from models import db, Product, Category

def update_product_categories():
    """Update existing products with appropriate categories"""
    
    app = create_app()
    
    with app.app_context():
        print("🏷️  Updating product categories...")
        
        # Get categories
        fashion_cat = Category.query.filter_by(slug='mode-vêtements').first()
        tech_cat = Category.query.filter_by(slug='électronique').first()
        arts_cat = Category.query.filter_by(slug='arts-artisanat').first()
        
        if not fashion_cat or not tech_cat or not arts_cat:
            print("❌ Categories not found! Please run create_categories.py first")
            return
        
        # Update products
        products = Product.query.all()
        updated_count = 0
        
        for product in products:
            old_category = product.category.name if product.category else "None"
            
            if any(word in product.name for word in ['Boubou', 'Dashiki', 'Wax', 'Mode']):
                product.category_id = fashion_cat.id
                new_category = fashion_cat.name
            elif any(word in product.name for word in ['Smartphone', 'Ordinateur', 'Écouteurs', 'Samsung', 'HP', 'JBL']):
                product.category_id = tech_cat.id
                new_category = tech_cat.name
            elif any(word in product.name for word in ['Masque', 'Collier', 'Sac', 'Artisanat', 'Perles']):
                product.category_id = arts_cat.id
                new_category = arts_cat.name
            else:
                continue
            
            print(f"✅ {product.name}")
            print(f"   {old_category} → {new_category}")
            updated_count += 1
        
        # Commit changes
        db.session.commit()
        print(f"\n🎉 Updated {updated_count} products with categories!")
        
        # Show final status
        print(f"\n📋 PRODUCT CATEGORIES:")
        print("=" * 50)
        for product in Product.query.all():
            category_name = product.category.name if product.category else "❌ No Category"
            print(f"📦 {product.name} → {category_name}")

if __name__ == "__main__":
    update_product_categories()
