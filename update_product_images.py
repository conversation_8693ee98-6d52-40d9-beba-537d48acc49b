#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create realistic product images for demo products
"""

import os
import json
from PIL import Image, ImageDraw, ImageFont
from app import create_app
from models import db, Product, Shop

def create_product_image(product_name, product_type, filename, image_index=0):
    """Create a product image based on product type"""
    
    # Define product themes
    themes = {
        'fashion': {
            'bg_colors': ['#FFE5E5', '#E5F3FF', '#E5FFE5', '#FFF5E5'],
            'accent_colors': ['#FF6B6B', '#4ECDC4', '#95E1D3', '#F38BA8'],
            'icons': ['👗', '👔', '👚', '🥿']
        },
        'tech': {
            'bg_colors': ['#E8F4FD', '#F0F8FF', '#E6F3FF', '#F5F5F5'],
            'accent_colors': ['#3498DB', '#2C3E50', '#34495E', '#5DADE2'],
            'icons': ['📱', '💻', '⌚', '🎧']
        },
        'crafts': {
            'bg_colors': ['#FFF8DC', '#F5E6D3', '#E6D3A3', '#DEB887'],
            'accent_colors': ['#D2691E', '#8B4513', '#CD853F', '#A0522D'],
            'icons': ['🎨', '🏺', '👜', '💍']
        }
    }
    
    # Create image
    width, height = 400, 400
    theme = themes.get(product_type, themes['fashion'])
    
    bg_color = theme['bg_colors'][image_index % len(theme['bg_colors'])]
    accent_color = theme['accent_colors'][image_index % len(theme['accent_colors'])]
    icon = theme['icons'][image_index % len(theme['icons'])]
    
    img = Image.new('RGB', (width, height), bg_color)
    draw = ImageDraw.Draw(img)
    
    # Draw decorative elements
    # Main circle
    circle_size = 200
    circle_x = (width - circle_size) // 2
    circle_y = (height - circle_size) // 2
    draw.ellipse([circle_x, circle_y, circle_x + circle_size, circle_y + circle_size], 
                fill=accent_color, outline='white', width=5)
    
    # Try to load fonts
    try:
        icon_font = ImageFont.truetype("/System/Library/Fonts/Apple Color Emoji.ttc", 80)
        text_font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 16)
    except:
        try:
            icon_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 60)
            text_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 14)
        except:
            icon_font = ImageFont.load_default()
            text_font = ImageFont.load_default()
    
    # Add icon in center
    bbox = draw.textbbox((0, 0), icon, font=icon_font)
    icon_width = bbox[2] - bbox[0]
    icon_height = bbox[3] - bbox[1]
    icon_x = (width - icon_width) // 2
    icon_y = (height - icon_height) // 2 - 20
    
    draw.text((icon_x, icon_y), icon, font=icon_font)
    
    # Add product name at bottom
    words = product_name.split()[:3]  # First 3 words
    short_name = ' '.join(words)
    if len(short_name) > 25:
        short_name = short_name[:22] + "..."
    
    bbox = draw.textbbox((0, 0), short_name, font=text_font)
    text_width = bbox[2] - bbox[0]
    text_x = (width - text_width) // 2
    text_y = height - 40
    
    # Text background
    draw.rectangle([text_x - 10, text_y - 5, text_x + text_width + 10, text_y + 25], 
                  fill='white', outline=accent_color, width=2)
    draw.text((text_x, text_y), short_name, fill=accent_color, font=text_font)
    
    # Add corner decoration
    corner_size = 30
    draw.ellipse([10, 10, corner_size, corner_size], fill=accent_color)
    draw.ellipse([width-corner_size, 10, width-10, corner_size], fill=accent_color)
    
    # Save image
    img.save(filename)
    print(f"Created product image: {filename}")

def update_product_images():
    """Update all demo products with appropriate images"""
    
    app = create_app()
    
    with app.app_context():
        products = Product.query.all()
        
        if not products:
            print("No products found to update")
            return
        
        # Ensure upload directory exists
        upload_dir = 'static/uploads/products'
        os.makedirs(upload_dir, exist_ok=True)
        
        # Product type mapping based on shop
        shop_types = {}
        shops = Shop.query.all()
        for shop in shops:
            if 'Mode' in shop.name or 'Africaine' in shop.name:
                shop_types[shop.id] = 'fashion'
            elif 'Tech' in shop.name or 'Solutions' in shop.name:
                shop_types[shop.id] = 'tech'
            elif 'Artisanat' in shop.name or 'Traditionnel' in shop.name:
                shop_types[shop.id] = 'crafts'
            else:
                shop_types[shop.id] = 'fashion'
        
        updated_count = 0
        
        for product in products:
            print(f"\nUpdating images for: {product.name}")
            
            shop_type = shop_types.get(product.shop_id, 'fashion')
            
            # Create 2-3 images per product
            new_images = []
            num_images = 2 if product.featured else 1
            
            for i in range(num_images):
                image_filename = f"product_{product.id}_{i+1}.png"
                image_path = os.path.join(upload_dir, image_filename)
                create_product_image(product.name, shop_type, image_path, i)
                new_images.append(f"/static/uploads/products/{image_filename}")
            
            # Update product images
            product.images = new_images
            updated_count += 1
            
            print(f"✅ Updated {product.name} with {len(new_images)} images")
        
        # Commit changes
        db.session.commit()
        print(f"\n🎉 Updated {updated_count} products with new images!")
        
        # Print summary by shop
        print("\n📋 UPDATED PRODUCTS BY SHOP:")
        print("=" * 60)
        for shop in shops:
            shop_products = Product.query.filter_by(shop_id=shop.id).all()
            print(f"\n🏪 {shop.name}")
            for product in shop_products:
                print(f"   📦 {product.name} - {len(product.images)} images")

if __name__ == "__main__":
    print("🖼️  Starting product image update...")
    update_product_images()
