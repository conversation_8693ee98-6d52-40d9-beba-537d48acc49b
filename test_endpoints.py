#!/usr/bin/env python3
"""
Test script to find correct AliExpress API endpoints
"""

import requests
import json

# RapidAPI configuration
RAPID_API_KEY = '**************************************************'
RAPID_API_HOST = 'aliexpress-true-api.p.rapidapi.com'

def test_endpoint(endpoint, params=None):
    """Test a specific endpoint"""
    url = f"https://{RAPID_API_HOST}{endpoint}"
    
    headers = {
        "X-RapidAPI-Key": RAPID_API_KEY,
        "X-RapidAPI-Host": RAPID_API_HOST
    }
    
    try:
        print(f"\n🔍 Testing: {endpoint}")
        response = requests.get(url, headers=headers, params=params, timeout=30)
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ SUCCESS!")
            data = response.json()
            print(f"📋 Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            return True
        else:
            print(f"❌ Error: {response.text[:200]}")
            return False
    except Exception as e:
        print(f"💥 Exception: {str(e)}")
        return False

def main():
    print("🚀 AliExpress API Endpoint Discovery")
    print("=" * 50)
    
    # Test various endpoint patterns
    endpoints_to_test = [
        # Search endpoints
        "/api/v1/search",
        "/api/v2/search", 
        "/api/v3/search",
        "/search",
        "/api/search",
        
        # Product endpoints
        "/api/v1/products",
        "/api/v2/products",
        "/api/v3/products",
        "/products",
        "/api/products",
        
        # Promo product endpoints
        "/api/v1/promo-products",
        "/api/v2/promo-products",
        "/api/v3/promo-products",
        "/api/v1/featured-promo-products",
        "/api/v2/featured-promo-products",
        "/api/v3/featured-promo-products",
        "/api/v3/lists/promo-products",
        "/promo-products",
        
        # Product detail endpoints
        "/api/v1/product",
        "/api/v2/product",
        "/api/v3/product",
        "/product",
        "/api/product",
    ]
    
    # Test search with basic params
    search_params = {
        "keywords": "phone",
        "page_no": "1",
        "page_size": "5"
    }
    
    # Test promo products with basic params
    promo_params = {
        "promo_name": "0225-1231 Idealo Promo",
        "page_no": "1", 
        "page_size": "5"
    }
    
    working_endpoints = []
    
    for endpoint in endpoints_to_test:
        if "search" in endpoint:
            if test_endpoint(endpoint, search_params):
                working_endpoints.append(f"{endpoint} (search)")
        elif "promo" in endpoint:
            if test_endpoint(endpoint, promo_params):
                working_endpoints.append(f"{endpoint} (promo)")
        else:
            if test_endpoint(endpoint):
                working_endpoints.append(f"{endpoint} (basic)")
    
    print("\n" + "=" * 50)
    print("✅ WORKING ENDPOINTS:")
    for endpoint in working_endpoints:
        print(f"  ✓ {endpoint}")
    
    if not working_endpoints:
        print("❌ No working endpoints found!")
        print("💡 The API might have changed or require different parameters")

if __name__ == "__main__":
    main()
