#!/usr/bin/env python3
"""
Test AliExpress product details endpoints
"""

import requests
import json

# RapidAPI configuration
RAPID_API_KEY = '**************************************************'
RAPID_API_HOST = 'aliexpress-true-api.p.rapidapi.com'

def test_product_details_endpoints():
    """Test different product details endpoints"""
    
    headers = {
        "X-RapidAPI-Key": RAPID_API_KEY,
        "X-RapidAPI-Host": RAPID_API_HOST
    }
    
    # First get a product ID from search
    print("🔍 Getting a product ID from search...")
    search_url = f"https://{RAPID_API_HOST}/api/v3/products"
    search_params = {
        "keywords": "phone case",
        "page_no": "1",
        "page_size": "1"
    }
    
    try:
        response = requests.get(search_url, headers=headers, params=search_params, timeout=30)
        if response.status_code == 200:
            data = response.json()
            products = data.get('products', [])
            if products:
                product_id = products[0].get('product_id')
                print(f"✅ Found product ID: {product_id}")
                
                # Test different product details endpoints
                endpoints_to_test = [
                    "/api/v1/product",
                    "/api/v2/product", 
                    "/api/v3/product",
                    "/api/v1/product-details",
                    "/api/v2/product-details",
                    "/api/v3/product-details",
                    "/api/v1/products/details",
                    "/api/v2/products/details",
                    "/api/v3/products/details",
                    "/product",
                    "/product-details",
                    "/products/details"
                ]
                
                for endpoint in endpoints_to_test:
                    print(f"\n🔍 Testing: {endpoint}")
                    url = f"https://{RAPID_API_HOST}{endpoint}"
                    params = {"product_id": product_id}
                    
                    try:
                        response = requests.get(url, headers=headers, params=params, timeout=30)
                        print(f"📊 Status: {response.status_code}")
                        
                        if response.status_code == 200:
                            print("✅ SUCCESS!")
                            data = response.json()
                            print(f"📋 Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                            return endpoint  # Return the working endpoint
                        else:
                            print(f"❌ Error: {response.text[:100]}")
                            
                    except Exception as e:
                        print(f"💥 Exception: {str(e)}")
                        
            else:
                print("❌ No products found in search")
        else:
            print(f"❌ Search failed: {response.status_code}")
            
    except Exception as e:
        print(f"💥 Search exception: {str(e)}")
    
    return None

if __name__ == "__main__":
    print("🚀 Testing AliExpress Product Details Endpoints")
    print("=" * 60)
    working_endpoint = test_product_details_endpoints()
    
    if working_endpoint:
        print(f"\n✅ WORKING ENDPOINT FOUND: {working_endpoint}")
    else:
        print("\n❌ NO WORKING ENDPOINT FOUND")
