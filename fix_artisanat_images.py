#!/usr/bin/env python3
"""
Fix broken images for Artisanat Traditionnel Sénégal products
"""

from app import create_app
from models import db, Shop, Product

def fix_artisanat_images():
    """Fix broken images for artisanat products"""
    app = create_app()
    
    with app.app_context():
        try:
            # Get the shop
            shop = Shop.query.filter_by(slug='artisanat-traditionnel-senegal').first()
            if not shop:
                print("❌ Shop not found")
                return
            
            print(f"🏪 Found shop: {shop.name}")
            
            # Get products
            products = Product.query.filter_by(shop_id=shop.id).all()
            print(f"📦 Found {len(products)} products")
            
            # Working image URLs for African artisanal products
            artisanal_images = [
                'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=500&h=600&fit=crop&auto=format',  # African mask
                'https://images.unsplash.com/photo-1573408301185-9146fe634ad0?w=500&h=600&fit=crop&auto=format',  # African jewelry
                'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=500&h=600&fit=crop&auto=format',  # Traditional crafts
                'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=500&h=600&fit=crop&auto=format',  # Leather goods
                'https://images.unsplash.com/photo-1573408301185-9146fe634ad0?w=500&h=600&fit=crop&auto=format',  # Beaded jewelry
                'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=500&h=600&fit=crop&auto=format',  # Wooden crafts
            ]
            
            # Update each product with working images
            for i, product in enumerate(products):
                print(f"\n📦 Updating: {product.name}")
                print(f"   Current images: {product.images}")
                
                # Assign images cyclically
                new_images = [
                    artisanal_images[i % len(artisanal_images)],
                    artisanal_images[(i + 1) % len(artisanal_images)]
                ]
                
                product.images = new_images
                print(f"   New images: {new_images}")
            
            # Commit changes
            db.session.commit()
            print(f"\n✅ Successfully updated images for {len(products)} products!")
            
            # Verify the changes
            print(f"\n📊 Updated Products:")
            for product in products:
                print(f"   - {product.name}")
                print(f"     Images: {len(product.images)} image(s)")
                for j, img in enumerate(product.images):
                    print(f"       {j+1}. {img}")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            db.session.rollback()

if __name__ == '__main__':
    fix_artisanat_images()
