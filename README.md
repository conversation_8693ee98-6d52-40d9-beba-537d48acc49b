# Afroly.org - Plateforme de Centre Commercial Africain

Afroly.org est une plateforme de commerce électronique multi-vendeurs complète conçue spécifiquement pour les entreprises et clients africains. Elle fournit un marché où les vendeurs peuvent créer des boutiques et vendre des produits tandis que les clients peuvent parcourir et acheter auprès de plusieurs vendeurs.

## 🌟 Key Features

### Multi-User System
- **Shoppers**: Browse products, add to cart, place orders, write reviews
- **Vendors**: Create shops, manage products, process orders, track sales
- **Admins**: Oversee platform, approve shops/products, manage users

### Vendor Tiers
- **Free Tier**: 1 shop, 20 products, 15% commission
- **Premium Tier**: 5 shops, 200 products, 10% commission, PayPal/Stripe integration
- **Gold Tier**: Unlimited shops/products, 5% commission, advanced features

### Shop-Specific Cart System
- **No Global Cart**: Each shop has its own cart
- **Separate Checkout**: Users checkout per shop
- **Individual Orders**: Orders are shop-specific

### African-Focused Design
- Mobile-first responsive design
- African currency support (USD, NGN, KES, GHS, ZAR, EGP)
- Localized phone number validation
- African-themed UI elements

## 🚀 Technology Stack

- **Backend**: Flask (Python)
- **Database**: SQLite (easily upgradeable to PostgreSQL)
- **Frontend**: Bootstrap 5, HTML5, CSS3, JavaScript
- **Authentication**: Flask-Login
- **File Uploads**: Pillow for image processing
- **Payment**: Stripe & PayPal integration ready

## 📁 Project Structure

```
afroly/
├── app.py                 # Main Flask application
├── config.py             # Configuration settings
├── models.py             # Database models
├── requirements.txt      # Python dependencies
├── routes/               # Route blueprints
│   ├── auth.py          # Authentication routes
│   ├── main.py          # Homepage and search
│   ├── shop.py          # Shop management
│   ├── product.py       # Product management
│   ├── cart.py          # Cart and checkout
│   └── admin.py         # Admin panel
├── templates/           # Jinja2 templates
├── static/              # CSS, JS, images
├── utils/               # Utility functions
└── venv/               # Virtual environment
```

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.8+
- pip (Python package manager)

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd afroly
   ```

2. **Create virtual environment**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Initialize database**
   ```bash
   flask --app app init-db
   ```

6. **Run the application**
   ```bash
   python app.py
   ```

7. **Access the application**
   Open your browser and go to `http://127.0.0.1:5000`

## 🔧 Configuration

### Environment Variables (.env)
```
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///afromall.db

# Payment Gateway Settings (Optional)
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key
STRIPE_SECRET_KEY=sk_test_your_stripe_key
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret

# Email Settings (Optional)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
```

## 👥 Default Admin Account

After running `flask --app app init-db`, a default admin account is created:
- **Email**: <EMAIL>
- **Password**: admin123

**⚠️ Important**: Change this password immediately in production!

## 🎯 Core Functionality

### For Shoppers
1. **Browse Products**: Search and filter products across all shops
2. **Shop-Specific Carts**: Add products to individual shop carts
3. **Checkout Process**: Complete orders per shop
4. **Order Tracking**: View order history and status
5. **Reviews**: Rate and review products and shops

### For Vendors
1. **Shop Creation**: Set up shop with branding and policies
2. **Product Management**: Add, edit, and manage product listings
3. **Order Processing**: View and fulfill customer orders
4. **Dashboard**: Track sales, inventory, and performance
5. **Tier Upgrades**: Access more features with higher tiers

### For Admins
1. **User Management**: Manage user accounts and tiers
2. **Shop Approval**: Review and approve new shops
3. **Product Moderation**: Approve product listings
4. **Platform Analytics**: View platform-wide statistics
5. **Content Management**: Manage categories and site content

## 🔒 Security Features

- Password hashing with bcrypt
- CSRF protection with Flask-WTF
- SQL injection prevention with SQLAlchemy ORM
- File upload validation and sanitization
- Role-based access control

## 📱 Mobile Responsiveness

AfroMall is built with a mobile-first approach:
- Responsive Bootstrap 5 grid system
- Touch-friendly interface elements
- Optimized for African mobile usage patterns
- Fast loading on slower connections

## 🌍 African Localization

- **Currency Support**: Multiple African currencies
- **Phone Validation**: African phone number formats
- **Cultural Design**: African-inspired color schemes and imagery
- **Local Payment Methods**: Integration ready for local payment gateways

## 🚀 Deployment

### Production Considerations
1. **Database**: Upgrade to PostgreSQL for production
2. **Web Server**: Use Gunicorn + Nginx
3. **Environment**: Set `FLASK_ENV=production`
4. **Security**: Use strong secret keys and HTTPS
5. **File Storage**: Consider cloud storage for uploads

### Docker Deployment (Optional)
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "app:create_app()"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Email: <EMAIL>
- Documentation: [Wiki](link-to-wiki)

## 🔮 Future Enhancements

- [ ] Mobile apps (iOS/Android)
- [ ] Advanced analytics dashboard
- [ ] Multi-language support
- [ ] Social media integration
- [ ] Advanced payment gateway integrations
- [ ] Inventory management system
- [ ] Affiliate marketing program
- [ ] API for third-party integrations

---

**Built with ❤️ for Africa** 🌍
