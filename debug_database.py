#!/usr/bin/env python3
"""
Debug script to check database connection and table creation
"""

import os
import sys
import sqlite3
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_database_file():
    """Check if database file exists and what tables it contains"""
    print("🔍 Checking database files...")
    
    # Common database file locations
    db_files = [
        'afromall.db',
        'afroly.db',
        'instance/afromall.db',
        'instance/afroly.db'
    ]
    
    for db_file in db_files:
        if Path(db_file).exists():
            print(f"✅ Found database file: {db_file}")
            
            # Check tables in the database
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # Get list of tables
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                
                if tables:
                    print(f"   Tables in {db_file}:")
                    for table in tables:
                        print(f"   - {table[0]}")
                        
                        # Check if it's the user table, show some info
                        if table[0] == 'user':
                            cursor.execute("SELECT COUNT(*) FROM user;")
                            count = cursor.fetchone()[0]
                            print(f"     (contains {count} users)")
                else:
                    print(f"   ⚠️ No tables found in {db_file}")
                
                conn.close()
                
            except Exception as e:
                print(f"   ❌ Error reading {db_file}: {str(e)}")
        else:
            print(f"❌ Database file not found: {db_file}")

def check_flask_config():
    """Check Flask database configuration"""
    print("\n🔍 Checking Flask configuration...")
    
    try:
        from config import Config
        print(f"✅ Config imported successfully")
        
        if hasattr(Config, 'SQLALCHEMY_DATABASE_URI'):
            db_uri = Config.SQLALCHEMY_DATABASE_URI
            print(f"   Database URI: {db_uri}")
            
            # Extract database file path from URI
            if db_uri.startswith('sqlite:///'):
                db_path = db_uri.replace('sqlite:///', '')
                if Path(db_path).exists():
                    print(f"   ✅ Database file exists: {db_path}")
                else:
                    print(f"   ❌ Database file missing: {db_path}")
            else:
                print(f"   ℹ️ Non-SQLite database: {db_uri}")
        else:
            print("   ❌ SQLALCHEMY_DATABASE_URI not found in config")
            
    except ImportError as e:
        print(f"   ❌ Error importing config: {str(e)}")

def test_app_creation():
    """Test if the Flask app can be created"""
    print("\n🔍 Testing Flask app creation...")
    
    try:
        from app import create_app
        print("✅ App module imported successfully")
        
        app = create_app()
        print("✅ Flask app created successfully")
        
        with app.app_context():
            print("✅ App context created successfully")
            
            # Try to import models
            from models import db, User
            print("✅ Models imported successfully")
            
            # Check if tables exist
            try:
                user_count = User.query.count()
                print(f"✅ User table accessible, contains {user_count} users")
            except Exception as e:
                print(f"❌ Error accessing User table: {str(e)}")
                
                # Try to create tables
                print("🔄 Attempting to create tables...")
                try:
                    db.create_all()
                    print("✅ Tables created successfully")
                    
                    # Try again
                    user_count = User.query.count()
                    print(f"✅ User table now accessible, contains {user_count} users")
                    
                except Exception as create_error:
                    print(f"❌ Error creating tables: {str(create_error)}")
                    
    except Exception as e:
        print(f"❌ Error testing app creation: {str(e)}")

def check_permissions():
    """Check file and directory permissions"""
    print("\n🔍 Checking permissions...")
    
    # Check current directory
    current_dir = Path('.')
    if current_dir.is_dir():
        if os.access(current_dir, os.W_OK):
            print("✅ Current directory is writable")
        else:
            print("❌ Current directory is not writable")
    
    # Check instance directory
    instance_dir = Path('instance')
    if instance_dir.exists():
        if os.access(instance_dir, os.W_OK):
            print("✅ Instance directory is writable")
        else:
            print("❌ Instance directory is not writable")
    else:
        print("ℹ️ Instance directory does not exist")
        try:
            instance_dir.mkdir(exist_ok=True)
            print("✅ Created instance directory")
        except Exception as e:
            print(f"❌ Cannot create instance directory: {str(e)}")

def main():
    """Main debug function"""
    print("🔧 Afroly.org Database Debug Script")
    print("=" * 50)
    
    check_database_file()
    check_flask_config()
    check_permissions()
    test_app_creation()
    
    print("\n" + "=" * 50)
    print("🏁 Debug completed!")
    print("\n💡 If you see errors above, try running:")
    print("   python fix_database.py")

if __name__ == '__main__':
    main()
