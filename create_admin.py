#!/usr/bin/env python3
"""
Create admin user for afroly.org
"""

from app import create_app
from models import db, User, UserRole, UserTier

def create_admin():
    app = create_app()
    
    with app.app_context():
        print("🔍 Checking existing admin users...")
        
        # Check existing admin users
        admins = User.query.filter_by(role=UserRole.ADMIN).all()
        print(f"Found {len(admins)} admin users:")
        for admin in admins:
            print(f"  📧 Email: {admin.email}, Active: {admin.is_active}")
        
        # Create <NAME_EMAIL>
        admin_afroly = User.query.filter_by(email='<EMAIL>').first()
        if not admin_afroly:
            print("\n🔧 Creating <EMAIL>...")
            admin_afroly = User(
                email='<EMAIL>',
                first_name='Admin',
                last_name='<PERSON><PERSON>',
                role=UserRole.ADMIN,
                tier=UserTier.GOLD,
                is_active=True,
                email_verified=True
            )
            admin_afroly.set_password('admin123')
            db.session.add(admin_afroly)
            db.session.commit()
            print('✅ Created <EMAIL> with password: admin123')
        else:
            print("\n🔧 Updating <EMAIL>...")
            # Reset password and ensure active
            admin_afroly.set_password('admin123')
            admin_afroly.is_active = True
            admin_afroly.role = UserRole.ADMIN
            db.session.commit()
            print('✅ Updated <EMAIL> with password: admin123')
        
        # <NAME_EMAIL> exists (legacy)
        admin_afromall = User.query.filter_by(email='<EMAIL>').first()
        if not admin_afromall:
            print("\n🔧 Creating <EMAIL>...")
            admin_afromall = User(
                email='<EMAIL>',
                first_name='Admin',
                last_name='AfroMall',
                role=UserRole.ADMIN,
                tier=UserTier.GOLD,
                is_active=True,
                email_verified=True
            )
            admin_afromall.set_password('admin123')
            db.session.add(admin_afromall)
            db.session.commit()
            print('✅ Created <EMAIL> with password: admin123')
        else:
            print("\n🔧 Updating <EMAIL>...")
            admin_afromall.set_password('admin123')
            admin_afromall.is_active = True
            admin_afromall.role = UserRole.ADMIN
            db.session.commit()
            print('✅ Updated <EMAIL> with password: admin123')
        
        print("\n🎉 Admin setup complete!")
        print("\n📋 ADMIN CREDENTIALS:")
        print("=" * 40)
        print("🔐 Primary Admin:")
        print("   Email: <EMAIL>")
        print("   Password: admin123")
        print()
        print("🔐 Legacy Admin:")
        print("   Email: <EMAIL>") 
        print("   Password: admin123")
        print()
        print("🌐 Login URL: http://127.0.0.1:5002/admin/login")

if __name__ == '__main__':
    create_admin()
