#!/usr/bin/env python3
"""
Test script for eBay API integration
"""

import requests
import json

# RapidAPI configuration
RAPID_API_KEY = '**************************************************'
RAPID_API_HOST = 'ebay-search-result.p.rapidapi.com'

def test_ebay_search():
    """Test the eBay search API"""
    print("🚀 Testing eBay search API...")
    
    # Test search query
    search_query = "macbook pro"
    
    url = f"https://ebay-search-result.p.rapidapi.com/search/{search_query}"
    
    headers = {
        "X-RapidAPI-Key": RAPID_API_KEY,
        "X-RapidAPI-Host": RAPID_API_HOST
    }
    
    try:
        print(f"📡 Sending request to search for: {search_query}")
        response = requests.get(url, headers=headers, timeout=30)
        print(f"📊 Response status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            # Print a sample of the response structure
            print("\n📋 Response data structure:")
            print(json.dumps(data, indent=2)[:1000] + "..." if len(json.dumps(data, indent=2)) > 1000 else json.dumps(data, indent=2))
            
            # Check if results are present
            if "results" in data and data["results"]:
                results = data["results"]
                print(f"\n🎯 Found {len(results)} products")
                
                # Display details of the first few products
                for i, product in enumerate(results[:5]):
                    print(f"\n📦 Product {i+1}:")
                    print(f"📝 Title: {product.get('title', 'N/A')}")
                    print(f"💰 Price: {product.get('price', 'N/A')}")
                    print(f"🚚 Shipping: {product.get('shipping', 'N/A')}")
                    print(f"⭐ Rating: {product.get('rating', 'N/A')}")
                    print(f"🖼️  Image URL: {product.get('image', 'N/A')}")
                    
                    url = product.get('url', 'N/A')
                    if url and len(url) > 100:
                        print(f"🔗 Product URL: {url[:100]}...")
                    else:
                        print(f"🔗 Product URL: {url}")
                
                print(f"\n✅ eBay API Test Successful!")
                print(f"🎉 Found {len(results)} products for '{search_query}'")
                print(f"🔗 Ready for eBay dropshipping integration!")
                
                return True
            else:
                print("❌ No results found in the response")
                return False
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"💥 Error: {str(e)}")
        return False

def test_multiple_searches():
    """Test multiple search queries"""
    print("\n" + "="*50)
    print("🔍 Testing Multiple Search Queries")
    print("="*50)
    
    search_queries = [
        "iphone case",
        "wireless earbuds", 
        "laptop stand",
        "phone charger",
        "bluetooth speaker"
    ]
    
    success_count = 0
    
    for query in search_queries:
        print(f"\n🔍 Testing search: '{query}'")
        
        url = f"https://ebay-search-result.p.rapidapi.com/search/{query.replace(' ', '%20')}"
        
        headers = {
            "X-RapidAPI-Key": RAPID_API_KEY,
            "X-RapidAPI-Host": RAPID_API_HOST
        }
        
        try:
            response = requests.get(url, headers=headers, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                results = data.get("results", [])
                
                if results:
                    print(f"✅ Found {len(results)} products for '{query}'")
                    
                    # Show first product details
                    first_product = results[0]
                    title = first_product.get('title', 'N/A')[:50]
                    price = first_product.get('price', 'N/A')
                    print(f"   📦 Sample: {title}... - {price}")
                    
                    success_count += 1
                else:
                    print(f"⚠️  No products found for '{query}'")
            else:
                print(f"❌ Error {response.status_code} for '{query}'")
                
        except Exception as e:
            print(f"💥 Error searching '{query}': {str(e)}")
    
    print(f"\n📊 Summary: {success_count}/{len(search_queries)} searches successful")
    return success_count > 0

if __name__ == "__main__":
    print("🚀 eBay API Test Suite")
    print("=" * 50)
    
    # Test main search
    main_test_success = test_ebay_search()
    
    # Test multiple searches
    multiple_test_success = test_multiple_searches()
    
    print("\n" + "=" * 50)
    if main_test_success and multiple_test_success:
        print("✅ All eBay API Tests Passed!")
        print("🎯 eBay integration is ready for afroly.org dropshipping!")
        print("🔗 Users can now import products from both AliExpress and eBay")
    else:
        print("⚠️  Some tests failed - check API configuration")
    
    print("=" * 50)
