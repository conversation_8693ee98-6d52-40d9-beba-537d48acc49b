#!/usr/bin/env python3
"""
Test script for AliExpress API integration
"""

import requests
import json

# RapidAPI configuration
RAPID_API_KEY = '**************************************************'
RAPID_API_HOST = 'aliexpress-true-api.p.rapidapi.com'

def test_featured_promo_list():
    """Test the featured-promo endpoint to get the list of promotional campaigns"""
    print("🔍 Testing featured-promo endpoint...")
    
    featured_url = "https://aliexpress-true-api.p.rapidapi.com/api/v3/lists/featured-promo"
    
    headers = {
        "X-RapidAPI-Key": RAPID_API_KEY,
        "X-RapidAPI-Host": RAPID_API_HOST
    }
    
    try:
        print("📡 Sending request to featured-promo endpoint...")
        featured_response = requests.get(featured_url, headers=headers, timeout=30)
        print(f"📊 Response status code: {featured_response.status_code}")
        
        if featured_response.status_code == 200:
            featured_data = featured_response.json()
            print("✅ Response received successfully!")
            print("📋 Response data structure:")
            print(json.dumps(featured_data, indent=2)[:1000] + "...")  # Print first 1000 chars
            
            if "promos" in featured_data:
                promos = featured_data.get("promos", [])
                print(f"🎯 Found {len(promos)} promotional campaigns")
                
                if promos:
                    # Select the first promo
                    selected_promo = promos[0]
                    promo_name = selected_promo.get("promo_name", "")
                    product_num = selected_promo.get("product_num", 0)
                    print(f"🏷️  Selected promo: {promo_name} with {product_num} products")
                    
                    # Test the featured-promo-products endpoint
                    test_featured_promo_products(promo_name)
                else:
                    print("⚠️  No promos found in the response")
            else:
                print("❌ No 'promos' field found in the response")
        else:
            print(f"❌ Error: {featured_response.status_code} - {featured_response.text}")
    except Exception as e:
        print(f"💥 Error: {str(e)}")

def test_featured_promo_products(promo_name):
    """Test the featured-promo-products endpoint to get products from a specific campaign"""
    print(f"\n🛍️  Testing featured-promo-products endpoint with promo_name={promo_name}...")
    
    products_url = "https://aliexpress-true-api.p.rapidapi.com/api/v3/lists/featured-promo-products"
    
    headers = {
        "X-RapidAPI-Key": RAPID_API_KEY,
        "X-RapidAPI-Host": RAPID_API_HOST
    }
    
    products_params = {
        "promo_name": promo_name,
        "page_no": "1",
        "page_size": "10",
        "target_currency": "USD",
        "target_language": "EN"
    }
    
    try:
        print("📡 Sending request to featured-promo-products endpoint...")
        products_response = requests.get(products_url, headers=headers, params=products_params, timeout=30)
        print(f"📊 Response status code: {products_response.status_code}")
        
        if products_response.status_code == 200:
            products_data = products_response.json()
            print("✅ Response received successfully!")
            print("📋 Response data structure:")
            print(json.dumps(products_data, indent=2)[:1000] + "...")  # Print first 1000 chars
            
            # Try to extract products from the response
            product_list = []
            if "products" in products_data and "product" in products_data["products"]:
                product_list = products_data["products"]["product"]
                print(f"🎯 Found products in products_data['products']['product']")
            elif "result" in products_data and "products" in products_data["result"]:
                product_list = products_data["result"]["products"]
                print(f"🎯 Found products in products_data['result']['products']")
            elif "products" in products_data:
                product_list = products_data["products"]
                print(f"🎯 Found products in products_data['products']")
            
            print(f"🛒 Found {len(product_list)} products in the selected campaign")
            
            if product_list:
                # Print details of the first product
                first_product = product_list[0]
                product_id = first_product.get("product_id", "")
                product_title = first_product.get("product_title", "")
                price = first_product.get("target_sale_price", "")
                currency = first_product.get("target_sale_price_currency", "USD")
                
                print(f"\n📦 Sample product:")
                print(f"🆔 ID: {product_id}")
                print(f"📝 Title: {product_title}")
                print(f"💰 Price: {price} {currency}")
                
                # Try to get the image URL
                image_url = None
                if 'product_main_image_url' in first_product and first_product['product_main_image_url']:
                    image_url = first_product['product_main_image_url']
                    print(f"🖼️  Image URL: {image_url}")
                elif 'product_small_image_urls' in first_product:
                    small_images = first_product['product_small_image_urls']
                    if 'string' in small_images:
                        if isinstance(small_images['string'], list) and len(small_images['string']) > 0:
                            image_url = small_images['string'][0]
                            print(f"🖼️  Image URL (from small_images list): {image_url}")
                        elif isinstance(small_images['string'], str):
                            image_url = small_images['string']
                            print(f"🖼️  Image URL (from small_images string): {image_url}")
                    elif isinstance(small_images, list) and len(small_images) > 0:
                        image_url = small_images[0]
                        print(f"🖼️  Image URL (from direct small_images list): {image_url}")
                
                if not image_url:
                    print("❌ No image URL found")
                    
                print(f"\n🎉 API Test Successful! Ready for dropshipping integration.")
        else:
            print(f"❌ Error: {products_response.status_code} - {products_response.text}")
    except Exception as e:
        print(f"💥 Error: {str(e)}")

def test_search_products():
    """Test the search endpoint"""
    print(f"\n🔍 Testing product search endpoint...")
    
    search_url = "https://aliexpress-true-api.p.rapidapi.com/api/v3/search"
    
    headers = {
        "X-RapidAPI-Key": RAPID_API_KEY,
        "X-RapidAPI-Host": RAPID_API_HOST
    }
    
    search_params = {
        "keywords": "phone case",
        "page_no": "1",
        "page_size": "5",
        "target_currency": "USD",
        "target_language": "EN"
    }
    
    try:
        print("📡 Searching for 'phone case' products...")
        search_response = requests.get(search_url, headers=headers, params=search_params, timeout=30)
        print(f"📊 Response status code: {search_response.status_code}")
        
        if search_response.status_code == 200:
            search_data = search_response.json()
            print("✅ Search successful!")
            
            # Try to extract products
            product_list = []
            if "products" in search_data and "product" in search_data["products"]:
                product_list = search_data["products"]["product"]
            elif "result" in search_data and "products" in search_data["result"]:
                product_list = search_data["result"]["products"]
            elif "products" in search_data:
                product_list = search_data["products"]
            
            print(f"🛒 Found {len(product_list)} products for 'phone case'")
            
            if product_list:
                for i, product in enumerate(product_list[:3]):  # Show first 3 products
                    title = product.get("product_title", "No title")[:50]
                    price = product.get("target_sale_price", "0")
                    print(f"  {i+1}. {title}... - ${price}")
        else:
            print(f"❌ Search Error: {search_response.status_code} - {search_response.text}")
    except Exception as e:
        print(f"💥 Search Error: {str(e)}")

if __name__ == "__main__":
    print("🚀 AliExpress API Test Suite")
    print("=" * 50)
    
    # Test featured promos
    test_featured_promo_list()
    
    # Test search
    test_search_products()
    
    print("\n" + "=" * 50)
    print("✅ API Test Suite Complete!")
    print("🎯 If you see products above, the integration is working!")
    print("🔗 You can now use the dropshipping feature in afroly.org")
