#!/usr/bin/env python3
"""
Script to add demo data for new mobile money services: Moov Money, Airtel Money, MTN MoMo
"""

from app import create_app
from models import db, Shop

def add_new_mobile_money_demo():
    """Add demo payment methods for new mobile money services"""
    app = create_app()
    
    with app.app_context():
        try:
            shops = Shop.query.all()
            
            if not shops:
                print("No shops found to update")
                return
            
            # Demo payment methods for new mobile money services
            new_payment_methods = [
                # Shop 1 (Sénégal) - Mode Africaine Élégante
                {
                    'shop_index': 0,
                    'new_methods': [
                        {
                            'type': 'moov_money',
                            'details': {
                                'phone_number': '+************',
                                'account_name': 'Mode Africaine Élégante SARL'
                            }
                        }
                    ]
                },
                # Shop 2 (Ghana) - TechAfrika Solutions
                {
                    'shop_index': 1,
                    'new_methods': [
                        {
                            'type': 'airtel_money',
                            'details': {
                                'phone_number': '+************',
                                'account_name': 'TechAfrika Solutions Ltd'
                            }
                        }
                    ]
                },
                # Shop 3 (Sénégal) - Artisanat Traditionnel
                {
                    'shop_index': 2,
                    'new_methods': [
                        {
                            'type': 'moov_money',
                            'details': {
                                'phone_number': '+************',
                                'account_name': 'Fatou Diallo Artisanat'
                            }
                        }
                    ]
                }
            ]
            
            # Update shops with new mobile money payment methods
            for payment_data in new_payment_methods:
                shop_index = payment_data['shop_index']
                new_methods = payment_data['new_methods']
                
                if shop_index < len(shops):
                    shop = shops[shop_index]
                    
                    # Initialize payment_info if it doesn't exist
                    if not shop.payment_info:
                        shop.payment_info = {'methods': []}
                    elif 'methods' not in shop.payment_info:
                        shop.payment_info['methods'] = []
                    
                    # Add new methods to existing ones
                    existing_methods = shop.payment_info['methods']
                    method_types = [m.get('type') for m in existing_methods]
                    
                    for new_method in new_methods:
                        if new_method['type'] not in method_types:
                            existing_methods.append(new_method)
                            print(f"✅ Added {new_method['type']} to {shop.name}")
                        else:
                            print(f"⚠️  {new_method['type']} already exists for {shop.name}")
            
            db.session.commit()
            print(f"\n✅ Successfully updated mobile money payment methods!")
            
            # Display current payment methods for all shops
            print("\n📊 Current Payment Methods by Shop:")
            for shop in shops:
                print(f"\n🏪 {shop.name} ({shop.country}):")
                if shop.payment_info and shop.payment_info.get('methods'):
                    mobile_money_methods = []
                    other_methods = []
                    
                    for method in shop.payment_info['methods']:
                        method_type = method.get('type', 'Unknown')
                        if method_type in ['mtn_money', 'mtn_momo', 'orange_money', 'airtel_money', 'moov_money', 'wave']:
                            mobile_money_methods.append(method)
                        else:
                            other_methods.append(method)
                    
                    if mobile_money_methods:
                        print("   📱 Mobile Money:")
                        for method in mobile_money_methods:
                            method_type = method.get('type', 'Unknown')
                            details = method.get('details', {})
                            phone = details.get('phone_number', 'N/A')
                            name = details.get('account_name', details.get('momo_name', 'N/A'))
                            print(f"      💳 {method_type}: {phone} ({name})")
                    
                    if other_methods:
                        print("   🏦 Autres méthodes:")
                        for method in other_methods:
                            method_type = method.get('type', 'Unknown')
                            details = method.get('details', {})
                            if method_type == 'bank_account':
                                bank = details.get('bank_name', 'N/A')
                                account = details.get('account_number', 'N/A')
                                print(f"      🏛️ {method_type}: {bank} - {account}")
                            elif method_type == 'paypal':
                                email = details.get('paypal_email', 'N/A')
                                print(f"      💰 {method_type}: {email}")
                            else:
                                print(f"      ⚙️ {method_type}")
                else:
                    print("   ❌ No payment methods configured")
            
            # Summary of mobile money coverage
            print("\n📈 Mobile Money Coverage Summary:")
            mobile_money_services = ['mtn_money', 'mtn_momo', 'orange_money', 'airtel_money', 'moov_money', 'wave']
            
            for service in mobile_money_services:
                shops_with_service = []
                for shop in shops:
                    if shop.payment_info and shop.payment_info.get('methods'):
                        for method in shop.payment_info['methods']:
                            if method.get('type') == service:
                                shops_with_service.append(shop.name)
                                break
                
                if shops_with_service:
                    print(f"   📱 {service}: {len(shops_with_service)} boutique(s) - {', '.join(shops_with_service)}")
                else:
                    print(f"   📱 {service}: 0 boutique")
            
        except Exception as e:
            print(f"❌ Error updating mobile money methods: {e}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    add_new_mobile_money_demo()
