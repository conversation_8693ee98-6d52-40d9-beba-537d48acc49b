#!/usr/bin/env python3
"""
<PERSON>ript to download and update shop images for demo shops
"""

import os
import requests
import shutil
from PIL import Image, ImageDraw, ImageFont
from app import create_app
from models import db, Shop

def create_logo_image(shop_name, shop_type, filename):
    """Create a simple logo image for a shop"""
    
    # Define colors based on shop type
    colors = {
        'fashion': ('#FF6B6B', '#4ECDC4'),  # Coral and Teal
        'tech': ('#3498DB', '#2C3E50'),     # Blue and Dark Blue
        'crafts': ('#E67E22', '#8B4513')    # Orange and Brown
    }
    
    # Create image
    width, height = 300, 300
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img)
    
    # Get colors for shop type
    primary_color, secondary_color = colors.get(shop_type, ('#333333', '#666666'))
    
    # Draw background circle
    margin = 30
    draw.ellipse([margin, margin, width-margin, height-margin], fill=primary_color)
    
    # Draw inner circle
    inner_margin = 60
    draw.ellipse([inner_margin, inner_margin, width-inner_margin, height-inner_margin], fill=secondary_color)
    
    # Try to load a font, fallback to default if not available
    try:
        font_size = 24
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
    except:
        try:
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 24)
        except:
            font = ImageFont.load_default()
    
    # Add shop initials or icon
    if shop_type == 'fashion':
        text = '👗'
        font_size = 60
    elif shop_type == 'tech':
        text = '📱'
        font_size = 60
    elif shop_type == 'crafts':
        text = '🎨'
        font_size = 60
    else:
        # Use first letters of shop name
        words = shop_name.split()
        text = ''.join([word[0].upper() for word in words[:2]])
    
    # Get text size and center it
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (width - text_width) // 2
    y = (height - text_height) // 2
    
    draw.text((x, y), text, fill='white', font=font)
    
    # Save image
    img.save(filename)
    print(f"Created logo: {filename}")

def create_banner_image(shop_name, shop_type, filename):
    """Create a banner image for a shop"""
    
    # Define colors and themes based on shop type
    themes = {
        'fashion': {
            'bg_color': '#FF6B6B',
            'text_color': 'white',
            'accent_color': '#4ECDC4',
            'emoji': '✨👗✨'
        },
        'tech': {
            'bg_color': '#3498DB',
            'text_color': 'white', 
            'accent_color': '#2C3E50',
            'emoji': '⚡📱💻'
        },
        'crafts': {
            'bg_color': '#E67E22',
            'text_color': 'white',
            'accent_color': '#8B4513', 
            'emoji': '🎨✋🏿🌍'
        }
    }
    
    # Create banner image
    width, height = 1200, 400
    img = Image.new('RGB', (width, height), themes[shop_type]['bg_color'])
    draw = ImageDraw.Draw(img)
    
    # Draw accent stripe
    stripe_height = 80
    draw.rectangle([0, height-stripe_height, width, height], fill=themes[shop_type]['accent_color'])
    
    # Try to load a font
    try:
        title_font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 48)
        subtitle_font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 24)
    except:
        try:
            title_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 48)
            subtitle_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 24)
        except:
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()
    
    # Add shop name
    bbox = draw.textbbox((0, 0), shop_name, font=title_font)
    text_width = bbox[2] - bbox[0]
    x = (width - text_width) // 2
    y = height // 2 - 60
    
    draw.text((x, y), shop_name, fill=themes[shop_type]['text_color'], font=title_font)
    
    # Add emoji decoration
    emoji_text = themes[shop_type]['emoji']
    bbox = draw.textbbox((0, 0), emoji_text, font=subtitle_font)
    emoji_width = bbox[2] - bbox[0]
    emoji_x = (width - emoji_width) // 2
    emoji_y = y + 70
    
    draw.text((emoji_x, emoji_y), emoji_text, fill=themes[shop_type]['text_color'], font=subtitle_font)
    
    # Save image
    img.save(filename)
    print(f"Created banner: {filename}")

def update_shop_images():
    """Update all demo shops with appropriate images"""
    
    app = create_app()
    
    with app.app_context():
        shops = Shop.query.all()
        
        if not shops:
            print("No shops found to update")
            return
        
        # Ensure upload directory exists
        upload_dir = 'static/uploads/shops'
        os.makedirs(upload_dir, exist_ok=True)
        
        # Shop type mapping
        shop_types = {
            'Mode Africaine Élégante': 'fashion',
            'TechAfrika Solutions': 'tech', 
            'Artisanat Traditionnel Sénégal': 'crafts'
        }
        
        for shop in shops:
            print(f"\nUpdating images for: {shop.name}")
            
            shop_type = shop_types.get(shop.name, 'fashion')
            
            # Create logo
            logo_filename = f"logo_{shop.id}_{shop.slug}.png"
            logo_path = os.path.join(upload_dir, logo_filename)
            create_logo_image(shop.name, shop_type, logo_path)
            
            # Create banner
            banner_filename = f"banner_{shop.id}_{shop.slug}.png"
            banner_path = os.path.join(upload_dir, banner_filename)
            create_banner_image(shop.name, shop_type, banner_path)
            
            # Update shop in database
            shop.logo = logo_filename
            shop.banner = banner_filename
            
            print(f"✅ Updated {shop.name} with logo: {logo_filename}, banner: {banner_filename}")
        
        # Commit changes
        db.session.commit()
        print("\n🎉 All shop images updated successfully!")
        
        # Print summary
        print("\n📋 UPDATED SHOPS:")
        print("=" * 50)
        for shop in shops:
            print(f"🏪 {shop.name}")
            print(f"   Logo: {shop.logo}")
            print(f"   Banner: {shop.banner}")
            print(f"   Country: {shop.country}")
            print()

if __name__ == "__main__":
    print("🖼️  Starting shop image update...")
    update_shop_images()
