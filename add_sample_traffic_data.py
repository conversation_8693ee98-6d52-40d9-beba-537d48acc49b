#!/usr/bin/env python3
"""
<PERSON>ript to add sample traffic data for testing the vendor dashboard traffic statistics.
"""

import sys
import os
from datetime import datetime, date, timedelta
import random

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, Shop, ShopStatistic, SiteStatistic

def add_sample_traffic_data():
    """Add sample traffic data for existing shops."""
    app = create_app()
    
    with app.app_context():
        # Get all shops
        shops = Shop.query.all()
        
        if not shops:
            print("No shops found. Please create some shops first.")
            return
        
        print(f"Found {len(shops)} shops. Adding sample traffic data...")
        
        # Generate data for the last 30 days
        for i in range(30):
            current_date = date.today() - timedelta(days=i)
            
            for shop in shops:
                # Check if data already exists for this date
                existing_stat = ShopStatistic.query.filter_by(
                    shop_id=shop.id, 
                    date=current_date
                ).first()
                
                if not existing_stat:
                    # Generate random but realistic traffic data
                    base_views = random.randint(10, 100)
                    unique_visitors = random.randint(5, base_views)
                    product_views = random.randint(0, base_views * 2)
                    orders_count = random.randint(0, max(1, unique_visitors // 10))
                    revenue = orders_count * random.uniform(10.0, 100.0)
                    
                    # Create shop statistic
                    shop_stat = ShopStatistic(
                        shop_id=shop.id,
                        date=current_date,
                        views=base_views,
                        unique_visitors=unique_visitors,
                        product_views=product_views,
                        orders_count=orders_count,
                        revenue=revenue
                    )
                    
                    db.session.add(shop_stat)
                    print(f"Added traffic data for {shop.name} on {current_date}")
        
        # Also add some site statistics
        for i in range(30):
            current_date = date.today() - timedelta(days=i)
            
            existing_site_stat = SiteStatistic.query.filter_by(date=current_date).first()
            
            if not existing_site_stat:
                total_views = random.randint(100, 1000)
                unique_visitors = random.randint(50, total_views)
                new_users = random.randint(0, unique_visitors // 5)
                new_shops = random.randint(0, 3)
                new_products = random.randint(0, 10)
                orders_count = random.randint(0, unique_visitors // 20)
                revenue = orders_count * random.uniform(15.0, 150.0)
                
                site_stat = SiteStatistic(
                    date=current_date,
                    page_views=total_views,
                    unique_visitors=unique_visitors,
                    new_users=new_users,
                    new_shops=new_shops,
                    new_products=new_products,
                    orders_count=orders_count,
                    revenue=revenue
                )
                
                db.session.add(site_stat)
                print(f"Added site statistics for {current_date}")
        
        # Commit all changes
        try:
            db.session.commit()
            print("✅ Sample traffic data added successfully!")
            print("You can now view the traffic statistics in the vendor dashboard.")
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error adding sample data: {e}")

if __name__ == "__main__":
    add_sample_traffic_data()
