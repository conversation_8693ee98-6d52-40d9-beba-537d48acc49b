#!/usr/bin/env python3
"""
Create a proper placeholder image for products
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_placeholder_image():
    """Create a placeholder image for products"""
    
    # Create a 400x400 image with a light gray background
    width, height = 400, 400
    background_color = (240, 240, 240)  # Light gray
    text_color = (120, 120, 120)  # Dark gray
    border_color = (200, 200, 200)  # Medium gray
    
    # Create image
    image = Image.new('RGB', (width, height), background_color)
    draw = ImageDraw.Draw(image)
    
    # Draw border
    draw.rectangle([0, 0, width-1, height-1], outline=border_color, width=2)
    
    # Try to use a font, fallback to default if not available
    try:
        font_large = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 24)
        font_small = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 16)
    except:
        try:
            font_large = ImageFont.truetype("arial.ttf", 24)
            font_small = ImageFont.truetype("arial.ttf", 16)
        except:
            font_large = ImageFont.load_default()
            font_small = ImageFont.load_default()
    
    # Draw icon (simple camera icon using rectangles and circles)
    icon_size = 80
    icon_x = (width - icon_size) // 2
    icon_y = (height - icon_size) // 2 - 30
    
    # Camera body
    draw.rectangle([icon_x, icon_y + 20, icon_x + icon_size, icon_y + icon_size - 10], 
                  outline=text_color, width=3)
    
    # Camera lens
    lens_radius = 25
    lens_x = icon_x + icon_size // 2
    lens_y = icon_y + 45
    draw.ellipse([lens_x - lens_radius, lens_y - lens_radius, 
                 lens_x + lens_radius, lens_y + lens_radius], 
                outline=text_color, width=3)
    
    # Inner lens circle
    inner_radius = 15
    draw.ellipse([lens_x - inner_radius, lens_y - inner_radius,
                 lens_x + inner_radius, lens_y + inner_radius],
                outline=text_color, width=2)
    
    # Camera flash
    flash_size = 8
    draw.rectangle([icon_x + 15, icon_y + 5, icon_x + 15 + flash_size, icon_y + 5 + flash_size],
                  fill=text_color)
    
    # Add text
    text1 = "Aucune Image"
    text2 = "Disponible"
    
    # Get text dimensions for centering
    bbox1 = draw.textbbox((0, 0), text1, font=font_large)
    bbox2 = draw.textbbox((0, 0), text2, font=font_small)
    
    text1_width = bbox1[2] - bbox1[0]
    text2_width = bbox2[2] - bbox2[0]
    
    # Draw text
    draw.text(((width - text1_width) // 2, icon_y + icon_size + 20), 
             text1, fill=text_color, font=font_large)
    draw.text(((width - text2_width) // 2, icon_y + icon_size + 50), 
             text2, fill=text_color, font=font_small)
    
    # Save the image
    output_path = "static/images/no-image.png"
    image.save(output_path, "PNG")
    print(f"✅ Placeholder image created: {output_path}")
    
    # Also create a smaller version for thumbnails
    thumbnail = image.resize((200, 200), Image.Resampling.LANCZOS)
    thumbnail_path = "static/images/no-image-thumb.png"
    thumbnail.save(thumbnail_path, "PNG")
    print(f"✅ Thumbnail placeholder created: {thumbnail_path}")

if __name__ == "__main__":
    create_placeholder_image()
