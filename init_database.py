#!/usr/bin/env python3
"""
Database initialization script for deployment
This script ensures all tables are created and basic data is populated
"""

import os
import sys
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import (
    db, User, UserRole, UserTier, Shop, ShopStatus, Product, ProductStatus,
    Category, Order, OrderStatus, CreditPackage, EmailTemplate, EmailType,
    NewsletterSubscriber, SiteSetting
)

def init_database():
    """Initialize database with all tables and essential data"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔄 Creating database tables...")
            
            # Drop all tables first (for clean deployment)
            db.drop_all()
            
            # Create all tables
            db.create_all()
            print("✅ Database tables created successfully!")
            
            # Create admin user
            print("🔄 Creating admin user...")
            admin = User.query.filter_by(email='<EMAIL>').first()
            if not admin:
                admin = User(
                    email='<EMAIL>',
                    password_hash=generate_password_hash('admin123'),
                    first_name='Admin',
                    last_name='Afroly',
                    phone='+44 7951 658211',
                    role=UserRole.ADMIN,
                    tier=UserTier.GOLD,
                    is_active=True,
                    email_verified=True,
                    credit_balance=10000,
                    expiration_date=datetime.utcnow() + timedelta(days=365)
                )
                db.session.add(admin)
                print("✅ Admin user created!")
            else:
                print("ℹ️ Admin user already exists")
            
            # Create demo vendor user
            print("🔄 Creating demo vendor user...")
            vendor = User.query.filter_by(email='<EMAIL>').first()
            if not vendor:
                vendor = User(
                    email='<EMAIL>',
                    password_hash=generate_password_hash('vendor123'),
                    first_name='Demo',
                    last_name='Vendor',
                    phone='+237 6 72 02 75 04',
                    role=UserRole.VENDOR,
                    tier=UserTier.PREMIUM,
                    is_active=True,
                    email_verified=True,
                    credit_balance=500,
                    expiration_date=datetime.utcnow() + timedelta(days=30)
                )
                db.session.add(vendor)
                print("✅ Demo vendor user created!")
            else:
                print("ℹ️ Demo vendor user already exists")
            
            # Create categories
            print("🔄 Creating product categories...")
            categories_data = [
                {'name': 'Mode Africaine', 'description': 'Vêtements et accessoires traditionnels africains'},
                {'name': 'Artisanat', 'description': 'Objets d\'art et artisanat africain'},
                {'name': 'Beauté & Cosmétiques', 'description': 'Produits de beauté et cosmétiques naturels'},
                {'name': 'Alimentation', 'description': 'Produits alimentaires et épices africaines'},
                {'name': 'Électronique', 'description': 'Appareils électroniques et accessoires'},
                {'name': 'Maison & Décoration', 'description': 'Articles pour la maison et décoration'},
                {'name': 'Santé & Bien-être', 'description': 'Produits de santé et bien-être naturels'},
                {'name': 'Livres & Éducation', 'description': 'Livres, matériel éducatif et culturel'},
                {'name': 'Sport & Loisirs', 'description': 'Articles de sport et loisirs'},
                {'name': 'Bijoux & Accessoires', 'description': 'Bijoux traditionnels et accessoires'}
            ]
            
            for cat_data in categories_data:
                category = Category.query.filter_by(name=cat_data['name']).first()
                if not category:
                    category = Category(**cat_data)
                    db.session.add(category)
            
            print("✅ Categories created!")
            
            # Create credit packages
            print("🔄 Creating credit packages...")
            packages_data = [
                {
                    'name': 'Pack Starter',
                    'credits': 100,
                    'price': 1000,
                    'description': 'Pack idéal pour commencer'
                },
                {
                    'name': 'Pack Business',
                    'credits': 300,
                    'price': 2000,
                    'description': 'Pack pour développer votre activité'
                },
                {
                    'name': 'Pack Pro',
                    'credits': 500,
                    'price': 3000,
                    'description': 'Pack professionnel pour grandes boutiques'
                }
            ]
            
            for package_data in packages_data:
                package = CreditPackage.query.filter_by(name=package_data['name']).first()
                if not package:
                    package = CreditPackage(**package_data)
                    db.session.add(package)
            
            print("✅ Credit packages created!")
            
            # Create email templates
            print("🔄 Creating email templates...")
            templates_data = [
                {
                    'name': 'Bienvenue',
                    'email_type': EmailType.WELCOME,
                    'subject': 'Bienvenue sur Afroly.org !',
                    'content': '''
                    <h2>Bienvenue sur Afroly.org !</h2>
                    <p>Bonjour {{ user.first_name }},</p>
                    <p>Nous sommes ravis de vous accueillir sur Afroly.org, la plateforme de vente en ligne dédiée à l'Afrique.</p>
                    <p>Vous pouvez maintenant créer votre boutique et commencer à vendre vos produits.</p>
                    <p>Cordialement,<br>L'équipe Afroly.org</p>
                    '''
                },
                {
                    'name': 'Boutique Créée',
                    'email_type': EmailType.SHOP_CREATED,
                    'subject': 'Votre boutique a été créée avec succès',
                    'content': '''
                    <h2>Boutique créée avec succès !</h2>
                    <p>Bonjour {{ user.first_name }},</p>
                    <p>Votre boutique "{{ shop.name }}" a été créée avec succès.</p>
                    <p>Elle est actuellement en cours de révision par notre équipe.</p>
                    <p>Vous recevrez une notification dès qu'elle sera approuvée.</p>
                    <p>Cordialement,<br>L'équipe Afroly.org</p>
                    '''
                },
                {
                    'name': 'Boutique Approuvée',
                    'email_type': EmailType.SHOP_APPROVED,
                    'subject': 'Votre boutique a été approuvée !',
                    'content': '''
                    <h2>Félicitations ! Votre boutique est approuvée !</h2>
                    <p>Bonjour {{ user.first_name }},</p>
                    <p>Excellente nouvelle ! Votre boutique "{{ shop.name }}" a été approuvée.</p>
                    <p>Elle est maintenant visible sur Afroly.org et vous pouvez commencer à recevoir des commandes.</p>
                    <p>Lien de votre boutique: {{ shop_url }}</p>
                    <p>Cordialement,<br>L'équipe Afroly.org</p>
                    '''
                }
            ]
            
            for template_data in templates_data:
                template = EmailTemplate.query.filter_by(name=template_data['name']).first()
                if not template:
                    template = EmailTemplate(**template_data)
                    db.session.add(template)
            
            print("✅ Email templates created!")
            
            # Create site settings
            print("🔄 Creating site settings...")
            settings_data = [
                {'key': 'site_name', 'value': 'Afroly.org'},
                {'key': 'site_description', 'value': 'Marketplace africain pour vendeurs et acheteurs'},
                {'key': 'contact_email', 'value': '<EMAIL>'},
                {'key': 'contact_phone', 'value': '+44 7951 658211'},
                {'key': 'whatsapp_number', 'value': '+44 7951 658211'},
                {'key': 'whatsapp_enabled', 'value': 'true'},
                {'key': 'currency_symbol', 'value': 'FCFA'},
                {'key': 'currency_code', 'value': 'XAF'},
                {'key': 'mail_server', 'value': ''},
                {'key': 'mail_port', 'value': '587'},
                {'key': 'mail_username', 'value': ''},
                {'key': 'mail_password', 'value': ''},
                {'key': 'mail_use_tls', 'value': 'true'},
                {'key': 'mail_use_ssl', 'value': 'false'}
            ]
            
            for setting_data in settings_data:
                setting = SiteSetting.query.filter_by(key=setting_data['key']).first()
                if not setting:
                    setting = SiteSetting(**setting_data)
                    db.session.add(setting)
            
            print("✅ Site settings created!")
            
            # Commit all changes
            db.session.commit()
            print("✅ All data committed to database!")
            
            print("\n🎉 Database initialization completed successfully!")
            print("\n📋 Login credentials:")
            print("   Admin: <EMAIL> / admin123")
            print("   Vendor: <EMAIL> / vendor123")
            print("\n🌐 Your application is ready to use!")
            
        except Exception as e:
            print(f"❌ Error during database initialization: {str(e)}")
            db.session.rollback()
            raise e

if __name__ == '__main__':
    init_database()
