#!/usr/bin/env python3
"""
Fix existing products with old string status values to use new enum values
"""

import sqlite3
import os

def fix_product_status():
    """Fix product status values in the database"""

    # Database path
    db_path = 'instance/afromall.db'

    if not os.path.exists(db_path):
        print("❌ Database file not found!")
        return False

    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check current status values
        cursor.execute("SELECT DISTINCT status FROM product")
        current_statuses = cursor.fetchall()
        print(f"📊 Current status values: {current_statuses}")

        # Update mapping for old string values to new enum values
        status_mapping = {
            'active': 'ACTIVE',
            'pending': 'PENDING',
            'rejected': 'REJECTED',
            'out_of_stock': 'OUT_OF_STOCK',
            'draft': 'PENDING',  # Map draft to pending if it exists
            'published': 'ACTIVE',  # Map published to active if it exists
        }

        updates_made = 0

        for old_status, new_status in status_mapping.items():
            # Update products with old status values
            cursor.execute(
                "UPDATE product SET status = ? WHERE status = ?",
                (new_status, old_status)
            )

            rows_updated = cursor.rowcount
            if rows_updated > 0:
                print(f"✅ Updated {rows_updated} products from '{old_status}' to '{new_status}'")
                updates_made += rows_updated

        # Commit changes
        conn.commit()

        # Verify the fix
        cursor.execute("SELECT DISTINCT status FROM product")
        new_statuses = cursor.fetchall()
        print(f"📊 New status values: {new_statuses}")

        # Count products by status
        cursor.execute("SELECT status, COUNT(*) FROM product GROUP BY status")
        status_counts = cursor.fetchall()
        print(f"📈 Product counts by status: {status_counts}")

        conn.close()

        print(f"\n🎉 Successfully updated {updates_made} products!")
        print("✅ Database migration completed!")

        return True

    except Exception as e:
        print(f"❌ Error fixing product status: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 Fixing Product Status Values")
    print("=" * 40)

    success = fix_product_status()

    if success:
        print("\n✅ Migration completed successfully!")
        print("🚀 You can now access the product pages without errors.")
    else:
        print("\n❌ Migration failed!")
        print("🔍 Please check the error messages above.")
