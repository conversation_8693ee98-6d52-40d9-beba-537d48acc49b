#!/usr/bin/env python3
"""
Script to add blog articles about French African countries
"""

from app import create_app
from models import db, BlogPost, User
from services.ai_service import AIService
from utils.helpers import generate_slug
from datetime import datetime
import time

def add_blog_articles():
    """Add 10 blog articles about French African countries"""
    app = create_app()
    
    with app.app_context():
        # Get admin user as author
        admin_user = User.query.filter_by(email='<EMAIL>').first()
        if not admin_user:
            print("❌ Admin user not found")
            return
        
        # Topics about French African countries
        topics = [
            "Le e-commerce au Sénégal : Opportunités et défis pour les entrepreneurs",
            "Comment développer son business en ligne en Côte d'Ivoire",
            "Les tendances du shopping digital au Cameroun en 2024",
            "Guide complet pour vendre en ligne au Mali : De l'idée au succès",
            "L'essor du commerce électronique au Burkina Faso",
            "Stratégies marketing digital pour les entreprises togolaises",
            "Le marché en ligne au Niger : Potentiel et perspectives d'avenir",
            "Success stories : Entrepreneurs qui réussissent au Bénin",
            "Les méthodes de paiement mobile en Afrique de l'Ouest",
            "Comment créer une boutique en ligne rentable en Guinée"
        ]
        
        categories = [
            "E-commerce",
            "Entrepreneuriat", 
            "Marketing Digital",
            "Business",
            "Fintech",
            "Success Stories",
            "Guides Pratiques",
            "Tendances",
            "Paiements",
            "Création d'entreprise"
        ]
        
        print("🚀 Génération des articles de blog avec l'IA...")
        
        for i, topic in enumerate(topics):
            try:
                print(f"\n📝 Génération de l'article {i+1}/10: {topic[:50]}...")
                
                # Generate article content using AI
                ai_content = AIService.generate_blog_post(
                    topic=topic,
                    category=categories[i],
                    target_audience="entrepreneurs africains francophones"
                )
                
                # Generate slug
                slug = generate_slug(ai_content['title'])
                
                # Check if slug exists and make it unique
                existing_post = BlogPost.query.filter_by(slug=slug).first()
                if existing_post:
                    slug = f"{slug}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
                
                # Create blog post
                post = BlogPost(
                    title=ai_content['title'],
                    slug=slug,
                    content=ai_content['content'],
                    excerpt=ai_content['excerpt'],
                    category=categories[i],
                    tags=ai_content['tags'],
                    author_id=admin_user.id,
                    is_published=True,
                    is_featured=(i < 3),  # Make first 3 articles featured
                    published_at=datetime.utcnow()
                )
                
                db.session.add(post)
                db.session.commit()
                
                print(f"✅ Article créé: {ai_content['title']}")
                
                # Small delay to avoid rate limiting
                time.sleep(2)
                
            except Exception as e:
                print(f"❌ Erreur lors de la création de l'article {i+1}: {e}")
                db.session.rollback()
                continue
        
        print(f"\n🎉 Génération terminée! {len(topics)} articles créés avec succès.")
        
        # Display summary
        total_posts = BlogPost.query.count()
        published_posts = BlogPost.query.filter_by(is_published=True).count()
        featured_posts = BlogPost.query.filter_by(is_featured=True).count()
        
        print(f"\n📊 Résumé du blog:")
        print(f"   • Total articles: {total_posts}")
        print(f"   • Articles publiés: {published_posts}")
        print(f"   • Articles en vedette: {featured_posts}")

if __name__ == "__main__":
    add_blog_articles()
