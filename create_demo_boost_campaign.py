#!/usr/bin/env python3
"""
Create a demo boost campaign to test the boost system
"""

import os
import sys
from datetime import datetime, timedelta
import random

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, User, Shop, Product, ProductBoost, CreditTransaction, CreditTransactionType, UserRole, UserTier

def create_demo_boost_campaign():
    """Create demo users, shops, products and boost campaigns."""
    app = create_app()
    
    with app.app_context():
        print("🚀 Creating Demo Boost Campaign...")
        print("=" * 50)
        
        try:
            # Create demo vendor user with credits
            demo_email = "<EMAIL>"
            existing_user = User.query.filter_by(email=demo_email).first()
            
            if existing_user:
                demo_user = existing_user
                print(f"✓ Using existing demo user: {demo_user.email}")
                # Add more credits for testing
                demo_user.credit_balance += 500
            else:
                demo_user = User(
                    email=demo_email,
                    first_name="Demo",
                    last_name="Vendor",
                    phone="+237612345678",
                    role=UserRole.VENDOR,
                    tier=UserTier.PREMIUM,
                    credit_balance=600,  # Start with 600 credits for testing
                    is_active=True,
                    email_verified=True
                )
                demo_user.set_password("demo123")
                db.session.add(demo_user)
                db.session.flush()
                print(f"✓ Created demo user: {demo_user.email}")
                
                # Create welcome credit transaction
                welcome_transaction = CreditTransaction(
                    user_id=demo_user.id,
                    amount=600,
                    transaction_type=CreditTransactionType.PURCHASE,
                    description="Crédits de démonstration",
                    payment_status="completed"
                )
                db.session.add(welcome_transaction)
            
            # Create demo shop
            demo_shop_slug = "boutique-demo-boost"
            existing_shop = Shop.query.filter_by(slug=demo_shop_slug).first()
            
            if existing_shop:
                demo_shop = existing_shop
                print(f"✓ Using existing demo shop: {demo_shop.name}")
            else:
                demo_shop = Shop(
                    name="Boutique Démo Boost",
                    slug=demo_shop_slug,
                    description="Boutique de démonstration pour tester le système de boost",
                    owner_id=demo_user.id,
                    country="Cameroun",
                    city="Yaoundé",
                    whatsapp_number="+237612345678",
                    is_approved=True,
                    is_active=True
                )
                db.session.add(demo_shop)
                db.session.flush()
                print(f"✓ Created demo shop: {demo_shop.name}")
            
            # Create demo products
            demo_products = [
                {
                    "name": "Smartphone Samsung Galaxy A54",
                    "description": "Smartphone dernière génération avec appareil photo 50MP, écran AMOLED 6.4 pouces, 128GB de stockage. Parfait pour les photos et vidéos de qualité professionnelle.",
                    "price": 285000,
                    "category": "Électronique",
                    "stock_quantity": 15
                },
                {
                    "name": "Robe Africaine Wax Premium",
                    "description": "Magnifique robe en tissu wax authentique, coupe moderne et élégante. Parfaite pour les occasions spéciales et le quotidien. Tailles disponibles: S à XL.",
                    "price": 35000,
                    "category": "Mode",
                    "stock_quantity": 25
                },
                {
                    "name": "Sac à Main Cuir Artisanal",
                    "description": "Sac à main en cuir véritable fait à la main par des artisans locaux. Design unique, spacieux et durable. Idéal pour le travail et les sorties.",
                    "price": 45000,
                    "category": "Accessoires",
                    "stock_quantity": 12
                },
                {
                    "name": "Chaussures Sport Nike Air Max",
                    "description": "Chaussures de sport confortables et stylées, parfaites pour le running et le fitness. Technologie Air Max pour un confort optimal.",
                    "price": 95000,
                    "category": "Chaussures",
                    "stock_quantity": 20
                },
                {
                    "name": "Montre Connectée Apple Watch",
                    "description": "Montre connectée avec suivi de santé, GPS, résistance à l'eau. Compatible iPhone. Parfaite pour le sport et la vie quotidienne.",
                    "price": 195000,
                    "category": "Électronique",
                    "stock_quantity": 8
                }
            ]
            
            created_products = []
            for product_data in demo_products:
                existing_product = Product.query.filter_by(
                    shop_id=demo_shop.id,
                    name=product_data["name"]
                ).first()
                
                if existing_product:
                    product = existing_product
                    print(f"✓ Using existing product: {product.name}")
                else:
                    product = Product(
                        name=product_data["name"],
                        description=product_data["description"],
                        price=product_data["price"],
                        category=product_data["category"],
                        stock_quantity=product_data["stock_quantity"],
                        shop_id=demo_shop.id,
                        is_approved=True,
                        is_active=True,
                        images='["https://via.placeholder.com/400x400/007bff/ffffff?text=' + product_data["name"].replace(" ", "+") + '"]'
                    )
                    db.session.add(product)
                    db.session.flush()
                    print(f"✓ Created product: {product.name}")
                
                created_products.append(product)
            
            # Create boost campaigns for some products
            boost_campaigns = [
                {
                    "product": created_products[0],  # Samsung Galaxy
                    "duration_days": 7,
                    "credits_cost": 50
                },
                {
                    "product": created_products[1],  # Robe Africaine
                    "duration_days": 14,
                    "credits_cost": 90
                },
                {
                    "product": created_products[4],  # Apple Watch
                    "duration_days": 30,
                    "credits_cost": 150
                }
            ]
            
            print(f"\n🚀 Creating Boost Campaigns...")
            for i, campaign in enumerate(boost_campaigns):
                product = campaign["product"]
                duration_days = campaign["duration_days"]
                credits_cost = campaign["credits_cost"]
                
                # Check if boost already exists
                existing_boost = ProductBoost.query.filter_by(
                    product_id=product.id,
                    is_active=True
                ).first()
                
                if existing_boost:
                    print(f"✓ Product '{product.name}' already has active boost")
                    continue
                
                # Create boost
                expires_at = datetime.utcnow() + timedelta(days=duration_days)

                boost = ProductBoost(
                    product_id=product.id,
                    user_id=demo_user.id,
                    credits_spent=credits_cost,
                    duration_days=duration_days,
                    expires_at=expires_at,
                    is_active=True
                )
                db.session.add(boost)
                
                # Deduct credits from user
                demo_user.credit_balance -= credits_cost
                
                # Create credit transaction
                boost_transaction = CreditTransaction(
                    user_id=demo_user.id,
                    amount=-credits_cost,
                    transaction_type=CreditTransactionType.BOOST,
                    description=f"Boost produit: {product.name} ({duration_days} jours)",
                    payment_status="completed"
                )
                db.session.add(boost_transaction)
                
                print(f"✓ Created {duration_days}-day boost for '{product.name}' ({credits_cost} crédits)")
            
            # Add some performance data to boosts
            print(f"\n📊 Adding Performance Data...")
            boosts = ProductBoost.query.filter_by(user_id=demo_user.id, is_active=True).all()
            for boost in boosts:
                # Simulate some impressions and clicks
                boost.impressions = random.randint(150, 500)
                boost.clicks = random.randint(10, boost.impressions // 8)
                print(f"✓ Added performance data to boost for '{boost.product.name}': {boost.impressions} impressions, {boost.clicks} clicks")
            
            db.session.commit()
            
            print(f"\n🎉 Demo Boost Campaign Created Successfully!")
            print("=" * 50)
            print(f"📧 Demo User Email: {demo_user.email}")
            print(f"🔑 Demo User Password: demo123")
            print(f"💰 Credits Remaining: {demo_user.credit_balance}")
            print(f"🏪 Demo Shop: {demo_shop.name}")
            print(f"📦 Products Created: {len(created_products)}")
            print(f"🚀 Active Boosts: {len([b for b in boosts if b.is_active])}")
            
            print(f"\n📋 Test Instructions:")
            print(f"1. Login with: {demo_user.email} / demo123")
            print(f"2. Go to 'Mes Crédits' to see credit balance")
            print(f"3. Visit 'Mes Produits' to see boosted products")
            print(f"4. Check 'Mes Boosts' for boost performance")
            print(f"5. Browse categories to see boosted products appear first")
            print(f"6. Try boosting more products with remaining credits")
            
            print(f"\n🌐 URLs to Test:")
            print(f"• Shop URL: http://127.0.0.1:5000/shop/{demo_shop.slug}")
            print(f"• Credits Dashboard: http://127.0.0.1:5000/shop/credits")
            print(f"• Boost Management: http://127.0.0.1:5000/shop/boosts")
            print(f"• Product Management: http://127.0.0.1:5000/shop/products")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error creating demo campaign: {e}")
            raise

if __name__ == '__main__':
    create_demo_boost_campaign()
