#!/usr/bin/env python3
"""
Script to add MTN Money demo payment methods to existing shops
"""

from app import create_app
from models import db, Shop

def add_mtn_money_demo():
    """Add MTN Money demo payment methods to existing shops"""
    app = create_app()
    
    with app.app_context():
        try:
            shops = Shop.query.all()
            
            if not shops:
                print("No shops found to update")
                return
            
            # Demo MTN Money payment methods for different shops
            mtn_money_data = [
                # Shop 1 (Sénégal) - Mode Africaine Élégante
                {
                    'methods': [
                        {
                            'type': 'orange_money',
                            'details': {
                                'phone_number': '+************',
                                'account_name': 'Mode Africaine Élégante'
                            }
                        },
                        {
                            'type': 'wave',
                            'details': {
                                'phone_number': '+************',
                                'account_name': 'Mode Africaine Élégante'
                            }
                        }
                    ]
                },
                # Shop 2 (Ghana) - TechAfrika Solutions
                {
                    'methods': [
                        {
                            'type': 'mtn_money',
                            'details': {
                                'phone_number': '+************',
                                'account_name': 'TechAfrika Solutions Ltd'
                            }
                        },
                        {
                            'type': 'mtn_momo',
                            'details': {
                                'phone_number': '+************',
                                'account_name': 'TechAfrika Solutions',
                                'momo_name': 'TechAfrika'
                            }
                        }
                    ]
                },
                # Shop 3 (Sénégal) - Artisanat Traditionnel
                {
                    'methods': [
                        {
                            'type': 'orange_money',
                            'details': {
                                'phone_number': '+************',
                                'account_name': 'Fatou Diallo'
                            }
                        }
                    ]
                }
            ]
            
            # Update shops with MTN Money payment methods
            for i, shop in enumerate(shops[:3]):  # Update first 3 shops
                if i < len(mtn_money_data):
                    # Initialize payment_info if it doesn't exist
                    if not shop.payment_info:
                        shop.payment_info = {}
                    
                    # Add new methods to existing ones
                    existing_methods = shop.payment_info.get('methods', [])
                    new_methods = mtn_money_data[i]['methods']
                    
                    # Remove duplicates and add new methods
                    method_types = [m.get('type') for m in existing_methods]
                    for new_method in new_methods:
                        if new_method['type'] not in method_types:
                            existing_methods.append(new_method)
                            print(f"✅ Added {new_method['type']} to {shop.name}")
                        else:
                            print(f"⚠️  {new_method['type']} already exists for {shop.name}")
                    
                    shop.payment_info['methods'] = existing_methods
            
            db.session.commit()
            print(f"\n✅ Successfully updated MTN Money payment methods!")
            
            # Display current payment methods for all shops
            print("\n📊 Current Payment Methods by Shop:")
            for shop in shops:
                print(f"\n🏪 {shop.name} ({shop.country}):")
                if shop.payment_info and shop.payment_info.get('methods'):
                    for method in shop.payment_info['methods']:
                        method_type = method.get('type', 'Unknown')
                        details = method.get('details', {})
                        phone = details.get('phone_number', 'N/A')
                        name = details.get('account_name', details.get('momo_name', 'N/A'))
                        print(f"   💳 {method_type}: {phone} ({name})")
                else:
                    print("   ❌ No payment methods configured")
            
        except Exception as e:
            print(f"❌ Error updating MTN Money methods: {e}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    add_mtn_money_demo()
