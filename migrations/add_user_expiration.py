#!/usr/bin/env python3
"""
Migration script to add expiration_date column to User table
"""

import sqlite3
import os
from datetime import datetime

def migrate_database():
    """Add expiration_date column to User table."""

    # Database path
    db_path = 'instance/afromall.db'

    if not os.path.exists(db_path):
        print(f"Database not found at {db_path}")
        return False

    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check if column already exists
        cursor.execute("PRAGMA table_info(user)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'expiration_date' in columns:
            print("Column 'expiration_date' already exists in user table.")
            conn.close()
            return True

        # Add the expiration_date column
        print("Adding expiration_date column to user table...")
        cursor.execute("""
            ALTER TABLE user
            ADD COLUMN expiration_date DATETIME
        """)

        # Commit changes
        conn.commit()
        print("Successfully added expiration_date column to user table.")

        # Verify the column was added
        cursor.execute("PRAGMA table_info(user)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'expiration_date' in columns:
            print("Migration completed successfully!")
            return True
        else:
            print("Error: Column was not added properly.")
            return False

    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("Starting user expiration migration...")
    success = migrate_database()
    if success:
        print("Migration completed successfully!")
    else:
        print("Migration failed!")
