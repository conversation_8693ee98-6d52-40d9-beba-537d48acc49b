#!/usr/bin/env python3
"""
Migration script to add credit approval system to CreditTransaction model.
This adds approval fields to enable admin approval workflow for credit purchases.
"""

import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from models import db
from sqlalchemy import text

def add_credit_approval_system():
    """Add approval system fields to CreditTransaction table."""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔄 Adding credit approval system...")
            
            # Check if columns already exist
            inspector = db.inspect(db.engine)
            existing_columns = [col['name'] for col in inspector.get_columns('credit_transaction')]
            
            # Add new columns if they don't exist
            with db.engine.connect() as conn:
                if 'is_approved' not in existing_columns:
                    conn.execute(text('ALTER TABLE credit_transaction ADD COLUMN is_approved BOOLEAN NOT NULL DEFAULT FALSE'))
                    print("✓ Added is_approved column")

                if 'approved_by' not in existing_columns:
                    conn.execute(text('ALTER TABLE credit_transaction ADD COLUMN approved_by INTEGER'))
                    print("✓ Added approved_by column")

                if 'approved_at' not in existing_columns:
                    conn.execute(text('ALTER TABLE credit_transaction ADD COLUMN approved_at DATETIME'))
                    print("✓ Added approved_at column")

                if 'admin_notes' not in existing_columns:
                    conn.execute(text('ALTER TABLE credit_transaction ADD COLUMN admin_notes TEXT'))
                    print("✓ Added admin_notes column")

                # Update payment_status default if needed
                if 'payment_status' in existing_columns:
                    # Update existing NULL payment_status to 'pending'
                    conn.execute(text("UPDATE credit_transaction SET payment_status = 'pending' WHERE payment_status IS NULL"))
                    print("✓ Updated NULL payment_status to 'pending'")

                # Add foreign key constraint for approved_by if it doesn't exist
                try:
                    conn.execute(text('ALTER TABLE credit_transaction ADD CONSTRAINT fk_credit_transaction_approved_by FOREIGN KEY (approved_by) REFERENCES user (id)'))
                    print("✓ Added foreign key constraint for approved_by")
                except Exception as e:
                    if "already exists" not in str(e).lower():
                        print(f"⚠️  Could not add foreign key constraint: {e}")

                conn.commit()
            
            # Update existing transactions to be approved (for backward compatibility)
            # Only update transactions that are not purchases or are welcome credits
            from models import CreditTransactionType
            
            # Auto-approve non-purchase transactions (welcome credits, admin adjustments, etc.)
            with db.engine.connect() as conn:
                result = conn.execute(text("""
                    UPDATE credit_transaction
                    SET is_approved = TRUE,
                        payment_status = 'completed',
                        approved_at = created_at
                    WHERE transaction_type != 'purchase'
                    AND is_approved = FALSE
                """))

                auto_approved_count = result.rowcount
                if auto_approved_count > 0:
                    print(f"✓ Auto-approved {auto_approved_count} non-purchase transactions")

                # Set purchase transactions to pending for admin review
                result = conn.execute(text("""
                    UPDATE credit_transaction
                    SET payment_status = 'pending',
                        is_approved = FALSE
                    WHERE transaction_type = 'purchase'
                    AND payment_status = 'completed'
                """))

                pending_count = result.rowcount
                if pending_count > 0:
                    print(f"✓ Set {pending_count} purchase transactions to pending for admin review")

                conn.commit()
            
            print("\n🎉 Credit approval system migration completed successfully!")
            print("\n📋 Summary:")
            print("• Added approval workflow fields to credit_transaction table")
            print("• Auto-approved existing non-purchase transactions")
            print("• Set existing purchase transactions to pending for admin review")
            print("• Admins can now approve/reject credit purchases at /admin/credits")
            
        except Exception as e:
            print(f"❌ Error during migration: {e}")
            raise

if __name__ == '__main__':
    add_credit_approval_system()
