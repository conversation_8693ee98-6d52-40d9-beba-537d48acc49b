#!/usr/bin/env python3
"""
Migration script to add advertisement and credit system tables
"""

import os
import sys
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from models import db

def run_migration():
    """Run the migration to add new tables."""
    app = create_app()
    
    with app.app_context():
        print("Starting migration: Adding advertisement and credit system tables...")
        
        try:
            # Create all tables (this will create new ones and skip existing ones)
            db.create_all()
            
            # Add credit balance column to existing users if it doesn't exist
            try:
                with db.engine.connect() as conn:
                    conn.execute(db.text("ALTER TABLE user ADD COLUMN credit_balance INTEGER DEFAULT 0 NOT NULL"))
                    conn.commit()
                print("✓ Added credit_balance column to user table")
            except Exception as e:
                if "duplicate column name" in str(e).lower() or "already exists" in str(e).lower():
                    print("✓ credit_balance column already exists in user table")
                else:
                    print(f"⚠ Warning adding credit_balance column: {e}")
                    # Try alternative approach
                    try:
                        with db.engine.connect() as conn:
                            # Check if column exists first
                            result = conn.execute(db.text("PRAGMA table_info(user)"))
                            columns = [row[1] for row in result.fetchall()]
                            if 'credit_balance' not in columns:
                                conn.execute(db.text("ALTER TABLE user ADD COLUMN credit_balance INTEGER DEFAULT 0 NOT NULL"))
                                conn.commit()
                                print("✓ Added credit_balance column to user table (alternative method)")
                            else:
                                print("✓ credit_balance column already exists in user table")
                    except Exception as e2:
                        print(f"❌ Failed to add credit_balance column: {e2}")
            
            # Create some default credit packages
            from models import CreditPackage
            
            default_packages = [
                {
                    'name': 'Package Starter',
                    'credits': 10,
                    'price_eur': 5.00,
                    'price_fcfa': 3275,
                    'bonus_credits': 0,
                    'description': 'Parfait pour commencer',
                    'display_order': 1,
                    'is_active': True
                },
                {
                    'name': 'Package Pro',
                    'credits': 25,
                    'price_eur': 10.00,
                    'price_fcfa': 6550,
                    'bonus_credits': 5,
                    'description': 'Le plus populaire - 5 crédits bonus !',
                    'display_order': 2,
                    'is_active': True
                },
                {
                    'name': 'Package Business',
                    'credits': 50,
                    'price_eur': 18.00,
                    'price_fcfa': 11790,
                    'bonus_credits': 15,
                    'description': 'Meilleure valeur - 15 crédits bonus !',
                    'display_order': 3,
                    'is_active': True
                }
            ]
            
            for package_data in default_packages:
                existing = CreditPackage.query.filter_by(name=package_data['name']).first()
                if not existing:
                    package = CreditPackage(**package_data)
                    db.session.add(package)
                    print(f"✓ Created credit package: {package_data['name']}")
                else:
                    print(f"✓ Credit package already exists: {package_data['name']}")
            
            # Create a sample advertisement
            from models import Advertisement, AdType, AdStatus
            
            sample_ad = Advertisement.query.filter_by(name='Publicité d\'exemple').first()
            if not sample_ad:
                sample_ad = Advertisement(
                    name='Publicité d\'exemple',
                    ad_type=AdType.TEXT,
                    status=AdStatus.DRAFT,
                    title='Découvrez nos produits !',
                    content='Explorez notre large gamme de produits africains authentiques.',
                    display_order=1
                )
                db.session.add(sample_ad)
                print("✓ Created sample advertisement")
            else:
                print("✓ Sample advertisement already exists")
            
            db.session.commit()
            print("✅ Migration completed successfully!")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Migration failed: {e}")
            raise

if __name__ == '__main__':
    run_migration()
