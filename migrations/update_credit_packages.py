#!/usr/bin/env python3
"""
Migration script to update credit packages with new pricing
"""

import os
import sys

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from models import db, CreditPackage

def update_credit_packages():
    """Update existing credit packages with new pricing."""
    app = create_app()
    
    with app.app_context():
        print("Starting credit package update...")
        
        try:
            # Updated package data
            updated_packages = [
                {
                    'name': 'Package Starter',
                    'credits': 100,
                    'price_eur': 1.53,
                    'price_fcfa': 1000,
                    'bonus_credits': 0,
                    'description': 'Parfait pour commencer'
                },
                {
                    'name': 'Package Pro',
                    'credits': 300,
                    'price_eur': 3.05,
                    'price_fcfa': 2000,
                    'bonus_credits': 0,
                    'description': 'Le plus populaire !'
                },
                {
                    'name': 'Package Business',
                    'credits': 500,
                    'price_eur': 4.58,
                    'price_fcfa': 3000,
                    'bonus_credits': 0,
                    'description': 'Meilleure valeur !'
                }
            ]
            
            for package_data in updated_packages:
                existing = CreditPackage.query.filter_by(name=package_data['name']).first()
                if existing:
                    # Update existing package
                    existing.credits = package_data['credits']
                    existing.price_eur = package_data['price_eur']
                    existing.price_fcfa = package_data['price_fcfa']
                    existing.bonus_credits = package_data['bonus_credits']
                    existing.description = package_data['description']
                    print(f"✓ Updated package: {package_data['name']}")
                else:
                    # Create new package
                    package = CreditPackage(
                        name=package_data['name'],
                        credits=package_data['credits'],
                        price_eur=package_data['price_eur'],
                        price_fcfa=package_data['price_fcfa'],
                        bonus_credits=package_data['bonus_credits'],
                        description=package_data['description'],
                        display_order=len(updated_packages),
                        is_active=True
                    )
                    db.session.add(package)
                    print(f"✓ Created package: {package_data['name']}")
            
            db.session.commit()
            print("✅ Credit packages updated successfully!")
            
            # Display current packages
            print("\nCurrent Credit Packages:")
            packages = CreditPackage.query.order_by(CreditPackage.display_order).all()
            for package in packages:
                print(f"- {package.name}: {package.get_total_credits()} crédits ({package.price_fcfa} FCFA)")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Update failed: {e}")
            raise

if __name__ == '__main__':
    update_credit_packages()
