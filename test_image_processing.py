#!/usr/bin/env python3
"""
Test image processing with known working AliExpress URLs
"""

from services.aliexpress_service import AliExpressService
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_image_processing():
    """Test image processing with known working URLs"""
    
    service = AliExpressService()
    
    # Test with a mock product that has the working image URL you provided
    test_product = {
        'product_id': 'test123',
        'product_title': 'Test Product with Working Image',
        'target_sale_price': 19.99,
        'target_original_price': 29.99,
        'target_sale_price_currency': 'USD',
        'product_main_image_url': 'https://ae-pic-a1.aliexpress-media.com/kf/Sae045169cae34cd29414a480e176b74cx.jpg',
        'product_small_image_urls': {
            'string': [
                'https://ae-pic-a1.aliexpress-media.com/kf/Sae045169cae34cd29414a480e176b74cx.jpg',
                'https://ae-pic-a1.aliexpress-media.com/kf/S123456789abcdef.jpg'
            ]
        }
    }
    
    print('🧪 Testing image processing with working URL...')
    print(f'📸 Input main image: {test_product["product_main_image_url"]}')
    print(f'📸 Input small images: {test_product["product_small_image_urls"]}')
    
    formatted = service.format_product_for_import(test_product)
    
    if formatted:
        print(f'✅ Formatted product successfully!')
        print(f'📸 Images found: {formatted.get("images", [])}')
        print(f'🏷️  Product name: {formatted.get("name", "")}')
        print(f'💰 Price: {formatted.get("price", 0)}')
        
        # Test if the first image is accessible
        images = formatted.get("images", [])
        if images:
            first_image = images[0]
            print(f'\n🔗 Testing first image URL: {first_image}')
            
            if first_image.startswith('http'):
                import requests
                try:
                    response = requests.head(first_image, timeout=10)
                    print(f'✅ Image URL accessible: {response.status_code}')
                    print(f'📋 Content-Type: {response.headers.get("content-type", "unknown")}')
                except Exception as e:
                    print(f'❌ Image URL not accessible: {str(e)}')
            else:
                print(f'📁 Local placeholder image: {first_image}')
        
        return True
    else:
        print('❌ Failed to format product')
        return False

def test_various_image_structures():
    """Test different possible image data structures"""
    
    service = AliExpressService()
    
    test_cases = [
        {
            'name': 'Case 1: Main image + string array',
            'product': {
                'product_id': 'test1',
                'product_title': 'Test Case 1',
                'target_sale_price': 10.0,
                'product_main_image_url': 'https://ae-pic-a1.aliexpress-media.com/kf/Sae045169cae34cd29414a480e176b74cx.jpg',
                'product_small_image_urls': {
                    'string': [
                        'https://ae-pic-a1.aliexpress-media.com/kf/S111111111111111.jpg',
                        'https://ae-pic-a1.aliexpress-media.com/kf/S222222222222222.jpg'
                    ]
                }
            }
        },
        {
            'name': 'Case 2: Main image + single string',
            'product': {
                'product_id': 'test2',
                'product_title': 'Test Case 2',
                'target_sale_price': 15.0,
                'product_main_image_url': 'https://ae-pic-a1.aliexpress-media.com/kf/Sae045169cae34cd29414a480e176b74cx.jpg',
                'product_small_image_urls': {
                    'string': 'https://ae-pic-a1.aliexpress-media.com/kf/S333333333333333.jpg'
                }
            }
        },
        {
            'name': 'Case 3: Main image only',
            'product': {
                'product_id': 'test3',
                'product_title': 'Test Case 3',
                'target_sale_price': 20.0,
                'product_main_image_url': 'https://ae-pic-a1.aliexpress-media.com/kf/Sae045169cae34cd29414a480e176b74cx.jpg'
            }
        },
        {
            'name': 'Case 4: No images',
            'product': {
                'product_id': 'test4',
                'product_title': 'Test Case 4',
                'target_sale_price': 25.0
            }
        }
    ]
    
    print('\n🧪 Testing various image data structures...')
    
    for test_case in test_cases:
        print(f'\n📋 {test_case["name"]}')
        formatted = service.format_product_for_import(test_case['product'])
        
        if formatted:
            images = formatted.get('images', [])
            print(f'   ✅ Success: {len(images)} images found')
            for i, img in enumerate(images):
                print(f'     {i+1}. {img}')
        else:
            print(f'   ❌ Failed to format')

if __name__ == "__main__":
    print("🚀 Testing AliExpress Image Processing")
    print("=" * 60)
    
    # Test basic functionality
    success = test_image_processing()
    
    if success:
        # Test various structures
        test_various_image_structures()
    
    print("\n" + "=" * 60)
    print("🏁 Test completed!")
