#!/usr/bin/env python3
"""
Script to add demo payment methods to existing shops
"""

from app import create_app
from models import db, Shop

def add_demo_payment_methods():
    """Add demo payment methods to existing shops"""
    app = create_app()
    
    with app.app_context():
        try:
            shops = Shop.query.all()
            
            if not shops:
                print("No shops found to update")
                return
            
            # Demo payment methods for different shops
            payment_methods_data = [
                # Shop 1 (Sénégal) - Mode Africaine Élégante
                {
                    'methods': [
                        {
                            'type': 'orange_money',
                            'details': {
                                'phone_number': '+************',
                                'account_name': 'Mode Africaine Élégante'
                            }
                        },
                        {
                            'type': 'wave',
                            'details': {
                                'phone_number': '+************',
                                'account_name': 'Mode Africaine Élégante'
                            }
                        },
                        {
                            'type': 'bank_account',
                            'details': {
                                'bank_name': 'Banque Atlantique Sénégal',
                                'account_number': '***********',
                                'account_name': 'Mode Africaine Élégante SARL',
                                'swift_code': 'CBAOSNDA'
                            }
                        }
                    ]
                },
                # Shop 2 (Ghana) - TechAfrika Solutions
                {
                    'methods': [
                        {
                            'type': 'mtn_money',
                            'details': {
                                'phone_number': '+************',
                                'account_name': 'TechAfrika Solutions'
                            }
                        },
                        {
                            'type': 'bank_account',
                            'details': {
                                'bank_name': 'Ghana Commercial Bank',
                                'account_number': '**********',
                                'account_name': 'TechAfrika Solutions Ltd',
                                'swift_code': 'GHCBGHAC'
                            }
                        },
                        {
                            'type': 'paypal',
                            'details': {
                                'paypal_email': '<EMAIL>'
                            }
                        }
                    ]
                },
                # Shop 3 (Sénégal) - Artisanat Traditionnel
                {
                    'methods': [
                        {
                            'type': 'orange_money',
                            'details': {
                                'phone_number': '+************',
                                'account_name': 'Fatou Diallo'
                            }
                        },
                        {
                            'type': 'custom',
                            'details': {
                                'method_name': 'Western Union',
                                'account_details': 'Fatou DIALLO - Saint-Louis, Sénégal',
                                'instructions': 'Envoyez le paiement via Western Union au nom de Fatou DIALLO à Saint-Louis, Sénégal. Envoyez-moi le code de transfert par WhatsApp au +************.'
                            }
                        }
                    ]
                }
            ]
            
            # Update shops with payment methods
            for i, shop in enumerate(shops[:3]):  # Update first 3 shops
                if i < len(payment_methods_data):
                    shop.payment_info = payment_methods_data[i]
                    print(f"✅ Updated payment methods for {shop.name}")
                    
                    # Display the payment methods
                    for method in payment_methods_data[i]['methods']:
                        print(f"   - {method['type']}: {method['details']}")
            
            db.session.commit()
            print(f"\n✅ Successfully updated payment methods for {min(len(shops), 3)} shops!")
            
        except Exception as e:
            print(f"❌ Error updating payment methods: {e}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    add_demo_payment_methods()
