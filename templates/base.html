<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Afroly.org - Centre Commercial Africain{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="{{ url_for('main.index') }}">
                <i class="fas fa-store me-2"></i>Afroly.org
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Search Form -->
                <form class="d-flex mx-auto" style="width: 40%;" action="{{ url_for('main.search') }}" method="GET">
                    <div class="input-group">
                        <input class="form-control" type="search" name="q" placeholder="Rechercher produits, boutiques..."
                               value="{{ request.args.get('q', '') }}">
                        <button class="btn btn-outline-light" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>

                <!-- Navigation Links -->
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}">Accueil</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('shop.browse') }}">Boutiques</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.categories') }}">Catégories</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.pricing') }}">Tarification</a>
                    </li>
                    {% if current_user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="{{ url_for('cart.my_carts') }}">
                            <i class="fas fa-shopping-cart me-1"></i>Mon Panier
                            {% if cart_items_count > 0 %}
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                {{ cart_items_count }}
                                <span class="visually-hidden">articles dans le panier</span>
                            </span>
                            {% endif %}
                        </a>
                    </li>
                    {% endif %}

                    {% if current_user.is_authenticated %}

                        <!-- User Dropdown -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> {{ current_user.first_name }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">Profil</a></li>
                                {% if current_user.role == UserRole.VENDOR %}
                                    <li><a class="dropdown-item" href="{{ url_for('shop.vendor_dashboard') }}">Tableau de Bord Vendeur</a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('shop.credits_dashboard') }}">
                                        <i class="fas fa-coins me-2"></i>Mes Crédits ({{ current_user.credit_balance }})
                                    </a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('shop.payment_methods') }}">
                                        <i class="fas fa-credit-card me-2"></i>Méthodes de Paiement
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{{ url_for('main.help') }}">
                                        <i class="fas fa-question-circle me-2"></i>Centre d'Aide
                                    </a></li>
                                {% endif %}
                                {% if current_user.role == UserRole.ADMIN %}
                                    <li><a class="dropdown-item" href="{{ url_for('admin.dashboard') }}">Panneau Admin</a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('blog.admin_index') }}">Gestion Blog</a></li>
                                {% endif %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">Déconnexion</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth.login') }}">Connexion</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn btn-outline-light ms-2" href="{{ url_for('auth.register') }}">S'inscrire</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light mt-5">
        <div class="container py-5">
            <div class="row">
                <div class="col-md-3">
                    <h5>Afroly.org</h5>
                    <p>Votre destination shopping africaine de premier plan. Connecter acheteurs et vendeurs à travers l'Afrique.</p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
                <div class="col-md-3">
                    <h6>Liens Rapides</h6>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('main.about') }}" class="text-light">À Propos</a></li>
                        <li><a href="{{ url_for('main.contact') }}" class="text-light">Contact</a></li>
                        <li><a href="{{ url_for('blog.index') }}" class="text-light">Blog</a></li>
                        <li><a href="{{ url_for('shop.browse') }}" class="text-light">Parcourir Boutiques</a></li>
                        <li><a href="{{ url_for('main.categories') }}" class="text-light">Catégories</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h6>Pour les Vendeurs</h6>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('auth.register') }}" class="text-light">Devenir Vendeur</a></li>
                        <li><a href="{{ url_for('shop.vendor_dashboard') }}" class="text-light">Tableau de Bord</a></li>
                        <li><a href="#" class="text-light">Guide Vendeur</a></li>
                        <li><a href="{{ url_for('main.pricing') }}" class="text-light">Tarification</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h6>Support</h6>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('main.help') }}" class="text-light">Centre d'Aide</a></li>
                        <li><a href="{{ url_for('main.privacy') }}" class="text-light">Politique de Confidentialité</a></li>
                        <li><a href="{{ url_for('main.terms') }}" class="text-light">Conditions d'Utilisation</a></li>
                        <li><a href="{{ url_for('main.help') }}" class="text-light">FAQ</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 Afroly.org. Tous droits réservés.</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">Offert avec Amour par <a href="https://www.facebook.com/chocolat237/" target="_blank" class="text-warning fw-bold text-decoration-none">Croquette De Chocolat</a></p>
                </div>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Floating Button -->
    <div id="whatsapp-float" class="whatsapp-float" style="display: none;">
        <a href="#" id="whatsapp-link" target="_blank" class="whatsapp-button">
            <i class="fab fa-whatsapp"></i>
            <span class="whatsapp-text">Contactez-nous</span>
        </a>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- Auto-hide flash messages -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-hide success and info flash messages
        const alerts = document.querySelectorAll('.alert-dismissible');
        alerts.forEach(function(alert) {
            // Check if this is an important message that should not auto-disappear
            const alertText = alert.textContent || alert.innerText;
            const isPersistentMessage = alertText.includes('DEMANDE SOUMISE') ||
                                      alertText.includes('crédits a été soumise') ||
                                      alertText.includes('Transaction approuvée') ||
                                      alertText.includes('Transaction rejetée') ||
                                      alertText.includes('NOUVEAU') ||
                                      alertText.includes('Crédits de publicité GRATUITS') ||
                                      alertText.includes('Comment ça marche') ||
                                      alertText.includes('Informations de Paiement');

            if ((alert.classList.contains('alert-success') || alert.classList.contains('alert-info')) && !isPersistentMessage) {
                setTimeout(function() {
                    const closeBtn = alert.querySelector('.btn-close');
                    if (closeBtn && alert.style.display !== 'none') {
                        // Add fade out animation
                        alert.style.transition = 'opacity 0.5s ease-out';
                        alert.style.opacity = '0';
                        setTimeout(function() {
                            closeBtn.click();
                        }, 500);
                    }
                }, 120000); // Hide after 2 minutes (120 seconds)
            }
            // Keep error and warning messages visible longer (but not promotional messages)
            else if (alert.classList.contains('alert-warning') && !isPersistentMessage) {
                setTimeout(function() {
                    const closeBtn = alert.querySelector('.btn-close');
                    if (closeBtn && alert.style.display !== 'none') {
                        alert.style.transition = 'opacity 0.5s ease-out';
                        alert.style.opacity = '0';
                        setTimeout(function() {
                            closeBtn.click();
                        }, 500);
                    }
                }, 180000); // Hide after 3 minutes (180 seconds)
            }
            // Error messages stay until manually dismissed
        });
    });
    </script>

    {% block extra_scripts %}{% endblock %}
</body>
</html>
