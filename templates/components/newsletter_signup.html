<!-- Newsletter Signup Component -->
<div class="newsletter-signup bg-primary text-white py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h3 class="mb-3">
                    <i class="fas fa-envelope me-2"></i>
                    Restez informé(e) des dernières nouveautés !
                </h3>
                <p class="lead mb-4">
                    Recevez nos newsletters avec les meilleures offres, nouveaux produits et conseils pour entrepreneurs africains.
                </p>
                
                <form id="newsletter-form" class="row g-3 justify-content-center">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="email" 
                                   class="form-control form-control-lg" 
                                   id="newsletter-email" 
                                   placeholder="Votre adresse email"
                                   required>
                            <button type="submit" class="btn btn-warning btn-lg">
                                <i class="fas fa-paper-plane me-2"></i>S'abonner
                            </button>
                        </div>
                    </div>
                </form>
                
                <div id="newsletter-message" class="mt-3" style="display: none;"></div>
                
                <p class="small mt-3 opacity-75">
                    <i class="fas fa-shield-alt me-1"></i>
                    Vos données sont protégées. Désabonnement possible à tout moment.
                </p>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('newsletter-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const email = document.getElementById('newsletter-email').value;
    const messageDiv = document.getElementById('newsletter-message');
    const submitBtn = this.querySelector('button[type="submit"]');
    
    // Show loading state
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Inscription...';
    submitBtn.disabled = true;
    
    // Send subscription request
    fetch('/newsletter/subscribe', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: email })
    })
    .then(response => response.json())
    .then(data => {
        messageDiv.style.display = 'block';
        
        if (data.success) {
            messageDiv.className = 'alert alert-success';
            messageDiv.innerHTML = '<i class="fas fa-check-circle me-2"></i>' + data.message;
            document.getElementById('newsletter-email').value = '';
        } else {
            messageDiv.className = 'alert alert-warning';
            messageDiv.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>' + data.message;
        }
    })
    .catch(error => {
        messageDiv.style.display = 'block';
        messageDiv.className = 'alert alert-danger';
        messageDiv.innerHTML = '<i class="fas fa-times-circle me-2"></i>Erreur lors de l\'inscription. Veuillez réessayer.';
    })
    .finally(() => {
        // Reset button
        submitBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>S\'abonner';
        submitBtn.disabled = false;
        
        // Hide message after 5 seconds
        setTimeout(() => {
            messageDiv.style.display = 'none';
        }, 5000);
    });
});
</script>

<style>
.newsletter-signup {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    position: relative;
    overflow: hidden;
}

.newsletter-signup::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.newsletter-signup .container {
    position: relative;
    z-index: 1;
}

.newsletter-signup .input-group {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    border-radius: 50px;
    overflow: hidden;
}

.newsletter-signup .form-control {
    border: none;
    border-radius: 50px 0 0 50px;
    padding: 15px 20px;
}

.newsletter-signup .btn {
    border-radius: 0 50px 50px 0;
    padding: 15px 25px;
    border: none;
    font-weight: bold;
}

.newsletter-signup .btn:hover {
    background-color: #ffc107;
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

@media (max-width: 768px) {
    .newsletter-signup .input-group {
        flex-direction: column;
        border-radius: 10px;
    }
    
    .newsletter-signup .form-control,
    .newsletter-signup .btn {
        border-radius: 10px;
        margin-bottom: 10px;
    }
    
    .newsletter-signup .btn {
        margin-bottom: 0;
    }
}
</style>
