<!-- Email-specific Flash Messages Component -->
<!-- These messages stay visible until manually dismissed -->
{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        <div class="container-fluid mb-3">
            {% for category, message in messages %}
                <!-- Check if this is an email-related message -->
                {% set is_email_message = 'SMTP' in message or 'email' in message or 'Email' in message or 
                                         'template' in message or 'Template' in message or 
                                         'newsletter' in message or 'Newsletter' in message or 
                                         'abonné' in message or 'envoyé' in message or 
                                         'configuration' in message or 'Configuration' in message %}
                
                {% if is_email_message %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible" role="alert">
                        <div class="d-flex align-items-start">
                            <div class="me-3">
                                {% if category == 'success' %}
                                <i class="fas fa-check-circle fa-lg text-success"></i>
                                {% elif category == 'error' %}
                                <i class="fas fa-exclamation-triangle fa-lg text-danger"></i>
                                {% elif category == 'warning' %}
                                <i class="fas fa-exclamation-circle fa-lg text-warning"></i>
                                {% else %}
                                <i class="fas fa-info-circle fa-lg text-info"></i>
                                {% endif %}
                            </div>
                            <div class="flex-grow-1">
                                <strong>
                                    {% if category == 'success' %}
                                    Succès :
                                    {% elif category == 'error' %}
                                    Erreur :
                                    {% elif category == 'warning' %}
                                    Attention :
                                    {% else %}
                                    Information :
                                    {% endif %}
                                </strong>
                                {{ message }}
                                
                                <!-- Add helpful context for email messages -->
                                {% if 'SMTP' in message and 'mis à jour' in message %}
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-lightbulb me-1"></i>
                                        <strong>Conseil :</strong> Testez votre configuration en envoyant un email de test.
                                    </small>
                                </div>
                                {% elif 'Template' in message and 'créé' in message %}
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-lightbulb me-1"></i>
                                        <strong>Conseil :</strong> Vous pouvez maintenant utiliser ce template pour vos emails automatiques.
                                    </small>
                                </div>
                                {% elif 'Newsletter' in message and 'envoyée' in message %}
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-lightbulb me-1"></i>
                                        <strong>Conseil :</strong> Consultez les statistiques d'envoi dans la file d'attente email.
                                    </small>
                                </div>
                                {% elif 'test envoyé' in message %}
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-lightbulb me-1"></i>
                                        <strong>Conseil :</strong> Vérifiez votre boîte de réception (et le dossier spam) pour confirmer la réception.
                                    </small>
                                </div>
                                {% elif 'abonné' in message and 'newsletter' in message %}
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-lightbulb me-1"></i>
                                        <strong>Conseil :</strong> Vous pouvez maintenant créer et envoyer des newsletters à vos abonnés.
                                    </small>
                                </div>
                                {% endif %}
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fermer">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    </div>
                {% else %}
                    <!-- Regular flash message for non-email messages -->
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endif %}
            {% endfor %}
        </div>
    {% endif %}
{% endwith %}

<style>
/* Enhanced styling for email flash messages */
.alert {
    border-left: 4px solid;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.alert-success {
    border-left-color: #28a745;
    background-color: #f8fff9;
}

.alert-danger {
    border-left-color: #dc3545;
    background-color: #fff8f8;
}

.alert-warning {
    border-left-color: #ffc107;
    background-color: #fffef8;
}

.alert-info {
    border-left-color: #17a2b8;
    background-color: #f8feff;
}

/* Persistent message indicator */
.alert:not(.fade) {
    position: relative;
}

.alert:not(.fade)::before {
    content: "📌";
    position: absolute;
    top: 10px;
    right: 40px;
    font-size: 12px;
    opacity: 0.7;
}

/* Animation for better visibility */
.alert {
    animation: slideInDown 0.5s ease-out;
}

@keyframes slideInDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .alert .d-flex {
        flex-direction: column;
    }
    
    .alert .me-3 {
        margin-right: 0 !important;
        margin-bottom: 10px;
        text-align: center;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add special handling for email flash messages
    const emailAlerts = document.querySelectorAll('.alert:not(.fade)');
    
    emailAlerts.forEach(function(alert) {
        // Add a subtle pulse animation to draw attention
        alert.style.animation = 'slideInDown 0.5s ease-out, pulse 2s infinite';
        
        // Stop pulsing after 10 seconds
        setTimeout(function() {
            alert.style.animation = 'slideInDown 0.5s ease-out';
        }, 10000);
        
        // Add click handler to close button
        const closeBtn = alert.querySelector('.btn-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', function() {
                alert.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-20px)';
                setTimeout(function() {
                    alert.remove();
                }, 300);
            });
        }
    });
});

/* Pulse animation for important messages */
@keyframes pulse {
    0% { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    50% { box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
    100% { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
}
</script>
