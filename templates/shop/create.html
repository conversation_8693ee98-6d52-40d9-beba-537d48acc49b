{% extends "base.html" %}

{% block title %}Créer une Boutique - Afroly.org{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow border-0">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-store me-2"></i>Créer une Nouvelle Boutique
                    </h3>
                    <p class="mb-0 mt-2">Lancez votre business sur afroly.org</p>
                </div>

                <div class="card-body p-4">
                    <!-- Tier Info -->
                    <div class="alert alert-info alert-permanent tier-info mb-4">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>Votre Niveau : {{ current_user.tier.value.title() }}
                        </h6>
                        <p class="mb-2">
                            {% set product_limit = get_product_limit(current_user.tier.value) %}
                            {% if current_user.tier.value == 'free' %}
                            <strong>Gratuit :</strong> 1 boutique maximum • {{ product_limit }} produits par boutique
                            {% elif current_user.tier.value == 'premium' %}
                            <strong>Premium :</strong> 2 boutiques maximum • {{ product_limit }} produits par boutique
                            {% elif current_user.tier.value == 'gold' %}
                            <strong>Gold :</strong> 5 boutiques maximum • {{ product_limit }} produits par boutique
                            {% endif %}
                        </p>
                        {% if not current_user.can_create_shop() %}
                        <div class="alert alert-warning mt-2 mb-0">
                            <strong>Limite atteinte !</strong> Vous avez atteint le nombre maximum de boutiques pour votre niveau.
                            <a href="#" class="alert-link">Améliorer votre compte</a> pour créer plus de boutiques.
                        </div>
                        {% endif %}
                    </div>

                    {% if current_user.can_create_shop() %}
                    <form method="POST" enctype="multipart/form-data">
                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle me-2"></i>Informations de Base
                                </h5>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Nom de la Boutique *</label>
                                    <input type="text" class="form-control" id="name" name="name" required
                                           placeholder="Ex: Mode Africaine Élégante" value="{{ request.form.get('name', '') }}">
                                    <div class="form-text">Ce nom sera visible par tous les clients</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="country" class="form-label">Pays *</label>
                                    <select class="form-select" id="country" name="country" required>
                                        <option value="">Choisir un pays africain</option>
                                        {% for country_option in countries %}
                                        <option value="{{ country_option }}" {% if request.form.get('country') == country_option %}selected{% endif %}>
                                            {{ country_option }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">Sélectionnez le pays où votre boutique est basée</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_email" class="form-label">Email de Contact *</label>
                                    <input type="email" class="form-control" id="contact_email" name="contact_email" required
                                           placeholder="<EMAIL>" value="{{ request.form.get('contact_email', current_user.email) }}">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_phone" class="form-label">Téléphone</label>
                                    <input type="tel" class="form-control" id="contact_phone" name="contact_phone"
                                           placeholder="+221 77 123 45 67" value="{{ request.form.get('contact_phone', current_user.phone) }}">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="address" class="form-label">Adresse *</label>
                                    <input type="text" class="form-control" id="address" name="address" required
                                           placeholder="Ville, Pays" value="{{ request.form.get('address', '') }}">
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description de la Boutique *</label>
                                    <textarea class="form-control" id="description" name="description" rows="4" required
                                              placeholder="Décrivez votre boutique, vos produits et ce qui vous rend unique...">{{ request.form.get('description', '') }}</textarea>
                                    <div class="form-text">Minimum 50 caractères. Cette description aidera les clients à vous découvrir.</div>
                                </div>
                            </div>
                        </div>

                        <!-- Images -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-images me-2"></i>Images de la Boutique
                                </h5>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="logo" class="form-label">Logo de la Boutique</label>
                                    <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                                    <div class="form-text">Format recommandé : carré, 200x200px minimum</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="banner" class="form-label">Bannière de la Boutique</label>
                                    <input type="file" class="form-control" id="banner" name="banner" accept="image/*">
                                    <div class="form-text">Format recommandé : 1200x400px minimum</div>
                                </div>
                            </div>
                        </div>

                        <!-- Preview Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-eye me-2"></i>Aperçu
                                </h5>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center">
                                            <div class="shop-logo-preview bg-white rounded-circle d-flex align-items-center justify-content-center me-3"
                                                 style="width: 60px; height: 60px; border: 2px solid #dee2e6;">
                                                <i class="fas fa-store text-muted"></i>
                                            </div>
                                            <div>
                                                <h6 class="shop-name-preview mb-1">Nom de votre boutique</h6>
                                                <small class="text-muted">Votre description apparaîtra ici...</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Methods Section (Optional) -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-success border-bottom pb-2 mb-3">
                                    <i class="fas fa-credit-card me-2"></i>Méthodes de Paiement (Optionnel)
                                </h5>
                                <p class="text-muted mb-3">
                                    Vous pouvez configurer vos méthodes de paiement maintenant ou plus tard dans les paramètres de votre boutique.
                                </p>

                                {% set available_methods = temp_shop.get_available_payment_methods() %}

                                <!-- Mobile Money -->
                                {% if available_methods.mobile_money %}
                                <div class="payment-category mb-3">
                                    <h6 class="text-success mb-2">
                                        <i class="fas fa-mobile-alt me-2"></i>Mobile Money
                                    </h6>
                                    <div class="row">
                                        {% for method_key, method_config in available_methods.mobile_money.items() %}
                                        <div class="col-md-6 mb-2">
                                            <div class="card border-success">
                                                <div class="card-body p-3">
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input payment-method-toggle" type="checkbox"
                                                               name="payment_methods" value="{{ method_key }}"
                                                               id="{{ method_key }}"
                                                               data-target="#{{ method_key }}_fields">
                                                        <label class="form-check-label fw-bold" for="{{ method_key }}">
                                                            <i class="{{ method_config.icon }} me-2" style="color: {{ method_config.color }}"></i>
                                                            {{ method_config.name }}
                                                        </label>
                                                    </div>

                                                    <div id="{{ method_key }}_fields" class="payment-fields" style="display: none">
                                                        {% for field in method_config.fields %}
                                                        <div class="mb-2">
                                                            <input type="text" class="form-control form-control-sm"
                                                                   name="{{ method_key }}_{{ field }}"
                                                                   placeholder="{% if field == 'phone_number' %}Numéro de téléphone{% elif field == 'account_name' %}Nom du compte{% elif field == 'momo_name' %}Nom MoMo{% endif %}">
                                                        </div>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                <!-- International Payment Methods (Premium+) -->
                                {% if available_methods.international %}
                                <div class="payment-category mb-3">
                                    <h6 class="text-warning mb-2">
                                        <i class="fas fa-globe me-2"></i>Paiements Internationaux
                                        <span class="badge bg-warning text-dark">Premium+</span>
                                    </h6>
                                    <div class="row">
                                        {% for method_key, method_config in available_methods.international.items() %}
                                        <div class="col-md-6 mb-2">
                                            <div class="card border-warning">
                                                <div class="card-body p-3">
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input payment-method-toggle" type="checkbox"
                                                               name="payment_methods" value="{{ method_key }}"
                                                               id="{{ method_key }}"
                                                               data-target="#{{ method_key }}_fields">
                                                        <label class="form-check-label fw-bold" for="{{ method_key }}">
                                                            <i class="{{ method_config.icon }} me-2" style="color: {{ method_config.color }}"></i>
                                                            {{ method_config.name }}
                                                        </label>
                                                    </div>

                                                    <div id="{{ method_key }}_fields" class="payment-fields" style="display: none">
                                                        {% for field in method_config.fields %}
                                                        <div class="mb-2">
                                                            <input type="text" class="form-control form-control-sm"
                                                                   name="{{ method_key }}_{{ field }}"
                                                                   placeholder="{% if field == 'paypal_email' %}Email PayPal{% elif field == 'stripe_account_id' %}ID Compte Stripe{% endif %}">
                                                        </div>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Terms and Submit -->
                        <div class="row">
                            <div class="col-12">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                                    <label class="form-check-label" for="terms">
                                        J'accepte les <a href="{{ url_for('main.terms') }}" target="_blank">conditions d'utilisation</a>
                                        et la <a href="{{ url_for('main.privacy') }}" target="_blank">politique de confidentialité</a>
                                    </label>
                                </div>

                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Important :</strong> Votre boutique sera soumise à approbation avant d'être visible publiquement.
                                    Vous recevrez une notification par email une fois approuvée.
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('shop.vendor_dashboard') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Retour au Tableau de Bord
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-plus me-2"></i>Créer Ma Boutique
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h4>Limite de Boutiques Atteinte</h4>
                        <p class="text-muted mb-4">
                            Vous avez atteint le nombre maximum de boutiques autorisées pour votre niveau actuel.
                        </p>
                        <div class="d-flex gap-3 justify-content-center">
                            <a href="#" class="btn btn-warning">
                                <i class="fas fa-arrow-up me-2"></i>Améliorer Mon Compte
                            </a>
                            <a href="{{ url_for('shop.vendor_dashboard') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Retour au Tableau de Bord
                            </a>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
.shop-logo-preview {
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.card {
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

.alert {
    border-radius: 10px;
}
</style>
{% endblock %}

{% block extra_scripts %}
<script>
// Live preview functionality
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const descriptionInput = document.getElementById('description');
    const logoInput = document.getElementById('logo');

    const namePreview = document.querySelector('.shop-name-preview');
    const descriptionPreview = document.querySelector('.shop-name-preview').nextElementSibling;
    const logoPreview = document.querySelector('.shop-logo-preview');

    // Update name preview
    nameInput.addEventListener('input', function() {
        namePreview.textContent = this.value || 'Nom de votre boutique';
    });

    // Update description preview
    descriptionInput.addEventListener('input', function() {
        const text = this.value || 'Votre description apparaîtra ici...';
        descriptionPreview.textContent = text.length > 60 ? text.substring(0, 60) + '...' : text;
    });

    // Logo preview
    logoInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                logoPreview.innerHTML = `<img src="${e.target.result}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">`;
            };
            reader.readAsDataURL(file);
        }
    });

    // Character counter for description
    const charCounter = document.createElement('div');
    charCounter.className = 'form-text text-end';
    charCounter.style.marginTop = '-15px';
    charCounter.style.marginBottom = '15px';
    descriptionInput.parentNode.appendChild(charCounter);

    function updateCharCounter() {
        const length = descriptionInput.value.length;
        charCounter.textContent = `${length} caractères`;
        charCounter.className = `form-text text-end ${length < 50 ? 'text-danger' : 'text-success'}`;
    }

    descriptionInput.addEventListener('input', updateCharCounter);
    updateCharCounter();

    // Payment method toggle functionality
    document.querySelectorAll('.payment-method-toggle').forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            const targetId = this.getAttribute('data-target');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                if (this.checked) {
                    targetElement.style.display = 'block';
                    // Focus on first input field
                    const firstInput = targetElement.querySelector('input, textarea');
                    if (firstInput) {
                        setTimeout(() => firstInput.focus(), 100);
                    }
                } else {
                    targetElement.style.display = 'none';
                    // Clear all input values when unchecked
                    targetElement.querySelectorAll('input, textarea').forEach(function(input) {
                        input.value = '';
                    });
                }
            }
        });
    });
});
</script>
{% endblock %}
