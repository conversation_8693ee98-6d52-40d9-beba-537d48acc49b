<!-- Payment Methods Display Template -->
{% if shop.get_payment_methods() %}
<div class="payment-methods-section">
    <h6 class="text-primary mb-3">
        <i class="fas fa-credit-card me-2"></i>Méthodes de Paiement Acceptées
    </h6>

    <div class="row">
        {% for method in shop.get_payment_methods() %}
        {% set method_config = None %}
        {% for category, methods in config.PAYMENT_METHODS.items() %}
            {% if method.type in methods %}
                {% set method_config = methods[method.type] %}
                {% break %}
            {% endif %}
        {% endfor %}

        {% if method_config %}
        <div class="col-md-6 mb-3">
            <div class="card border-light">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center mb-2">
                        <i class="{{ method_config.icon }} me-2" style="color: {{ method_config.color }}"></i>
                        <strong>{{ method_config.name }}</strong>
                    </div>

                    {% if method.details %}
                    <div class="payment-details">
                        {% for field, value in method.details.items() %}
                        {% if value %}
                        <div class="small text-muted">
                            {% if field == 'phone_number' %}
                                <i class="fas fa-phone me-1"></i>{{ value }}
                            {% elif field == 'account_name' %}
                                <i class="fas fa-user me-1"></i>{{ value }}
                            {% elif field == 'momo_name' %}
                                <i class="fas fa-mobile-alt me-1"></i>{{ value }}
                            {% elif field == 'bank_name' %}
                                <i class="fas fa-university me-1"></i>{{ value }}
                            {% elif field == 'account_number' %}
                                <i class="fas fa-hashtag me-1"></i>{{ value }}
                            {% elif field == 'paypal_email' %}
                                <i class="fab fa-paypal me-1"></i>{{ value }}
                            {% elif field == 'method_name' %}
                                <i class="fas fa-tag me-1"></i>{{ value }}
                            {% else %}
                                {{ value }}
                            {% endif %}
                        </div>
                        {% endif %}
                        {% endfor %}

                        {% if method.details.instructions %}
                        <div class="mt-2">
                            <small class="text-info">
                                <i class="fas fa-info-circle me-1"></i>
                                {{ method.details.instructions }}
                            </small>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
        {% endfor %}
    </div>
</div>
{% else %}
<div class="alert alert-info">
    <i class="fas fa-info-circle me-2"></i>
    Cette boutique n'a pas encore configuré de méthodes de paiement.
    Contactez le vendeur pour plus d'informations.
</div>
{% endif %}
