{% extends "base.html" %}

{% block title %}Mes Crédits - Afroly.org{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-5 fw-bold">
                <i class="fas fa-coins me-3 text-warning"></i>Mes Crédits
            </h1>
            <p class="lead text-muted">Gérez vos crédits et boostez vos produits</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h2 class="display-4 mb-0">{{ current_user.credit_balance }}</h2>
                    <p class="mb-0">Crédits disponibles</p>
                </div>
            </div>
        </div>
    </div>

    <!-- How it works explanation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h5 class="alert-heading">
                    <i class="fas fa-lightbulb me-2"></i>Comment ça marche ?
                </h5>
                <p class="mb-2">
                    <strong>Boostez vos produits pour une visibilité maximale !</strong>
                </p>
                <ul class="mb-0">
                    <li><strong>Achetez des crédits</strong> avec les packages ci-dessous</li>
                    <li><strong>Utilisez vos crédits</strong> pour booster vos produits</li>
                    <li><strong>Vos produits boostés</strong> apparaissent en premier dans les résultats de recherche et catégories</li>
                    <li><strong>Plus de visibilité</strong> = plus de clics = plus de ventes !</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Credit Packages -->
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="mb-4">
                <i class="fas fa-shopping-cart me-2"></i>Acheter des Crédits
            </h3>
            
            {% if packages %}
            <div class="row">
                {% for package in packages %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 {% if loop.index == 2 %}border-primary{% endif %}">
                        {% if loop.index == 2 %}
                        <div class="card-header bg-primary text-white text-center">
                            <i class="fas fa-star me-1"></i>Le Plus Populaire
                        </div>
                        {% endif %}
                        <div class="card-body text-center">
                            <h5 class="card-title">{{ package.name }}</h5>
                            
                            <div class="display-6 fw-bold text-primary mb-2">
                                {{ package.get_total_credits() }}
                            </div>
                            <div class="text-muted mb-3">crédits</div>
                            
                            {% if package.bonus_credits > 0 %}
                            <div class="badge bg-success mb-3">
                                <i class="fas fa-gift me-1"></i>+{{ package.bonus_credits }} bonus
                            </div>
                            {% endif %}
                            
                            <div class="row mb-3">
                                <div class="col-6">
                                    <div class="fw-bold">{{ package.price_eur }}€</div>
                                    <small class="text-muted">EUR</small>
                                </div>
                                <div class="col-6">
                                    <div class="fw-bold">{{ "%.0f"|format(package.price_fcfa) }} FCFA</div>
                                    <small class="text-muted">FCFA</small>
                                </div>
                            </div>
                            
                            {% if package.description %}
                            <p class="card-text text-muted small">{{ package.description }}</p>
                            {% endif %}
                            
                            <div class="text-muted small mb-3">
                                {{ "%.3f"|format(package.get_price_per_credit_eur()) }}€ par crédit
                            </div>
                        </div>
                        <div class="card-footer">
                            <form method="POST" action="{{ url_for('shop.purchase_credits', package_id=package.id) }}">
                                <button type="submit" class="btn {% if loop.index == 2 %}btn-primary{% else %}btn-outline-primary{% endif %} w-100">
                                    <i class="fas fa-shopping-cart me-2"></i>Acheter
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-coins fa-3x text-muted mb-3"></i>
                <h4>Aucun package disponible</h4>
                <p class="text-muted">Les packages de crédits seront bientôt disponibles.</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Active Boosts -->
    {% if active_boosts %}
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="mb-4">
                <i class="fas fa-rocket me-2"></i>Mes Produits Boostés
            </h3>
            
            <div class="row">
                {% for boost in active_boosts %}
                <div class="col-lg-6 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-3">
                                    <img src="{{ boost.product.get_main_image() }}" 
                                         class="img-fluid rounded" alt="{{ boost.product.name }}"
                                         style="height: 80px; object-fit: cover;">
                                </div>
                                <div class="col-md-6">
                                    <h6 class="mb-1">{{ boost.product.name }}</h6>
                                    <small class="text-muted">{{ boost.product.shop.name }}</small>
                                    <div class="mt-2">
                                        <span class="badge bg-success">
                                            <i class="fas fa-rocket me-1"></i>Boosté
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-3 text-end">
                                    <div class="small">
                                        <div><strong>{{ boost.credits_spent }}</strong> crédits</div>
                                        <div class="text-muted">
                                            {% set remaining = boost.get_remaining_time() %}
                                            {% if remaining %}
                                            {{ remaining.days }}j {{ remaining.seconds // 3600 }}h restant
                                            {% else %}
                                            Expiré
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <div class="text-center">
                <a href="{{ url_for('shop.manage_boosts') }}" class="btn btn-outline-primary">
                    <i class="fas fa-list me-2"></i>Voir tous mes boosts
                </a>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Recent Transactions -->
    {% if recent_transactions %}
    <div class="row">
        <div class="col-12">
            <h3 class="mb-4">
                <i class="fas fa-history me-2"></i>Historique des Transactions
            </h3>
            
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Description</th>
                                    <th class="text-end">Crédits</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions %}
                                <tr>
                                    <td>{{ transaction.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                                    <td>
                                        {% if transaction.transaction_type.value == 'purchase' %}
                                        <span class="badge bg-success">Achat</span>
                                        {% elif transaction.transaction_type.value == 'boost' %}
                                        <span class="badge bg-primary">Boost</span>
                                        {% elif transaction.transaction_type.value == 'refund' %}
                                        <span class="badge bg-warning">Remboursement</span>
                                        {% else %}
                                        <span class="badge bg-secondary">Ajustement</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ transaction.description }}</td>
                                    <td class="text-end">
                                        <span class="{% if transaction.amount > 0 %}text-success{% else %}text-danger{% endif %}">
                                            {% if transaction.amount > 0 %}+{% endif %}{{ transaction.amount }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
