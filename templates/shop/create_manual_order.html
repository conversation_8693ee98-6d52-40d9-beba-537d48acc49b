{% extends "base.html" %}

{% block title %}Créer une Commande Manuelle - {{ shop.name }} - Afroly.org{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('shop.vendor_dashboard') }}">Tableau de Bord</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('shop.manage_orders', shop_id=shop.id) }}">Commandes - {{ shop.name }}</a></li>
            <li class="breadcrumb-item active">Créer une Commande Manuelle</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-plus-circle me-2"></i>Créer une Commande Manuelle
                    </h4>
                    <small class="text-muted">Boutique: {{ shop.name }}</small>
                </div>
                <div class="card-body">
                    <form method="POST" id="manualOrderForm">
                        <!-- Customer Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-user me-2"></i>Informations Client
                                </h5>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="customer_name" class="form-label">Nom Complet *</label>
                                <input type="text" class="form-control" id="customer_name" name="customer_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="customer_email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="customer_email" name="customer_email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="customer_phone" class="form-label">Téléphone</label>
                                <input type="tel" class="form-control" id="customer_phone" name="customer_phone">
                            </div>
                        </div>

                        <!-- Shipping Address -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-map-marker-alt me-2"></i>Adresse de Livraison
                                </h5>
                            </div>
                            <div class="col-12 mb-3">
                                <label for="address" class="form-label">Adresse</label>
                                <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="city" class="form-label">Ville</label>
                                <input type="text" class="form-control" id="city" name="city">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="country" class="form-label">Pays</label>
                                <input type="text" class="form-control" id="country" name="country" value="{{ shop.country }}">
                            </div>
                        </div>

                        <!-- Products Selection -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-shopping-cart me-2"></i>Produits
                                </h5>
                            </div>
                            <div class="col-12">
                                <div id="products-container">
                                    <div class="product-row mb-3">
                                        <div class="row align-items-end">
                                            <div class="col-md-6">
                                                <label class="form-label">Produit</label>
                                                <select class="form-select product-select" name="product_ids" onchange="updateProductInfo(this)">
                                                    <option value="">Sélectionner un produit...</option>
                                                    {% for product in products %}
                                                    <option value="{{ product.id }}" 
                                                            data-price="{{ product.get_display_price() }}" 
                                                            data-stock="{{ product.stock_quantity }}"
                                                            data-currency="{{ product.shop.get_currency_symbol() }}">
                                                        {{ product.name }} - {{ "%.0f"|format(product.get_display_price()) }} {{ product.shop.get_currency_symbol() }}
                                                        (Stock: {{ product.stock_quantity }})
                                                    </option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">Quantité</label>
                                                <input type="number" class="form-control quantity-input" name="quantities" 
                                                       value="1" min="1" onchange="calculateTotal()">
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">Total</label>
                                                <div class="form-control-plaintext product-total">0 {{ shop.get_currency_symbol() }}</div>
                                            </div>
                                            <div class="col-md-1">
                                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeProductRow(this)" style="display: none;">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <button type="button" class="btn btn-outline-primary btn-sm mb-3" onclick="addProductRow()">
                                    <i class="fas fa-plus me-1"></i>Ajouter un Produit
                                </button>
                                
                                <!-- Order Total -->
                                <div class="row">
                                    <div class="col-md-6 ms-auto">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <strong>Total de la Commande:</strong>
                                                    <strong id="order-total">0 {{ shop.get_currency_symbol() }}</strong>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Method -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-credit-card me-2"></i>Méthode de Paiement
                                </h5>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="payment_method" class="form-label">Méthode de Paiement</label>
                                <select class="form-select" id="payment_method" name="payment_method">
                                    <option value="">Sélectionner...</option>
                                    {% if payment_methods %}
                                        {% for method in payment_methods %}
                                        {% set method_config = None %}
                                        {% for category, methods in config.PAYMENT_METHODS.items() %}
                                            {% if method.type in methods and not method_config %}
                                                {% set method_config = methods[method.type] %}
                                            {% endif %}
                                        {% endfor %}
                                        {% if method_config %}
                                        <option value="{{ method.type }}">{{ method_config.name }}</option>
                                        {% endif %}
                                        {% endfor %}
                                    {% endif %}
                                    <option value="cash">Espèces</option>
                                    <option value="bank_transfer">Virement Bancaire</option>
                                    <option value="other">Autre</option>
                                </select>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-sticky-note me-2"></i>Notes
                                </h5>
                            </div>
                            <div class="col-12 mb-3">
                                <label for="customer_notes" class="form-label">Notes du Client</label>
                                <textarea class="form-control" id="customer_notes" name="customer_notes" rows="3" 
                                          placeholder="Instructions spéciales, préférences de livraison, etc."></textarea>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Créer la Commande
                                    </button>
                                    <a href="{{ url_for('shop.manage_orders', shop_id=shop.id) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>Annuler
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let productRowCount = 1;

function addProductRow() {
    productRowCount++;
    const container = document.getElementById('products-container');
    const newRow = document.createElement('div');
    newRow.className = 'product-row mb-3';
    newRow.innerHTML = `
        <div class="row align-items-end">
            <div class="col-md-6">
                <label class="form-label">Produit</label>
                <select class="form-select product-select" name="product_ids" onchange="updateProductInfo(this)">
                    <option value="">Sélectionner un produit...</option>
                    {% for product in products %}
                    <option value="{{ product.id }}" 
                            data-price="{{ product.get_display_price() }}" 
                            data-stock="{{ product.stock_quantity }}"
                            data-currency="{{ product.shop.get_currency_symbol() }}">
                        {{ product.name }} - {{ "%.0f"|format(product.get_display_price()) }} {{ product.shop.get_currency_symbol() }}
                        (Stock: {{ product.stock_quantity }})
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Quantité</label>
                <input type="number" class="form-control quantity-input" name="quantities" 
                       value="1" min="1" onchange="calculateTotal()">
            </div>
            <div class="col-md-2">
                <label class="form-label">Total</label>
                <div class="form-control-plaintext product-total">0 {{ shop.get_currency_symbol() }}</div>
            </div>
            <div class="col-md-1">
                <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeProductRow(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    container.appendChild(newRow);
    
    // Show remove button for all rows if more than one
    updateRemoveButtons();
}

function removeProductRow(button) {
    button.closest('.product-row').remove();
    productRowCount--;
    updateRemoveButtons();
    calculateTotal();
}

function updateRemoveButtons() {
    const rows = document.querySelectorAll('.product-row');
    rows.forEach((row, index) => {
        const removeBtn = row.querySelector('.btn-outline-danger');
        if (rows.length > 1) {
            removeBtn.style.display = 'block';
        } else {
            removeBtn.style.display = 'none';
        }
    });
}

function updateProductInfo(select) {
    const row = select.closest('.product-row');
    const quantityInput = row.querySelector('.quantity-input');
    const option = select.selectedOptions[0];
    
    if (option && option.value) {
        const stock = parseInt(option.dataset.stock);
        quantityInput.max = stock;
        
        if (parseInt(quantityInput.value) > stock) {
            quantityInput.value = stock;
        }
    }
    
    calculateTotal();
}

function calculateTotal() {
    let orderTotal = 0;
    
    document.querySelectorAll('.product-row').forEach(row => {
        const select = row.querySelector('.product-select');
        const quantityInput = row.querySelector('.quantity-input');
        const totalDiv = row.querySelector('.product-total');
        
        if (select.value && quantityInput.value) {
            const option = select.selectedOptions[0];
            const price = parseFloat(option.dataset.price);
            const quantity = parseInt(quantityInput.value);
            const currency = option.dataset.currency;
            
            const productTotal = price * quantity;
            totalDiv.textContent = `${productTotal.toFixed(0)} ${currency}`;
            orderTotal += productTotal;
        } else {
            totalDiv.textContent = `0 {{ shop.get_currency_symbol() }}`;
        }
    });
    
    document.getElementById('order-total').textContent = `${orderTotal.toFixed(0)} {{ shop.get_currency_symbol() }}`;
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    calculateTotal();
});
</script>
{% endblock %}
