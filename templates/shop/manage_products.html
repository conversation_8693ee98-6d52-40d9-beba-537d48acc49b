{% extends "base.html" %}

{% block title %}Gérer les Produits - {{ shop.name }} - AfroMall{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-6 fw-bold">
                <i class="fas fa-box me-3"></i>Gérer les Produits
            </h1>
            <p class="lead text-muted">{{ shop.name }}</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('product.create', shop_id=shop.id) }}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>Ajouter un Produit
            </a>
        </div>
    </div>
    
    <!-- Shop Info Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            {% if shop.logo %}
                            <img src="/static/uploads/shops/{{ shop.logo }}" alt="{{ shop.name }}" 
                                 class="rounded-circle" style="width: 60px; height: 60px; object-fit: cover;">
                            {% else %}
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                 style="width: 60px; height: 60px;">
                                <i class="fas fa-store text-white"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <h5 class="mb-1">{{ shop.name }}</h5>
                            <p class="text-muted mb-0">{{ shop.description[:100] }}{% if shop.description|length > 100 %}...{% endif %}</p>
                        </div>
                        <div class="col-md-4">
                            <div class="row text-center">
                                <div class="col-4">
                                    <h6 class="text-success mb-0">{{ products.total }}</h6>
                                    <small class="text-muted">Total</small>
                                </div>
                                <div class="col-4">
                                    <h6 class="text-primary mb-0">{{ active_count }}</h6>
                                    <small class="text-muted">Actifs</small>
                                </div>
                                <div class="col-4">
                                    <h6 class="text-warning mb-0">{{ pending_count }}</h6>
                                    <small class="text-muted">En Attente</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filters and Search -->
    <div class="row mb-4">
        <div class="col-md-8">
            <form method="GET" class="d-flex gap-3">
                <div class="flex-grow-1">
                    <input type="text" class="form-control" name="search" placeholder="Rechercher un produit..." 
                           value="{{ request.args.get('search', '') }}">
                </div>
                <select name="status" class="form-select" style="width: auto;">
                    <option value="">Tous les statuts</option>
                    <option value="active" {% if request.args.get('status') == 'active' %}selected{% endif %}>Actif</option>
                    <option value="pending" {% if request.args.get('status') == 'pending' %}selected{% endif %}>En Attente</option>
                    <option value="rejected" {% if request.args.get('status') == 'rejected' %}selected{% endif %}>Rejeté</option>
                    <option value="inactive" {% if request.args.get('status') == 'inactive' %}selected{% endif %}>Inactif</option>
                </select>
                <button type="submit" class="btn btn-outline-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
        <div class="col-md-4">
            <form method="GET" class="d-flex align-items-center">
                <input type="hidden" name="search" value="{{ request.args.get('search', '') }}">
                <input type="hidden" name="status" value="{{ request.args.get('status', '') }}">
                <label for="sort" class="form-label me-2 mb-0">Trier :</label>
                <select name="sort" id="sort" class="form-select" onchange="this.form.submit()">
                    <option value="newest" {% if sort_by == 'newest' %}selected{% endif %}>Plus récents</option>
                    <option value="oldest" {% if sort_by == 'oldest' %}selected{% endif %}>Plus anciens</option>
                    <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Nom A-Z</option>
                    <option value="price_low" {% if sort_by == 'price_low' %}selected{% endif %}>Prix croissant</option>
                    <option value="price_high" {% if sort_by == 'price_high' %}selected{% endif %}>Prix décroissant</option>
                </select>
            </form>
        </div>
    </div>
    
    <!-- Products List -->
    {% if products.items %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Produits ({{ products.total }})</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Produit</th>
                                    <th>Prix</th>
                                    <th>Stock</th>
                                    <th>Statut</th>
                                    <th>Créé le</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in products.items %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{{ product.get_main_image() }}" alt="{{ product.name }}" 
                                                 class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                            <div>
                                                <h6 class="mb-1">{{ product.name }}</h6>
                                                <small class="text-muted">{{ product.category.name if product.category else 'Sans catégorie' }}</small>
                                                {% if product.featured %}
                                                <span class="badge bg-warning ms-2">
                                                    <i class="fas fa-star"></i> Vedette
                                                </span>
                                                {% endif %}
                                                {% if product.is_boosted() %}
                                                <span class="badge bg-success ms-2">
                                                    <i class="fas fa-rocket"></i> Boosté
                                                </span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if product.sale_price %}
                                        <span class="fw-bold text-primary">{{ "%.2f"|format(product.sale_price) }} €</span>
                                        <br><small class="text-muted text-decoration-line-through">{{ "%.2f"|format(product.price) }} €</small>
                                        {% else %}
                                        <span class="fw-bold">{{ "%.2f"|format(product.price) }} €</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if product.stock_quantity > 10 %}
                                        <span class="badge bg-success">{{ product.stock_quantity }}</span>
                                        {% elif product.stock_quantity > 0 %}
                                        <span class="badge bg-warning">{{ product.stock_quantity }}</span>
                                        {% else %}
                                        <span class="badge bg-danger">Rupture</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if product.status.value == 'active' %}
                                        <span class="badge bg-success">Actif</span>
                                        {% elif product.status.value == 'pending' %}
                                        <span class="badge bg-warning">En Attente</span>
                                        {% elif product.status.value == 'rejected' %}
                                        <span class="badge bg-danger">Rejeté</span>
                                        {% else %}
                                        <span class="badge bg-secondary">Inactif</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ product.created_at.strftime('%d/%m/%Y') }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            {% if product.status.value == 'active' %}
                                            <a href="{{ url_for('product.view', slug=product.slug) }}"
                                               class="btn btn-sm btn-outline-primary" target="_blank"
                                               data-bs-toggle="tooltip" title="Voir le produit">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% endif %}
                                            <a href="{{ url_for('product.edit', product_id=product.id) }}"
                                               class="btn btn-sm btn-outline-secondary"
                                               data-bs-toggle="tooltip" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if product.can_be_boosted() %}
                                            <a href="{{ url_for('shop.boost_product', product_id=product.id) }}"
                                               class="btn btn-sm btn-outline-warning"
                                               data-bs-toggle="tooltip" title="Booster ce produit">
                                                <i class="fas fa-rocket"></i>
                                            </a>
                                            {% endif %}
                                            <button class="btn btn-sm btn-outline-danger"
                                                    onclick="deleteProduct({{ product.id }})"
                                                    data-bs-toggle="tooltip" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Pagination -->
    {% if products.pages > 1 %}
    <nav aria-label="Navigation des produits" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if products.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('shop.manage_products', shop_id=shop.id, 
                    search=request.args.get('search', ''), status=request.args.get('status', ''), 
                    sort=sort_by, page=products.prev_num) }}">
                    <i class="fas fa-chevron-left me-1"></i>Précédent
                </a>
            </li>
            {% endif %}
            
            {% for page_num in products.iter_pages() %}
                {% if page_num %}
                    {% if page_num != products.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('shop.manage_products', shop_id=shop.id, 
                            search=request.args.get('search', ''), status=request.args.get('status', ''), 
                            sort=sort_by, page=page_num) }}">
                            {{ page_num }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if products.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('shop.manage_products', shop_id=shop.id, 
                    search=request.args.get('search', ''), status=request.args.get('status', ''), 
                    sort=sort_by, page=products.next_num) }}">
                    Suivant<i class="fas fa-chevron-right ms-1"></i>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
    
    {% else %}
    <!-- No Products -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-box-open fa-4x text-muted mb-4"></i>
                    <h4>Aucun produit trouvé</h4>
                    {% if request.args.get('search') or request.args.get('status') %}
                    <p class="text-muted mb-4">Aucun produit ne correspond à vos critères de recherche.</p>
                    <a href="{{ url_for('shop.manage_products', shop_id=shop.id) }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-times me-2"></i>Effacer les Filtres
                    </a>
                    {% else %}
                    <p class="text-muted mb-4">Vous n'avez pas encore ajouté de produits à cette boutique.</p>
                    {% endif %}
                    <a href="{{ url_for('product.create', shop_id=shop.id) }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-plus me-2"></i>Ajouter Mon Premier Produit
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <h6 class="card-title">Actions Rapides</h6>
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="{{ url_for('shop.vendor_dashboard') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Retour au Tableau de Bord
                        </a>
                        <a href="{{ url_for('shop.edit', shop_id=shop.id) }}" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i>Modifier la Boutique
                        </a>
                        <a href="{{ url_for('shop.manage_orders', shop_id=shop.id) }}" class="btn btn-outline-success">
                            <i class="fas fa-shopping-cart me-2"></i>Gérer les Commandes
                        </a>
                        <a href="{{ url_for('shop.credits_dashboard') }}" class="btn btn-outline-warning">
                            <i class="fas fa-coins me-2"></i>Mes Crédits ({{ current_user.credit_balance }})
                        </a>
                        {% if shop.status.value == 'active' %}
                        <a href="{{ url_for('shop.view', slug=shop.slug) }}" class="btn btn-outline-info" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>Voir la Boutique
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la Suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer ce produit ? Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Supprimer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let productToDelete = null;

function deleteProduct(productId) {
    productToDelete = productId;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

document.getElementById('confirmDelete').addEventListener('click', function() {
    if (productToDelete) {
        // Send delete request
        fetch(`/product/delete/${productToDelete}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur lors de la suppression du produit');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur lors de la suppression du produit');
        });
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
        modal.hide();
    }
});

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
