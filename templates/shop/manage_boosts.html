{% extends "base.html" %}

{% block title %}Mes <PERSON>s - Afroly.org{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-5 fw-bold">
                <i class="fas fa-rocket me-3 text-warning"></i>Mes Bo<PERSON>s
            </h1>
            <p class="lead text-muted">Gérez vos produits boostés</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('shop.credits_dashboard') }}" class="btn btn-warning">
                <i class="fas fa-coins me-2"></i>Mes Crédits ({{ current_user.credit_balance }})
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-md-6">
            <form method="GET" class="d-flex gap-3">
                <select name="status" class="form-select" onchange="this.form.submit()">
                    <option value="all" {% if status_filter == 'all' %}selected{% endif %}>Tous les boosts</option>
                    <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Actifs</option>
                    <option value="expired" {% if status_filter == 'expired' %}selected{% endif %}>Expirés</option>
                </select>
            </form>
        </div>
    </div>

    <!-- Boosts List -->
    {% if boosts.items %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Historique des Boosts ({{ boosts.total }})</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Produit</th>
                                    <th>Durée</th>
                                    <th>Crédits</th>
                                    <th>Statut</th>
                                    <th>Performance</th>
                                    <th>Créé le</th>
                                    <th>Expire le</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for boost in boosts.items %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{{ boost.product.get_main_image() }}" alt="{{ boost.product.name }}" 
                                                 class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                            <div>
                                                <h6 class="mb-1">{{ boost.product.name }}</h6>
                                                <small class="text-muted">{{ boost.product.shop.name }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-bold">{{ boost.duration_days }}</span> jours
                                    </td>
                                    <td>
                                        <span class="badge bg-warning text-dark">{{ boost.credits_spent }} crédits</span>
                                    </td>
                                    <td>
                                        {% if boost.is_currently_active() %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-rocket me-1"></i>Actif
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary">Expiré</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="small">
                                            <div><strong>{{ boost.impressions }}</strong> vues</div>
                                            <div><strong>{{ boost.clicks }}</strong> clics</div>
                                            {% if boost.impressions > 0 %}
                                            <div class="text-muted">{{ "%.1f"|format(boost.get_ctr()) }}% CTR</div>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <small>{{ boost.created_at.strftime('%d/%m/%Y %H:%M') }}</small>
                                    </td>
                                    <td>
                                        <small>{{ boost.expires_at.strftime('%d/%m/%Y %H:%M') }}</small>
                                        {% if boost.is_currently_active() %}
                                        {% set remaining = boost.get_remaining_time() %}
                                        <div class="text-success small">
                                            {{ remaining.days }}j {{ remaining.seconds // 3600 }}h restant
                                        </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if boosts.pages > 1 %}
    <nav aria-label="Navigation des boosts" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if boosts.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('shop.manage_boosts', page=boosts.prev_num, status=status_filter) }}">
                    Précédent
                </a>
            </li>
            {% endif %}

            {% for page_num in boosts.iter_pages() %}
                {% if page_num %}
                    {% if page_num != boosts.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('shop.manage_boosts', page=page_num, status=status_filter) }}">
                            {{ page_num }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">…</span>
                </li>
                {% endif %}
            {% endfor %}

            {% if boosts.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('shop.manage_boosts', page=boosts.next_num, status=status_filter) }}">
                    Suivant
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    {% else %}
    <!-- No Boosts -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-rocket fa-4x text-muted mb-4"></i>
                    <h4>Aucun boost trouvé</h4>
                    {% if status_filter != 'all' %}
                    <p class="text-muted mb-4">Aucun boost ne correspond à vos critères.</p>
                    <a href="{{ url_for('shop.manage_boosts') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-times me-2"></i>Voir Tous les Boosts
                    </a>
                    {% else %}
                    <p class="text-muted mb-4">Vous n'avez pas encore boosté de produits.</p>
                    {% endif %}
                    <a href="{{ url_for('shop.credits_dashboard') }}" class="btn btn-warning">
                        <i class="fas fa-coins me-2"></i>Acheter des Crédits
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Info Box -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6 class="alert-heading">
                    <i class="fas fa-lightbulb me-2"></i>Conseils pour optimiser vos boosts
                </h6>
                <ul class="mb-0">
                    <li><strong>Timing :</strong> Boostez vos produits pendant les périodes de forte activité</li>
                    <li><strong>Qualité :</strong> Assurez-vous que vos produits ont de bonnes photos et descriptions</li>
                    <li><strong>Prix :</strong> Des prix compétitifs augmentent les chances de conversion</li>
                    <li><strong>Stock :</strong> Vérifiez que vos produits sont en stock avant de les booster</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <h6 class="card-title">Actions Rapides</h6>
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="{{ url_for('shop.vendor_dashboard') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Tableau de Bord
                        </a>
                        <a href="{{ url_for('shop.credits_dashboard') }}" class="btn btn-outline-warning">
                            <i class="fas fa-coins me-2"></i>Mes Crédits
                        </a>
                        {% if current_user.shops %}
                        <a href="{{ url_for('shop.manage_products', shop_id=current_user.shops[0].id) }}" class="btn btn-outline-primary">
                            <i class="fas fa-box me-2"></i>Mes Produits
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
