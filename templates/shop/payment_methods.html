{% extends "base.html" %}

{% block title %}Méthodes de Paiement - AfroMall{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="display-6 fw-bold">
                        <i class="fas fa-credit-card me-3 text-primary"></i>Méthodes de Paiement
                    </h1>
                    <p class="lead text-muted">Gérez les méthodes de paiement pour toutes vos boutiques</p>
                </div>
                <div>
                    <a href="{{ url_for('shop.vendor_dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour au Tableau de Bord
                    </a>
                </div>
            </div>

            {% if shops %}
            <!-- Payment Methods Overview -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <i class="fas fa-mobile-alt fa-2x text-success mb-2"></i>
                            <h5>Mobile Money</h5>
                            <p class="text-muted small">MTN, Orange, Airtel, Wave</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-warning">
                        <div class="card-body text-center">
                            <i class="fas fa-globe fa-2x text-warning mb-2"></i>
                            <h5>International</h5>
                            <p class="text-muted small">PayPal, Stripe (Premium+)</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-info">
                        <div class="card-body text-center">
                            <i class="fas fa-university fa-2x text-info mb-2"></i>
                            <h5>Virement Bancaire</h5>
                            <p class="text-muted small">Comptes bancaires locaux</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Shops Payment Methods -->
            {% for shop in shops %}
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0">
                                <i class="fas fa-store me-2"></i>{{ shop.name }}
                                <span class="badge bg-secondary ms-2">{{ shop.country }}</span>
                            </h5>
                            <small class="text-muted">{{ shop.description[:100] }}{% if shop.description|length > 100 %}...{% endif %}</small>
                        </div>
                        <div>
                            <a href="{{ url_for('shop.edit_payment_methods', shop_id=shop.id) }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-credit-card me-1"></i>Configurer Paiements
                            </a>
                            <a href="{{ url_for('shop.edit', shop_id=shop.id) }}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-edit me-1"></i>Modifier Boutique
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% set payment_methods = shop.get_payment_methods() %}
                    {% if payment_methods %}
                    <div class="row">
                        {% for method in payment_methods %}
                        {% set method_config = None %}
                        {% for category, methods in config.PAYMENT_METHODS.items() %}
                            {% if method.type in methods and not method_config %}
                                {% set method_config = methods[method.type] %}
                            {% endif %}
                        {% endfor %}

                        {% if method_config %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-light h-100">
                                <div class="card-body p-3">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="{{ method_config.icon }} me-2" style="color: {{ method_config.color }}"></i>
                                        <strong>{{ method_config.name }}</strong>
                                    </div>

                                    {% if method.details %}
                                    <div class="payment-details">
                                        {% for field, value in method.details.items() %}
                                        {% if value %}
                                        <div class="small text-muted mb-1">
                                            {% if field == 'phone_number' %}
                                                <i class="fas fa-phone me-1"></i>{{ value }}
                                            {% elif field == 'account_name' %}
                                                <i class="fas fa-user me-1"></i>{{ value }}
                                            {% elif field == 'momo_name' %}
                                                <i class="fas fa-mobile-alt me-1"></i>{{ value }}
                                            {% elif field == 'bank_name' %}
                                                <i class="fas fa-university me-1"></i>{{ value }}
                                            {% elif field == 'account_number' %}
                                                <i class="fas fa-hashtag me-1"></i>{{ value }}
                                            {% elif field == 'paypal_email' %}
                                                <i class="fab fa-paypal me-1"></i>{{ value }}
                                            {% elif field == 'method_name' %}
                                                <i class="fas fa-tag me-1"></i>{{ value }}
                                            {% else %}
                                                {{ value }}
                                            {% endif %}
                                        </div>
                                        {% endif %}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune méthode de paiement configurée</h5>
                        <p class="text-muted">Configurez vos méthodes de paiement pour permettre aux clients d'acheter vos produits.</p>
                        <a href="{{ url_for('shop.edit_payment_methods', shop_id=shop.id) }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Ajouter des Méthodes de Paiement
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}

            <!-- Help Section -->
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Aide et Conseils
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-mobile-alt me-2 text-success"></i>Mobile Money</h6>
                            <ul class="small">
                                <li>Disponible pour tous les niveaux d'abonnement</li>
                                <li>Méthode de paiement la plus populaire en Afrique</li>
                                <li>Paiements instantanés et sécurisés</li>
                                <li>Support MTN, Orange, Airtel, Moov, Wave</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-globe me-2 text-warning"></i>Paiements Internationaux</h6>
                            <ul class="small">
                                <li>Disponible pour les comptes Premium et Gold</li>
                                <li>PayPal et Stripe pour cartes de crédit</li>
                                <li>Idéal pour les clients internationaux</li>
                                <li>Frais de transaction plus élevés</li>
                            </ul>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Important :</strong> Assurez-vous que vos informations de paiement sont correctes.
                                Les clients utiliseront ces informations pour vous envoyer les paiements.
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {% else %}
            <!-- No Shops -->
            <div class="text-center py-5">
                <i class="fas fa-store fa-4x text-muted mb-4"></i>
                <h3 class="text-muted">Aucune boutique trouvée</h3>
                <p class="text-muted mb-4">Vous devez d'abord créer une boutique pour configurer des méthodes de paiement.</p>
                <a href="{{ url_for('shop.create') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>Créer Ma Première Boutique
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
.card {
    transition: all 0.3s ease;
    border-radius: 15px;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.payment-details {
    font-size: 0.9rem;
}

.badge {
    font-size: 0.75rem;
}
</style>
{% endblock %}
