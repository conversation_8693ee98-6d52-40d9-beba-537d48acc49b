{% extends "base.html" %}

{% block title %}Booster {{ product.name }} - Afroly.org{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-5 fw-bold">
                <i class="fas fa-rocket me-3 text-primary"></i>Booster un Produit
            </h1>
            <p class="lead text-muted">Augmentez la visibilité de votre produit</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <h3 class="mb-0">{{ current_user.credit_balance }}</h3>
                    <p class="mb-0">Crédits disponibles</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Product Info -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Produit à booster</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <img src="{{ product.get_main_image() }}" 
                             class="img-fluid rounded" alt="{{ product.name }}"
                             style="height: 200px; object-fit: cover;">
                    </div>
                    <h6 class="card-title">{{ product.name }}</h6>
                    <p class="card-text text-muted">{{ product.shop.name }}</p>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="fw-bold text-primary">{{ "%.0f"|format(product.get_display_price()) }} {{ product.shop.get_currency_symbol() }}</div>
                            <small class="text-muted">Prix</small>
                        </div>
                        <div class="col-6">
                            <div class="fw-bold">{{ product.stock_quantity }}</div>
                            <small class="text-muted">En stock</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Benefits -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-star me-2 text-warning"></i>Avantages du Boost
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Apparaît en premier dans les recherches
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Affiché dans les suggestions
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Badge "Produit Boosté"
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            Plus de visibilité = plus de ventes
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Boost Options -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Choisissez la durée du boost</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            {% for days, credits in credit_costs.items() %}
                            <div class="col-md-4 mb-3">
                                <div class="card h-100 boost-option {% if days == 7 %}border-primary{% endif %}" 
                                     onclick="selectBoostOption({{ days }}, {{ credits }})">
                                    <div class="card-body text-center">
                                        {% if days == 14 %}
                                        <div class="badge bg-warning mb-2">Populaire</div>
                                        {% elif days == 30 %}
                                        <div class="badge bg-success mb-2">Meilleure valeur</div>
                                        {% endif %}
                                        
                                        <h4 class="card-title">
                                            {% if days == 7 %}1 Semaine{% elif days == 14 %}2 Semaines{% else %}1 Mois{% endif %}
                                        </h4>
                                        
                                        <div class="display-6 fw-bold text-primary">{{ credits }}</div>
                                        <div class="text-muted mb-3">crédits</div>
                                        
                                        <div class="small text-muted">
                                            {{ "%.2f"|format(credits / days) }} crédits/jour
                                        </div>
                                        
                                        {% if days == 14 %}
                                        <div class="small text-success mt-2">
                                            <i class="fas fa-tag me-1"></i>10% d'économie
                                        </div>
                                        {% elif days == 30 %}
                                        <div class="small text-success mt-2">
                                            <i class="fas fa-tag me-1"></i>20% d'économie
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <input type="hidden" name="duration_days" id="duration_days" value="7">

                        <!-- Selected Option Display -->
                        <div class="alert alert-primary mt-4" id="selected-option">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h6 class="mb-1">Option sélectionnée:</h6>
                                    <span id="selected-duration">1 Semaine</span> pour <strong id="selected-credits">50</strong> crédits
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="small text-muted">Crédits après boost:</div>
                                    <div class="fw-bold" id="remaining-credits">{{ current_user.credit_balance - 50 }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="{{ url_for('shop.manage_products', shop_id=product.shop_id) }}" 
                               class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Retour
                            </a>
                            
                            {% if current_user.credit_balance >= 50 %}
                            <button type="submit" class="btn btn-primary btn-lg" id="boost-button">
                                <i class="fas fa-rocket me-2"></i>Booster le Produit
                            </button>
                            {% else %}
                            <div class="text-center">
                                <p class="text-danger mb-2">Crédits insuffisants</p>
                                <a href="{{ url_for('shop.credits_dashboard') }}" class="btn btn-warning">
                                    <i class="fas fa-coins me-2"></i>Acheter des Crédits
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </form>
                </div>
            </div>

            <!-- Info Box -->
            <div class="alert alert-info mt-4">
                <h6 class="alert-heading">
                    <i class="fas fa-info-circle me-2"></i>Comment ça marche ?
                </h6>
                <p class="mb-2">
                    Quand vous boostez un produit, il apparaît en priorité dans :
                </p>
                <ul class="mb-0">
                    <li>Les résultats de recherche de sa catégorie</li>
                    <li>Les suggestions "Produits similaires" sur d'autres pages produits</li>
                    <li>Les listes de produits avec un badge spécial "Boosté"</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
.boost-option {
    cursor: pointer;
    transition: all 0.3s ease;
}

.boost-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.boost-option.selected {
    border-color: #0d6efd !important;
    background-color: #f8f9ff;
}
</style>

<script>
const creditCosts = {{ credit_costs|tojson }};
const userCredits = {{ current_user.credit_balance }};

function selectBoostOption(days, credits) {
    // Remove selected class from all options
    document.querySelectorAll('.boost-option').forEach(option => {
        option.classList.remove('selected');
    });
    
    // Add selected class to clicked option
    event.currentTarget.classList.add('selected');
    
    // Update form values
    document.getElementById('duration_days').value = days;
    
    // Update display
    let durationText = days === 7 ? '1 Semaine' : days === 14 ? '2 Semaines' : '1 Mois';
    document.getElementById('selected-duration').textContent = durationText;
    document.getElementById('selected-credits').textContent = credits;
    document.getElementById('remaining-credits').textContent = userCredits - credits;
    
    // Update button state
    const boostButton = document.getElementById('boost-button');
    if (boostButton) {
        if (userCredits >= credits) {
            boostButton.disabled = false;
            boostButton.innerHTML = '<i class="fas fa-rocket me-2"></i>Booster le Produit';
        } else {
            boostButton.disabled = true;
            boostButton.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Crédits insuffisants';
        }
    }
}

// Initialize with first option selected
document.addEventListener('DOMContentLoaded', function() {
    const firstOption = document.querySelector('.boost-option');
    if (firstOption) {
        firstOption.click();
    }
});
</script>
{% endblock %}
