{% extends "base.html" %}

{% block title %}Modifier {{ shop.name }} - AfroMall{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow border-0">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">
                                <i class="fas fa-edit me-2"></i>Modifier la Boutique
                            </h3>
                            <p class="mb-0 mt-2">{{ shop.name }}</p>
                        </div>
                        <div>
                            {% if shop.status.value == 'active' %}
                            <span class="badge bg-success fs-6">Actif</span>
                            {% elif shop.status.value == 'pending' %}
                            <span class="badge bg-warning fs-6">En Attente</span>
                            {% elif shop.status.value == 'rejected' %}
                            <span class="badge bg-danger fs-6">Rejeté</span>
                            {% else %}
                            <span class="badge bg-secondary fs-6">Inactif</span>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="card-body p-4">
                    <form method="POST" enctype="multipart/form-data">
                        <!-- Current Images Display -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-images me-2"></i>Images Actuelles
                                </h5>
                            </div>

                            <div class="col-md-6">
                                <div class="text-center">
                                    <label class="form-label">Logo Actuel</label>
                                    <div class="current-logo mb-2">
                                        {% if shop.logo %}
                                        <img src="/static/uploads/shops/{{ shop.logo }}" alt="Logo"
                                             class="rounded-circle border" style="width: 100px; height: 100px; object-fit: cover;">
                                        {% else %}
                                        <div class="bg-light rounded-circle border d-flex align-items-center justify-content-center mx-auto"
                                             style="width: 100px; height: 100px;">
                                            <i class="fas fa-store fa-2x text-muted"></i>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="text-center">
                                    <label class="form-label">Bannière Actuelle</label>
                                    <div class="current-banner mb-2">
                                        {% if shop.banner %}
                                        <img src="/static/uploads/shops/{{ shop.banner }}" alt="Bannière"
                                             class="img-fluid rounded border" style="height: 100px; object-fit: cover; width: 100%;">
                                        {% else %}
                                        <div class="bg-light rounded border d-flex align-items-center justify-content-center"
                                             style="height: 100px;">
                                            <i class="fas fa-image fa-2x text-muted"></i>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle me-2"></i>Informations de Base
                                </h5>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Nom de la Boutique *</label>
                                    <input type="text" class="form-control" id="name" name="name" required
                                           value="{{ request.form.get('name', shop.name) }}">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="country" class="form-label">Pays *</label>
                                    <select class="form-select" id="country" name="country" required>
                                        <option value="">Choisir un pays africain</option>
                                        {% for country_option in countries %}
                                        <option value="{{ country_option }}" {% if request.form.get('country', shop.country) == country_option %}selected{% endif %}>
                                            {{ country_option }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="currency" class="form-label">Devise de Paiement *</label>
                                    <select class="form-select" id="currency" name="currency" required>
                                        <option value="">Choisir une devise</option>
                                        <optgroup label="Afrique de l'Ouest et Centrale (FCFA)">
                                            <option value="FCFA" {% if request.form.get('currency', shop.currency) == 'FCFA' %}selected{% endif %}>
                                                FCFA - Franc CFA (Afrique de l'Ouest et Centrale)
                                            </option>
                                            <option value="XOF" {% if request.form.get('currency', shop.currency) == 'XOF' %}selected{% endif %}>
                                                XOF - Franc CFA (Afrique de l'Ouest)
                                            </option>
                                            <option value="XAF" {% if request.form.get('currency', shop.currency) == 'XAF' %}selected{% endif %}>
                                                XAF - Franc CFA (Afrique Centrale)
                                            </option>
                                        </optgroup>
                                        <optgroup label="Autres Devises Africaines">
                                            <option value="NGN" {% if request.form.get('currency', shop.currency) == 'NGN' %}selected{% endif %}>
                                                NGN - Naira Nigérian (₦)
                                            </option>
                                            <option value="GHS" {% if request.form.get('currency', shop.currency) == 'GHS' %}selected{% endif %}>
                                                GHS - Cedi Ghanéen (GH₵)
                                            </option>
                                            <option value="KES" {% if request.form.get('currency', shop.currency) == 'KES' %}selected{% endif %}>
                                                KES - Shilling Kenyan (KSh)
                                            </option>
                                            <option value="UGX" {% if request.form.get('currency', shop.currency) == 'UGX' %}selected{% endif %}>
                                                UGX - Shilling Ougandais (USh)
                                            </option>
                                            <option value="TZS" {% if request.form.get('currency', shop.currency) == 'TZS' %}selected{% endif %}>
                                                TZS - Shilling Tanzanien (TSh)
                                            </option>
                                            <option value="RWF" {% if request.form.get('currency', shop.currency) == 'RWF' %}selected{% endif %}>
                                                RWF - Franc Rwandais
                                            </option>
                                            <option value="ETB" {% if request.form.get('currency', shop.currency) == 'ETB' %}selected{% endif %}>
                                                ETB - Birr Éthiopien (Br)
                                            </option>
                                            <option value="ZAR" {% if request.form.get('currency', shop.currency) == 'ZAR' %}selected{% endif %}>
                                                ZAR - Rand Sud-Africain (R)
                                            </option>
                                            <option value="MAD" {% if request.form.get('currency', shop.currency) == 'MAD' %}selected{% endif %}>
                                                MAD - Dirham Marocain
                                            </option>
                                            <option value="TND" {% if request.form.get('currency', shop.currency) == 'TND' %}selected{% endif %}>
                                                TND - Dinar Tunisien
                                            </option>
                                            <option value="EGP" {% if request.form.get('currency', shop.currency) == 'EGP' %}selected{% endif %}>
                                                EGP - Livre Égyptienne
                                            </option>
                                            <option value="DZD" {% if request.form.get('currency', shop.currency) == 'DZD' %}selected{% endif %}>
                                                DZD - Dinar Algérien (DA)
                                            </option>
                                        </optgroup>
                                        <optgroup label="Devises Internationales">
                                            <option value="EUR" {% if request.form.get('currency', shop.currency) == 'EUR' %}selected{% endif %}>
                                                EUR - Euro (€)
                                            </option>
                                            <option value="USD" {% if request.form.get('currency', shop.currency) == 'USD' %}selected{% endif %}>
                                                USD - Dollar Américain ($)
                                            </option>
                                            <option value="GBP" {% if request.form.get('currency', shop.currency) == 'GBP' %}selected{% endif %}>
                                                GBP - Livre Sterling (£)
                                            </option>
                                        </optgroup>
                                    </select>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        FCFA est recommandé pour l'Afrique de l'Ouest et Centrale
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_email" class="form-label">Email de Contact *</label>
                                    <input type="email" class="form-control" id="contact_email" name="contact_email" required
                                           value="{{ request.form.get('contact_email', shop.contact_email) }}">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_phone" class="form-label">Téléphone</label>
                                    <input type="tel" class="form-control" id="contact_phone" name="contact_phone"
                                           value="{{ request.form.get('contact_phone', shop.contact_phone or '') }}">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="address" class="form-label">Adresse *</label>
                                    <input type="text" class="form-control" id="address" name="address" required
                                           value="{{ request.form.get('address', shop.address) }}">
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description de la Boutique *</label>
                                    <textarea class="form-control" id="description" name="description" rows="4" required>{{ request.form.get('description', shop.description) }}</textarea>
                                    <div class="form-text">Minimum 50 caractères</div>
                                </div>
                            </div>
                        </div>

                        <!-- Update Images -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-upload me-2"></i>Mettre à Jour les Images
                                </h5>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="logo" class="form-label">Nouveau Logo</label>
                                    <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                                    <div class="form-text">Laissez vide pour conserver le logo actuel</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="banner" class="form-label">Nouvelle Bannière</label>
                                    <input type="file" class="form-control" id="banner" name="banner" accept="image/*">
                                    <div class="form-text">Laissez vide pour conserver la bannière actuelle</div>
                                </div>
                            </div>
                        </div>

                        <!-- Shop Stats -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-chart-bar me-2"></i>Statistiques de la Boutique
                                </h5>
                            </div>

                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h4 class="text-primary">{{ shop.get_active_products_count() }}</h4>
                                        <small class="text-muted">Produits Actifs</small>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h4 class="text-warning">{{ shop.products|selectattr('status.value', 'equalto', 'pending')|list|length }}</h4>
                                        <small class="text-muted">En Attente</small>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h4 class="text-success">{{ shop.orders|length }}</h4>
                                        <small class="text-muted">Commandes</small>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h4 class="text-info">{{ shop.reviews|length }}</h4>
                                        <small class="text-muted">Avis</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-bolt me-2"></i>Actions Rapides
                                </h5>
                            </div>

                            <div class="col-md-3">
                                <a href="{{ url_for('shop.manage_products', shop_id=shop.id) }}" class="btn btn-outline-primary w-100 mb-2">
                                    <i class="fas fa-box me-2"></i>Gérer Produits
                                </a>
                            </div>

                            <div class="col-md-3">
                                <a href="{{ url_for('shop.manage_orders', shop_id=shop.id) }}" class="btn btn-outline-success w-100 mb-2">
                                    <i class="fas fa-shopping-cart me-2"></i>Gérer Commandes
                                </a>
                            </div>

                            <div class="col-md-3">
                                <a href="{{ url_for('product.create', shop_id=shop.id) }}" class="btn btn-outline-info w-100 mb-2">
                                    <i class="fas fa-plus me-2"></i>Ajouter Produit
                                </a>
                            </div>

                            <div class="col-md-3">
                                {% if shop.status.value == 'active' %}
                                <a href="{{ url_for('shop.view', slug=shop.slug) }}" class="btn btn-outline-secondary w-100 mb-2" target="_blank">
                                    <i class="fas fa-external-link-alt me-2"></i>Voir Boutique
                                </a>
                                {% else %}
                                <button class="btn btn-outline-secondary w-100 mb-2" disabled>
                                    <i class="fas fa-eye-slash me-2"></i>Non Publique
                                </button>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Social Media Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-success border-bottom pb-2 mb-3">
                                    <i class="fas fa-share-alt me-2"></i>Réseaux Sociaux et Contact
                                </h5>
                                <p class="text-muted mb-3">
                                    Ajoutez vos informations de contact pour que les clients puissent vous joindre facilement.
                                </p>

                                {% set social_media = shop.get_social_media() %}

                                <!-- WhatsApp -->
                                <div class="card border-success mb-3">
                                    <div class="card-body">
                                        <div class="row align-items-center">
                                            <div class="col-md-1">
                                                <i class="fab fa-whatsapp fa-2x text-success"></i>
                                            </div>
                                            <div class="col-md-3">
                                                <h6 class="mb-1">WhatsApp</h6>
                                                <small class="text-muted">Contact direct avec vos clients</small>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="whatsapp_enabled"
                                                           name="whatsapp_enabled"
                                                           {% if social_media.whatsapp and social_media.whatsapp.enabled %}checked{% endif %}
                                                           data-target="#whatsapp_fields">
                                                    <label class="form-check-label" for="whatsapp_enabled">
                                                        Activer
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div id="whatsapp_fields" style="display: {% if social_media.whatsapp and social_media.whatsapp.enabled %}block{% else %}none{% endif %}">
                                                    <input type="tel" class="form-control" name="whatsapp_number"
                                                           placeholder="+221 77 123 45 67"
                                                           value="{{ social_media.whatsapp.number if social_media.whatsapp else '' }}">
                                                    <small class="text-muted">Numéro avec indicatif pays (ex: +221 77 123 45 67)</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Telegram -->
                                <div class="card border-info mb-3">
                                    <div class="card-body">
                                        <div class="row align-items-center">
                                            <div class="col-md-1">
                                                <i class="fab fa-telegram fa-2x text-info"></i>
                                            </div>
                                            <div class="col-md-3">
                                                <h6 class="mb-1">Telegram</h6>
                                                <small class="text-muted">Chat et support client</small>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="telegram_enabled"
                                                           name="telegram_enabled"
                                                           {% if social_media.telegram and social_media.telegram.enabled %}checked{% endif %}
                                                           data-target="#telegram_fields">
                                                    <label class="form-check-label" for="telegram_enabled">
                                                        Activer
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div id="telegram_fields" style="display: {% if social_media.telegram and social_media.telegram.enabled %}block{% else %}none{% endif %}">
                                                    <input type="text" class="form-control" name="telegram_username"
                                                           placeholder="@votre_nom_utilisateur"
                                                           value="{{ social_media.telegram.username if social_media.telegram else '' }}">
                                                    <small class="text-muted">Nom d'utilisateur Telegram (avec @)</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Facebook -->
                                <div class="card border-primary mb-3">
                                    <div class="card-body">
                                        <div class="row align-items-center">
                                            <div class="col-md-1">
                                                <i class="fab fa-facebook fa-2x text-primary"></i>
                                            </div>
                                            <div class="col-md-3">
                                                <h6 class="mb-1">Facebook</h6>
                                                <small class="text-muted">Page ou profil Facebook</small>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="facebook_enabled"
                                                           name="facebook_enabled"
                                                           {% if social_media.facebook and social_media.facebook.enabled %}checked{% endif %}
                                                           data-target="#facebook_fields">
                                                    <label class="form-check-label" for="facebook_enabled">
                                                        Activer
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div id="facebook_fields" style="display: {% if social_media.facebook and social_media.facebook.enabled %}block{% else %}none{% endif %}">
                                                    <input type="url" class="form-control" name="facebook_page"
                                                           placeholder="https://facebook.com/votre-page"
                                                           value="{{ social_media.facebook.page if social_media.facebook else '' }}">
                                                    <small class="text-muted">Lien vers votre page Facebook</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Conseil :</strong> Activez WhatsApp pour permettre aux clients de vous contacter directement.
                                    C'est le moyen de communication le plus populaire en Afrique !
                                </div>
                            </div>
                        </div>

                        <!-- Payment Methods Info -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-credit-card fa-2x me-3"></i>
                                        <div>
                                            <h6 class="mb-1">Méthodes de Paiement</h6>
                                            <p class="mb-2 small">Configurez vos méthodes de paiement pour permettre aux clients d'acheter vos produits.</p>
                                            <a href="{{ url_for('shop.payment_methods') }}" class="btn btn-primary btn-sm">
                                                <i class="fas fa-credit-card me-2"></i>Gérer les Méthodes de Paiement
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                {% if shop.status.value == 'rejected' %}
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Boutique Rejetée :</strong> Votre boutique a été rejetée.
                                    Après modification, elle sera soumise à nouveau pour approbation.
                                </div>
                                {% endif %}

                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('shop.vendor_dashboard') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Retour au Tableau de Bord
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-save me-2"></i>Enregistrer les Modifications
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
.current-logo img, .current-banner img {
    transition: all 0.3s ease;
}

.current-logo img:hover, .current-banner img:hover {
    transform: scale(1.05);
}

.card {
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}
</style>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Character counter for description
    const descriptionInput = document.getElementById('description');
    const charCounter = document.createElement('div');
    charCounter.className = 'form-text text-end';
    charCounter.style.marginTop = '-15px';
    charCounter.style.marginBottom = '15px';
    descriptionInput.parentNode.appendChild(charCounter);

    function updateCharCounter() {
        const length = descriptionInput.value.length;
        charCounter.textContent = `${length} caractères`;
        charCounter.className = `form-text text-end ${length < 50 ? 'text-danger' : 'text-success'}`;
    }

    descriptionInput.addEventListener('input', updateCharCounter);
    updateCharCounter();

    // Image preview functionality
    const logoInput = document.getElementById('logo');
    const bannerInput = document.getElementById('banner');

    logoInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const currentLogo = document.querySelector('.current-logo img, .current-logo div');
                if (currentLogo) {
                    if (currentLogo.tagName === 'IMG') {
                        currentLogo.src = e.target.result;
                    } else {
                        currentLogo.innerHTML = `<img src="${e.target.result}" class="rounded-circle border" style="width: 100px; height: 100px; object-fit: cover;">`;
                    }
                }
            };
            reader.readAsDataURL(file);
        }
    });

    bannerInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const currentBanner = document.querySelector('.current-banner img, .current-banner div');
                if (currentBanner) {
                    if (currentBanner.tagName === 'IMG') {
                        currentBanner.src = e.target.result;
                    } else {
                        currentBanner.innerHTML = `<img src="${e.target.result}" class="img-fluid rounded border" style="height: 100px; object-fit: cover; width: 100%;">`;
                    }
                }
            };
            reader.readAsDataURL(file);
        }
    });

    // Social media toggle functionality
    document.querySelectorAll('input[data-target]').forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            const targetId = this.getAttribute('data-target');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                if (this.checked) {
                    targetElement.style.display = 'block';
                    // Focus on the input field
                    const input = targetElement.querySelector('input');
                    if (input) {
                        setTimeout(() => input.focus(), 100);
                    }
                } else {
                    targetElement.style.display = 'none';
                    // Clear the input value when disabled
                    const input = targetElement.querySelector('input');
                    if (input) {
                        input.value = '';
                    }
                }
            }
        });
    });
});
</script>
{% endblock %}
