{% extends "base.html" %}

{% block title %}Tableau de Bord Vendeur - Afroly.org{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-5 fw-bold">
                <i class="fas fa-chart-bar me-3"></i>Tableau de Bord Vendeur
            </h1>
            <p class="lead text-muted">Gérez vos boutiques et suivez vos performances</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('shop.create') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>Créer une Boutique
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ shops|length }}</h4>
                            <p class="card-text">Boutiques</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-store fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ total_products }}</h4>
                            <p class="card-text">Produits Actifs</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-box fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ pending_products }}</h4>
                            <p class="card-text">En Attente</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ total_orders }}</h4>
                            <p class="card-text">Commandes</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-shopping-cart fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Tier Info -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info alert-permanent tier-info" style="border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="alert-heading mb-2">
                            <i class="fas fa-crown me-2 text-warning"></i>Niveau : {{ current_user.tier.value.title() }}
                        </h5>
                        <p class="mb-0 fw-bold">
                            {% set product_limit = get_product_limit(current_user.tier.value) %}
                            {% if current_user.tier.value == 'free' %}
                            <span class="text-secondary">Niveau Gratuit</span> - Commission 15% • 1 boutique max • {{ product_limit }} produits max
                            {% elif current_user.tier.value == 'premium' %}
                            <span class="text-primary">Niveau Premium</span> - Commission 10% • 1 boutique max • {{ product_limit }} produits max
                            {% elif current_user.tier.value == 'gold' %}
                            <span class="text-warning">Niveau Gold</span> - Commission 5% • 1 boutique max • {{ product_limit }} produits max
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        {% if current_user.tier.value != 'gold' %}
                        <a href="#" class="btn btn-warning">
                            <i class="fas fa-arrow-up me-2"></i>Améliorer
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Shops Section -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Mes Boutiques</h5>
                    {% if current_user.can_create_shop() %}
                    <a href="{{ url_for('shop.create') }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i>Nouvelle Boutique
                    </a>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if shops %}
                    <div class="row">
                        {% for shop in shops %}
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                {% if shop.banner %}
                                <img src="/static/uploads/shops/{{ shop.banner }}" class="card-img-top"
                                     style="height: 120px; object-fit: cover;" alt="{{ shop.name }}">
                                {% else %}
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
                                     style="height: 120px;">
                                    <i class="fas fa-store fa-2x text-muted"></i>
                                </div>
                                {% endif %}

                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-2">
                                        {% if shop.logo %}
                                        <img src="/static/uploads/shops/{{ shop.logo }}" alt="{{ shop.name }}"
                                             class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                        {% endif %}
                                        <h6 class="card-title mb-0">{{ shop.name }}</h6>
                                    </div>

                                    <p class="card-text small text-muted">
                                        {{ shop.description[:80] }}{% if shop.description|length > 80 %}...{% endif %}
                                    </p>

                                    <div class="mb-2">
                                        {% if shop.status.value == 'active' %}
                                        <span class="badge bg-success">Actif</span>
                                        {% elif shop.status.value == 'pending' %}
                                        <span class="badge bg-warning">En Attente</span>
                                        {% elif shop.status.value == 'rejected' %}
                                        <span class="badge bg-danger">Rejeté</span>
                                        {% else %}
                                        <span class="badge bg-secondary">Inactif</span>
                                        {% endif %}

                                        <small class="text-muted ms-2">
                                            {{ shop.get_active_products_count() }} produits
                                        </small>
                                    </div>

                                    {% if shop.status.value == 'active' %}
                                    <!-- Short URL Section -->
                                    <div class="mt-3 p-2 bg-light rounded">
                                        <small class="text-muted d-block mb-1">
                                            <i class="fas fa-link me-1"></i>Lien Court de Partage
                                        </small>
                                        <div class="input-group input-group-sm">
                                            <input type="text"
                                                   class="form-control"
                                                   id="shortUrl{{ shop.id }}"
                                                   value="{{ request.url_root }}s/{{ get_short_url_for_shop(shop) }}"
                                                   readonly>
                                            <button class="btn btn-outline-primary"
                                                    type="button"
                                                    onclick="copyShortUrl({{ shop.id }})"
                                                    title="Copier le lien">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>Partagez ce lien sur vos réseaux sociaux
                                        </small>
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="card-footer">
                                    <div class="btn-group w-100" role="group">
                                        {% if shop.status.value == 'active' %}
                                        <a href="{{ url_for('shop.view', slug=shop.slug) }}"
                                           class="btn btn-sm btn-outline-primary">Voir</a>
                                        {% endif %}
                                        <a href="{{ url_for('shop.edit', shop_id=shop.id) }}"
                                           class="btn btn-sm btn-outline-secondary">Modifier</a>
                                        <a href="{{ url_for('shop.manage_products', shop_id=shop.id) }}"
                                           class="btn btn-sm btn-outline-info">Produits</a>
                                        <a href="{{ url_for('shop.manage_orders', shop_id=shop.id) }}"
                                           class="btn btn-sm btn-outline-success">Commandes</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-store-slash fa-3x text-muted mb-3"></i>
                        <h5>Aucune boutique</h5>
                        <p class="text-muted mb-3">Vous n'avez pas encore créé de boutique.</p>
                        <a href="{{ url_for('shop.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Créer Ma Première Boutique
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Orders Sidebar -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Commandes Récentes</h5>
                </div>
                <div class="card-body">
                    {% if recent_orders %}
                    {% for order in recent_orders %}
                    <div class="d-flex align-items-center mb-3 {% if not loop.last %}border-bottom pb-3{% endif %}">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">Commande #{{ order.id }}</h6>
                            <small class="text-muted">{{ order.created_at.strftime('%d/%m/%Y %H:%M') }}</small>
                            <div class="mt-1">
                                {% if order.status.value == 'pending' %}
                                <span class="badge bg-warning">En Attente</span>
                                {% elif order.status.value == 'processing' %}
                                <span class="badge bg-info">En Cours</span>
                                {% elif order.status.value == 'shipped' %}
                                <span class="badge bg-primary">Expédiée</span>
                                {% elif order.status.value == 'delivered' %}
                                <span class="badge bg-success">Livrée</span>
                                {% elif order.status.value == 'cancelled' %}
                                <span class="badge bg-danger">Annulée</span>
                                {% elif order.status.value == 'refunded' %}
                                <span class="badge bg-secondary">Remboursée</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ order.status.value.title() }}</span>
                                {% endif %}
                                <span class="fw-bold text-primary ms-2">{{ "%.0f"|format(order.total) }} {{ order.shop.get_currency_symbol() }}</span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-shopping-cart fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">Aucune commande récente</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">Actions Rapides</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('shop.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Nouvelle Boutique
                        </a>
                        {% if shops %}
                        <a href="{{ url_for('product.create', shop_id=shops[0].id) }}" class="btn btn-success">
                            <i class="fas fa-box me-2"></i>Ajouter Produit
                        </a>
                        {% endif %}
                        {% if current_user.tier.value in ['premium', 'gold'] %}
                        <a href="{{ url_for('dropshipping.dashboard') }}" class="btn btn-info">
                            <i class="fas fa-shipping-fast me-2"></i>Dropshipping
                        </a>
                        {% endif %}
                        <a href="{{ url_for('shop.payment_methods') }}" class="btn btn-warning">
                            <i class="fas fa-credit-card me-2"></i>Méthodes de Paiement
                        </a>
                        <a href="{{ url_for('auth.profile') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-user me-2"></i>Mon Profil
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Shop Traffic Statistics Section -->
    {% if shops and shop_traffic_stats %}
    <div class="row mt-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2 text-primary"></i>Statistiques de Trafic des Boutiques
                    </h5>
                    <small class="text-muted">Derniers 30 jours</small>
                </div>
                <div class="card-body">
                    {% for shop in shops %}
                    {% set stats = shop_traffic_stats[shop.id] %}
                    <div class="shop-stats-section mb-4 {% if not loop.last %}border-bottom pb-4{% endif %}">
                        <div class="row align-items-center mb-3">
                            <div class="col-md-6">
                                <h6 class="mb-0">
                                    {% if shop.logo %}
                                    <img src="/static/uploads/shops/{{ shop.logo }}" alt="{{ shop.name }}" class="rounded me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                    {% else %}
                                    <i class="fas fa-store text-primary me-2"></i>
                                    {% endif %}
                                    {{ shop.name }}
                                </h6>
                                <small class="text-muted">{{ shop.slug }}</small>
                            </div>
                            <div class="col-md-6 text-end">
                                <a href="{{ url_for('shop.view', slug=shop.slug) }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                    <i class="fas fa-external-link-alt me-1"></i>Voir Boutique
                                </a>
                            </div>
                        </div>

                        <!-- Traffic Stats Cards -->
                        <div class="row">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card bg-primary text-white h-100">
                                    <div class="card-body text-center">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <i class="fas fa-eye fa-2x me-3"></i>
                                            <div>
                                                <h4 class="mb-0">{{ stats.totals.views or 0 }}</h4>
                                                <small>Vues Totales</small>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card bg-success text-white h-100">
                                    <div class="card-body text-center">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <i class="fas fa-users fa-2x me-3"></i>
                                            <div>
                                                <h4 class="mb-0">{{ stats.totals.unique_visitors or 0 }}</h4>
                                                <small>Visiteurs Uniques</small>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card bg-info text-white h-100">
                                    <div class="card-body text-center">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <i class="fas fa-box fa-2x me-3"></i>
                                            <div>
                                                <h4 class="mb-0">{{ stats.totals.product_views or 0 }}</h4>
                                                <small>Vues Produits</small>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card bg-warning text-white h-100">
                                    <div class="card-body text-center">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <i class="fas fa-shopping-cart fa-2x me-3"></i>
                                            <div>
                                                <h4 class="mb-0">{{ stats.totals.orders or 0 }}</h4>
                                                <small>Commandes</small>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Traffic Chart -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Évolution du Trafic (7 derniers jours)</h6>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="trafficChart{{ shop.id }}" height="100"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Conversion Rate -->
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Taux de Conversion</h6>
                                    </div>
                                    <div class="card-body text-center">
                                        <div class="mb-3">
                                            {% set conversion_rate = 0 %}
                                            {% if stats.totals.unique_visitors and stats.totals.unique_visitors > 0 %}
                                                {% set conversion_rate = (stats.totals.orders / stats.totals.unique_visitors) * 100 %}
                                            {% endif %}
                                            <h3 class="text-primary">{{ "%.1f"|format(conversion_rate) }}%</h3>
                                            <small class="text-muted">Visiteurs → Commandes</small>
                                        </div>
                                        <div class="progress mb-2" style="height: 10px;">
                                            <div class="progress-bar bg-primary" role="progressbar"
                                                 style="width: {{ conversion_rate }}%"
                                                 aria-valuenow="{{ conversion_rate }}"
                                                 aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <small class="text-muted">
                                            {{ stats.totals.orders or 0 }} commandes sur {{ stats.totals.unique_visitors or 0 }} visiteurs
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Revenus (30 jours)</h6>
                                    </div>
                                    <div class="card-body text-center">
                                        <div class="mb-3">
                                            <h3 class="text-success">{{ "%.0f"|format(stats.totals.revenue or 0) }} FCFA</h3>
                                            <small class="text-muted">Revenus Totaux</small>
                                        </div>
                                        <div class="text-muted">
                                            <small>
                                                {% if stats.totals.orders and stats.totals.orders > 0 %}
                                                Moyenne: {{ "%.0f"|format((stats.totals.revenue or 0) / stats.totals.orders) }} FCFA/commande
                                                {% else %}
                                                Aucune commande
                                                {% endif %}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}

                    <!-- Product Statistics Summary (Keep This) -->
                    <div class="row mt-4 pt-4 border-top">
                        <div class="col-12">
                            <h6 class="text-center mb-3">Résumé Global de Toutes vos Boutiques</h6>

                            <!-- Product Stats Only (Remove Traffic Stats) -->
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="border rounded p-3 bg-light">
                                        <h4 class="text-secondary mb-1">{{ total_products }}</h4>
                                        <small class="text-muted">Produits Totaux</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="border rounded p-3 bg-light">
                                        <h4 class="text-warning mb-1">{{ pending_products }}</h4>
                                        <small class="text-muted">Produits en Attente</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="border rounded p-3 bg-light">
                                        <h4 class="text-success mb-1">{{ total_orders }}</h4>
                                        <small class="text-muted">Commandes Totales</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="border rounded p-3 bg-light">
                                        <h4 class="text-info mb-1">{{ shops|length }}</h4>
                                        <small class="text-muted">Boutiques Actives</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_head %}
<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.btn-group .btn {
    font-size: 0.8rem;
}

.short-url-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
}

.short-url-section:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

/* Traffic Statistics Styling */
.shop-stats-section {
    transition: all 0.3s ease;
}

.shop-stats-section:hover {
    background-color: rgba(0, 123, 255, 0.02);
    border-radius: 10px;
    padding: 15px;
}

.shop-stats-section .card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.shop-stats-section .card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.shop-stats-section .card.bg-primary:hover {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.shop-stats-section .card.bg-success:hover {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
}

.shop-stats-section .card.bg-info:hover {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
}

.shop-stats-section .card.bg-warning:hover {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
}

.progress {
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.6s ease;
}

/* Chart container styling */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .shop-stats-section .card-body {
        padding: 1rem 0.5rem;
    }

    .shop-stats-section .card h4 {
        font-size: 1.5rem;
    }

    .chart-container {
        height: 250px;
    }
}
</style>
{% endblock %}

{% block extra_scripts %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
function copyShortUrl(shopId) {
    const input = document.getElementById('shortUrl' + shopId);
    const button = input.nextElementSibling;
    const originalIcon = button.innerHTML;

    // Select and copy the text
    input.select();
    input.setSelectionRange(0, 99999); // For mobile devices

    try {
        document.execCommand('copy');

        // Show success feedback
        button.innerHTML = '<i class="fas fa-check text-success"></i>';
        button.classList.remove('btn-outline-primary');
        button.classList.add('btn-success');

        // Show toast notification
        showToast('Lien copié!', 'Le lien court a été copié dans le presse-papiers.', 'success');

        // Reset button after 2 seconds
        setTimeout(() => {
            button.innerHTML = originalIcon;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-primary');
        }, 2000);

    } catch (err) {
        console.error('Erreur lors de la copie:', err);
        showToast('Erreur', 'Impossible de copier le lien. Veuillez le sélectionner manuellement.', 'error');
    }

    // Deselect the input
    input.blur();
}

function showToast(title, message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';

    toast.innerHTML = `
        <strong>${title}</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 3000);
}

// Initialize traffic charts
document.addEventListener('DOMContentLoaded', function() {
    {% if shops and shop_traffic_stats %}
    {% for shop in shops %}
    {% set stats = shop_traffic_stats[shop.id] %}

    // Traffic chart for shop {{ shop.id }}
    const ctx{{ shop.id }} = document.getElementById('trafficChart{{ shop.id }}');
    if (ctx{{ shop.id }}) {
        new Chart(ctx{{ shop.id }}, {
            type: 'line',
            data: {
                labels: [
                    {% if stats.daily_stats %}
                    {% for day in stats.daily_stats %}
                    '{{ day.date.strftime("%d/%m") }}',
                    {% endfor %}
                    {% else %}
                    'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'
                    {% endif %}
                ],
                datasets: [{
                    label: 'Vues',
                    data: [
                        {% if stats.daily_stats %}
                        {% for day in stats.daily_stats %}
                        {{ day.views or 0 }},
                        {% endfor %}
                        {% else %}
                        0, 0, 0, 0, 0, 0, 0
                        {% endif %}
                    ],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.1,
                    fill: true
                }, {
                    label: 'Visiteurs Uniques',
                    data: [
                        {% if stats.daily_stats %}
                        {% for day in stats.daily_stats %}
                        {{ day.unique_visitors or 0 }},
                        {% endfor %}
                        {% else %}
                        0, 0, 0, 0, 0, 0, 0
                        {% endif %}
                    ],
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    tension: 0.1,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: false
                    }
                }
            }
        });
    }
    {% endfor %}
    {% endif %}
});
</script>
{% endblock %}
