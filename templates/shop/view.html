{% extends "base.html" %}

{% block title %}{{ shop.name }} - Afroly.org{% endblock %}

{% block content %}
<!-- Shop Header -->
<div class="shop-header">
    {% if shop.banner %}
    <div class="shop-banner" style="background-image: url('/static/uploads/shops/{{ shop.banner }}');">
        <div class="banner-overlay"></div>
    </div>
    {% else %}
    <div class="shop-banner bg-primary">
        <div class="banner-overlay"></div>
    </div>
    {% endif %}

    <div class="container">
        <div class="shop-info">
            <div class="row align-items-end">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        {% if shop.logo %}
                        <img src="/static/uploads/shops/{{ shop.logo }}" alt="{{ shop.name }}"
                             class="shop-logo rounded-circle border border-white border-3">
                        {% else %}
                        <div class="shop-logo rounded-circle bg-white d-flex align-items-center justify-content-center border border-3">
                            <i class="fas fa-store fa-2x text-primary"></i>
                        </div>
                        {% endif %}

                        <div class="ms-4 text-white">
                            <h1 class="shop-name mb-2">{{ shop.name }}</h1>
                            <div class="shop-meta">
                                <div class="rating mb-2">
                                    {% for i in range(5) %}
                                        {% if i < shop.get_average_rating() %}
                                        <i class="fas fa-star text-warning"></i>
                                        {% else %}
                                        <i class="far fa-star text-warning"></i>
                                        {% endif %}
                                    {% endfor %}
                                    <span class="ms-2">({{ reviews|length }} avis)</span>
                                </div>
                                <p class="mb-1">
                                    <i class="fas fa-map-marker-alt me-2"></i>{{ shop.address }}
                                </p>
                                <p class="mb-0">
                                    <i class="fas fa-calendar me-2"></i>Membre depuis {{ shop.created_at.strftime('%B %Y') }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <div class="shop-actions">
                        <button class="btn btn-outline-light me-2" data-bs-toggle="tooltip" title="Ajouter aux favoris">
                            <i class="fas fa-heart"></i>
                        </button>
                        <div class="dropdown d-inline-block">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown" title="Partager">
                                <i class="fas fa-share"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="#" onclick="shareOnFacebook('{{ request.url }}', '{{ shop.name }}')">
                                        <i class="fab fa-facebook text-primary me-2"></i>Facebook
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#" onclick="shareOnWhatsApp('{{ request.url }}', '{{ shop.name }}')">
                                        <i class="fab fa-whatsapp text-success me-2"></i>WhatsApp
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#" onclick="shareOnTwitter('{{ request.url }}', '{{ shop.name }}')">
                                        <i class="fab fa-twitter text-info me-2"></i>Twitter
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#" onclick="shareOnTelegram('{{ request.url }}', '{{ shop.name }}')">
                                        <i class="fab fa-telegram text-primary me-2"></i>Telegram
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="#" onclick="copyToClipboard('{{ request.url }}')">
                                        <i class="fas fa-copy text-secondary me-2"></i>Copier le lien
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container py-4">
    <!-- Shop Description -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">À Propos de {{ shop.name }}</h5>
                    <p class="card-text">{{ shop.description }}</p>

                    <!-- Social Media & Contact -->
                    {% set social_media = shop.get_social_media() %}
                    {% if shop.has_enabled_social_media() %}
                    <div class="social-contact mt-3">
                        <h6>Contactez-nous :</h6>
                        <div class="d-flex flex-wrap gap-2">
                            <!-- WhatsApp -->
                            {% if social_media.whatsapp and social_media.whatsapp.enabled %}
                            <a href="{{ shop.get_whatsapp_link() }}" target="_blank"
                               class="btn btn-success btn-sm">
                                <i class="fab fa-whatsapp me-1"></i>WhatsApp
                            </a>
                            {% endif %}

                            <!-- Telegram -->
                            {% if social_media.telegram and social_media.telegram.enabled %}
                            <a href="https://t.me/{{ social_media.telegram.username.lstrip('@') }}" target="_blank"
                               class="btn btn-info btn-sm">
                                <i class="fab fa-telegram me-1"></i>Telegram
                            </a>
                            {% endif %}

                            <!-- Facebook -->
                            {% if social_media.facebook and social_media.facebook.enabled %}
                            <a href="{{ social_media.facebook.page }}" target="_blank"
                               class="btn btn-primary btn-sm">
                                <i class="fab fa-facebook me-1"></i>Facebook
                            </a>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Shop Stats -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Statistiques</h5>
                    <div class="row text-center">
                        <div class="col-12">
                            <h4 class="text-primary">{{ shop.get_active_products_count() }}</h4>
                            <small class="text-muted">Produits Disponibles</small>
                        </div>
                    </div>

                    <hr>

                    <div class="contact-info">
                        <h6>Contact</h6>
                        {% if shop.contact_email %}
                        <p class="mb-1">
                            <i class="fas fa-envelope me-2"></i>
                            <a href="mailto:{{ shop.contact_email }}">{{ shop.contact_email }}</a>
                        </p>
                        {% endif %}
                        {% if shop.contact_phone %}
                        <p class="mb-1">
                            <i class="fas fa-phone me-2"></i>
                            <a href="tel:{{ shop.contact_phone }}">{{ shop.contact_phone }}</a>
                        </p>
                        {% endif %}

                        <!-- WhatsApp Contact Button -->
                        {% if shop.get_whatsapp_number() %}
                        <div class="whatsapp-contact mt-3">
                            <a href="{{ shop.get_whatsapp_link('Bonjour, j\'ai vu votre boutique ' + shop.name + ' sur Afroly.org') }}"
                               target="_blank" class="btn btn-success btn-sm w-100">
                                <i class="fab fa-whatsapp me-2"></i>Contacter sur WhatsApp
                            </a>
                            <small class="text-muted d-block text-center mt-1">{{ shop.get_whatsapp_number() }}</small>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Products Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Produits ({{ products.total }})</h5>

                    <!-- Sort Options -->
                    <form method="GET" class="d-flex align-items-center">
                        <label for="sort" class="form-label me-2 mb-0">Trier par :</label>
                        <select name="sort" id="sort" class="form-select form-select-sm" onchange="this.form.submit()">
                            <option value="newest" {% if sort_by == 'newest' %}selected{% endif %}>Plus récents</option>
                            <option value="price_low" {% if sort_by == 'price_low' %}selected{% endif %}>Prix croissant</option>
                            <option value="price_high" {% if sort_by == 'price_high' %}selected{% endif %}>Prix décroissant</option>
                            <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Nom A-Z</option>
                        </select>
                    </form>
                </div>

                <div class="card-body">
                    {% if products.items %}
                    <div class="row">
                        {% for product in products.items %}
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card h-100 product-card">
                                <div class="position-relative">
                                    <img src="{{ product.get_main_image() }}" class="card-img-top"
                                         alt="{{ product.name }}" style="height: 200px; object-fit: cover;">

                                    {% if product.sale_price %}
                                    <span class="badge bg-danger position-absolute top-0 start-0 m-2">
                                        -{{ "%.0f"|format(((product.price - product.sale_price) / product.price * 100)) }}%
                                    </span>
                                    {% endif %}

                                    {% if product.featured %}
                                    <span class="badge bg-warning position-absolute top-0 end-0 m-2">
                                        <i class="fas fa-star"></i>
                                    </span>
                                    {% endif %}
                                </div>

                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title">{{ product.name }}</h6>
                                    <p class="card-text text-muted small flex-grow-1">
                                        {{ product.short_description }}
                                    </p>

                                    <div class="price-section mb-3">
                                        {% if product.sale_price %}
                                        <span class="h5 text-primary mb-0">{{ "%.0f"|format(product.sale_price) }} {{ shop.get_currency_symbol() }}</span>
                                        <span class="text-muted text-decoration-line-through ms-2">{{ "%.0f"|format(product.price) }} {{ shop.get_currency_symbol() }}</span>
                                        {% else %}
                                        <span class="h5 text-primary mb-0">{{ "%.0f"|format(product.price) }} {{ shop.get_currency_symbol() }}</span>
                                        {% endif %}
                                    </div>

                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            {% if product.stock_quantity > 0 %}
                                            <i class="fas fa-check-circle text-success"></i> En stock
                                            {% else %}
                                            <i class="fas fa-times-circle text-danger"></i> Rupture
                                            {% endif %}
                                        </small>

                                        <div class="btn-group">
                                            <a href="{{ url_for('product.view', slug=product.slug) }}"
                                               class="btn btn-sm btn-primary">Voir Produit</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Pagination -->
                    {% if products.pages > 1 %}
                    <nav aria-label="Navigation des produits" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if products.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('shop.view', slug=shop.slug, sort=sort_by, page=products.prev_num) }}">
                                    <i class="fas fa-chevron-left me-1"></i>Précédent
                                </a>
                            </li>
                            {% endif %}

                            {% for page_num in products.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != products.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('shop.view', slug=shop.slug, sort=sort_by, page=page_num) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if products.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('shop.view', slug=shop.slug, sort=sort_by, page=products.next_num) }}">
                                    Suivant<i class="fas fa-chevron-right ms-1"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-box-open fa-4x text-muted mb-4"></i>
                        <h4>Aucun produit disponible</h4>
                        <p class="text-muted">Cette boutique n'a pas encore de produits en vente.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Reviews Section -->
    {% if reviews %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Avis Clients</h5>
                </div>
                <div class="card-body">
                    {% for review in reviews %}
                    <div class="review-item {% if not loop.last %}border-bottom pb-3 mb-3{% endif %}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="rating mb-1">
                                    {% for i in range(5) %}
                                        {% if i < review.rating %}
                                        <i class="fas fa-star text-warning"></i>
                                        {% else %}
                                        <i class="far fa-star text-warning"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <h6 class="mb-1">{{ review.title }}</h6>
                                <p class="mb-1">{{ review.comment }}</p>
                                <small class="text-muted">
                                    Par {{ review.user.get_full_name() }} • {{ review.created_at.strftime('%d/%m/%Y') }}
                                </small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_head %}
<style>
.shop-header {
    position: relative;
    margin-bottom: 2rem;
}

.shop-banner {
    height: 300px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0,0,0,0.7), rgba(0,0,0,0.3));
}

.shop-info {
    position: absolute;
    bottom: -50px;
    left: 0;
    right: 0;
    z-index: 10;
}

.shop-logo {
    width: 100px;
    height: 100px;
    object-fit: cover;
}

.shop-name {
    font-size: 2.5rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.product-card {
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.rating i {
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .shop-name {
        font-size: 1.8rem;
    }

    .shop-logo {
        width: 80px;
        height: 80px;
    }
}
</style>
{% endblock %}

{% block extra_scripts %}
<script>
function addToCart(productId) {
    // Add to cart functionality
    fetch('/cart/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: 1
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>Produit ajouté au panier !
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);

            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 3000);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// Share functions
function shareOnFacebook(url, title) {
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank', 'width=600,height=400');
}

function shareOnTwitter(url, title) {
    window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`, '_blank', 'width=600,height=400');
}

function shareOnWhatsApp(url, title) {
    window.open(`https://wa.me/?text=${encodeURIComponent(title + ' ' + url)}`, '_blank');
}

function shareOnTelegram(url, title) {
    window.open(`https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`, '_blank');
}

function copyToClipboard(url) {
    navigator.clipboard.writeText(url).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'alert alert-success alert-dismissible fade show position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
        toast.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            Lien copié dans le presse-papiers!
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(toast);

        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    });
}
</script>
{% endblock %}
