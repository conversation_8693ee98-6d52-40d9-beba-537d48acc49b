{% extends "base.html" %}

{% block title %}Commande #{{ order.id }} - {{ shop.name }} - Afroly.org{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('shop.vendor_dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('shop.manage_orders', shop_id=shop.id) }}">Commandes</a></li>
                    <li class="breadcrumb-item active">Commande #{{ order.id }}</li>
                </ol>
            </nav>

            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">Commande #{{ order.id }}</h1>
                    <p class="text-muted mb-0">{{ order.created_at.strftime('%d/%m/%Y à %H:%M') }}</p>
                </div>
                <div>
                    {% if order.status.value == 'pending' %}
                    <span class="badge bg-warning fs-6">En Attente</span>
                    {% elif order.status.value == 'processing' %}
                    <span class="badge bg-primary fs-6">En Cours</span>
                    {% elif order.status.value == 'shipped' %}
                    <span class="badge bg-secondary fs-6">Expédiée</span>
                    {% elif order.status.value == 'delivered' %}
                    <span class="badge bg-success fs-6">Livrée</span>
                    {% elif order.status.value == 'cancelled' %}
                    <span class="badge bg-danger fs-6">Annulée</span>
                    {% elif order.status.value == 'refunded' %}
                    <span class="badge bg-info fs-6">Remboursée</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Order Details -->
        <div class="col-lg-8">
            <!-- Order Items -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-box me-2"></i>Articles Commandés
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Prix Unitaire</th>
                                    <th>Quantité</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in order.items %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{{ item.product.get_main_image() }}" alt="{{ item.product.name }}"
                                                 class="rounded me-3" style="width: 60px; height: 60px; object-fit: cover;">
                                            <div>
                                                <h6 class="mb-1">{{ item.product.name }}</h6>
                                                <small class="text-muted">SKU: {{ item.product.sku or 'N/A' }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ "%.0f"|format(item.price) }} FCFA</td>
                                    <td>{{ item.quantity }}</td>
                                    <td class="fw-bold">{{ "%.0f"|format(item.total) }} FCFA</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Shipping Address -->
            {% if order.shipping_address %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>Adresse de Livraison
                    </h5>
                </div>
                <div class="card-body">
                    {% if order.shipping_address is string %}
                        <p class="mb-0">{{ order.shipping_address }}</p>
                    {% else %}
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>Nom:</strong> {{ order.shipping_address.get('first_name', '') }} {{ order.shipping_address.get('last_name', '') }}</p>
                                <p class="mb-1"><strong>Adresse:</strong> {{ order.shipping_address.get('address_line_1', '') }}</p>
                                {% if order.shipping_address.get('address_line_2') %}
                                <p class="mb-1">{{ order.shipping_address.get('address_line_2') }}</p>
                                {% endif %}
                                <p class="mb-1"><strong>Ville:</strong> {{ order.shipping_address.get('city', '') }}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>État/Région:</strong> {{ order.shipping_address.get('state', '') }}</p>
                                <p class="mb-1"><strong>Code Postal:</strong> {{ order.shipping_address.get('postal_code', '') }}</p>
                                <p class="mb-1"><strong>Pays:</strong> {{ order.shipping_address.get('country', '') }}</p>
                                {% if order.shipping_address.get('phone') %}
                                <p class="mb-1"><strong>Téléphone:</strong> {{ order.shipping_address.get('phone') }}</p>
                                {% endif %}
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Notes -->
            {% if order.customer_notes or order.admin_notes %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-sticky-note me-2"></i>Notes
                    </h5>
                </div>
                <div class="card-body">
                    {% if order.customer_notes %}
                    <div class="mb-3">
                        <h6 class="text-primary">Notes du Client:</h6>
                        <p class="mb-0">{{ order.customer_notes }}</p>
                    </div>
                    {% endif %}
                    {% if order.admin_notes %}
                    <div>
                        <h6 class="text-warning">Notes Administratives:</h6>
                        <p class="mb-0">{{ order.admin_notes }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Customer Info -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>Informations Client
                    </h5>
                </div>
                <div class="card-body">
                    {% if order.customer %}
                    <p><strong>Nom:</strong> {{ order.customer.get_full_name() }}</p>
                    {% if order.customer.phone %}
                    <p><strong>Téléphone:</strong>
                        <span class="fw-bold text-success">{{ order.customer.phone }}</span>
                        <div class="mt-2">
                            <a href="#" onclick="contactWhatsApp('{{ order.customer.phone }}')" class="btn btn-sm btn-success me-2">
                                <i class="fab fa-whatsapp me-1"></i>WhatsApp
                            </a>
                            <a href="tel:{{ order.customer.phone }}" class="btn btn-sm btn-primary">
                                <i class="fas fa-phone me-1"></i>Appeler
                            </a>
                        </div>
                    </p>
                    {% endif %}
                    <p><strong>Email:</strong> <small class="text-muted">{{ order.customer.email }}</small></p>
                    {% else %}
                    <p class="text-muted">Commande manuelle</p>
                    {% endif %}

                    {% if order.payment_method %}
                    <p><strong>Méthode de Paiement:</strong> {{ order.payment_method }}</p>
                    {% endif %}
                </div>
            </div>

            <!-- Order Summary -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>Résumé
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Sous-total:</span>
                        <span>{{ "%.0f"|format(order.subtotal) }} FCFA</span>
                    </div>
                    {% if order.shipping_cost > 0 %}
                    <div class="d-flex justify-content-between mb-2">
                        <span>Livraison:</span>
                        <span>{{ "%.0f"|format(order.shipping_cost) }} FCFA</span>
                    </div>
                    {% else %}
                    <div class="d-flex justify-content-between mb-2">
                        <span>Livraison:</span>
                        <span class="text-success">Gratuite</span>
                    </div>
                    {% endif %}
                    {% if order.tax_amount > 0 %}
                    <div class="d-flex justify-content-between mb-2">
                        <span>Taxes:</span>
                        <span>{{ "%.0f"|format(order.tax_amount) }} FCFA</span>
                    </div>
                    {% endif %}
                    <hr>
                    <div class="d-flex justify-content-between">
                        <strong>Total:</strong>
                        <strong class="text-primary">{{ "%.0f"|format(order.total) }} FCFA</strong>
                    </div>

                    <div class="mt-3">
                        <strong>Statut du Paiement:</strong>
                        {% if order.payment_status == 'paid' %}
                        <span class="badge bg-success">Payée</span>
                        {% elif order.payment_status == 'pending' %}
                        <span class="badge bg-warning">En Attente</span>
                        {% else %}
                        <span class="badge bg-secondary">Non Payée</span>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <!-- Status Update Buttons -->
                        {% if order.status.value == 'pending' %}
                        <button type="button" class="btn btn-success" onclick="updateOrderStatus({{ order.id }}, 'processing')">
                            <i class="fas fa-cog me-2"></i>Mettre en Cours
                        </button>
                        {% endif %}

                        {% if order.status.value in ['pending', 'processing'] %}
                        <button type="button" class="btn btn-primary" onclick="updateOrderStatus({{ order.id }}, 'shipped')">
                            <i class="fas fa-shipping-fast me-2"></i>Marquer comme Expédiée
                        </button>
                        {% endif %}

                        {% if order.status.value == 'shipped' %}
                        <button type="button" class="btn btn-info" onclick="updateOrderStatus({{ order.id }}, 'delivered')">
                            <i class="fas fa-check-circle me-2"></i>Marquer comme Livrée
                        </button>
                        {% endif %}

                        {% if order.status.value in ['pending', 'processing'] %}
                        <button type="button" class="btn btn-danger" onclick="updateOrderStatus({{ order.id }}, 'cancelled')">
                            <i class="fas fa-times me-2"></i>Annuler la Commande
                        </button>
                        {% endif %}

                        <!-- Contact Customer -->
                        {% if order.customer and order.customer.phone %}
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-success" onclick="contactWhatsApp('{{ order.customer.phone }}')">
                                <i class="fab fa-whatsapp me-2"></i>Contacter via WhatsApp
                            </button>
                            <a href="tel:{{ order.customer.phone }}" class="btn btn-outline-primary">
                                <i class="fas fa-phone me-2"></i>Appeler le Client
                            </a>
                        </div>
                        {% endif %}

                        <!-- Print -->
                        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-2"></i>Imprimer la Commande
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function updateOrderStatus(orderId, newStatus) {
    if (confirm('Êtes-vous sûr de vouloir changer le statut de cette commande ?')) {
        fetch(`/shop/order/${orderId}/status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                status: newStatus
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur lors de la mise à jour du statut');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur lors de la mise à jour du statut');
        });
    }
}

function contactWhatsApp(phone) {
    // Clean phone number and format for WhatsApp
    let cleanPhone = phone.replace(/[^\d+]/g, '');
    if (!cleanPhone.startsWith('+')) {
        cleanPhone = '+' + cleanPhone;
    }

    const message = encodeURIComponent('Bonjour, je vous contacte concernant votre commande sur Afroly.org.');
    window.open(`https://wa.me/${cleanPhone.replace('+', '')}?text=${message}`, '_blank');
}

function contactCustomer(phone) {
    // For backward compatibility - redirect to WhatsApp
    contactWhatsApp(phone);
}
</script>
{% endblock %}
