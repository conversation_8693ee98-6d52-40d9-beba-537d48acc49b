{% extends "base.html" %}

{% block title %}Modifier Méth<PERSON> de Paiement - {{ shop.name }} - AfroMall{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="display-6 fw-bold">
                        <i class="fas fa-credit-card me-3 text-primary"></i>Méthodes de Paiement
                    </h1>
                    <p class="lead text-muted">{{ shop.name }} - {{ shop.country }}</p>
                </div>
                <div>
                    <a href="{{ url_for('shop.payment_methods') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la Liste
                    </a>
                </div>
            </div>

            <!-- Payment Methods Form -->
            <form method="POST">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-store me-2"></i>{{ shop.name }}
                            <span class="badge bg-light text-dark ms-2">{{ shop.country }}</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-4">
                            Configurez les méthodes de paiement que vos clients peuvent utiliser pour acheter vos produits.
                        </p>
                        
                        {% set available_methods = shop.get_available_payment_methods() %}
                        {% set current_methods = shop.get_payment_methods() %}
                        
                        <!-- Mobile Money -->
                        {% if available_methods.mobile_money %}
                        <div class="payment-category mb-4">
                            <h6 class="text-success mb-3">
                                <i class="fas fa-mobile-alt me-2"></i>Mobile Money
                            </h6>
                            <div class="row">
                                {% for method_key, method_config in available_methods.mobile_money.items() %}
                                {% set current_method = current_methods|selectattr('type', 'equalto', method_key)|first %}
                                <div class="col-md-6 mb-3">
                                    <div class="card border-success">
                                        <div class="card-body">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input payment-method-toggle" type="checkbox" 
                                                       name="payment_methods" value="{{ method_key }}" 
                                                       id="{{ method_key }}" 
                                                       {% if current_method %}checked{% endif %}
                                                       data-target="#{{ method_key }}_fields">
                                                <label class="form-check-label fw-bold" for="{{ method_key }}">
                                                    <i class="{{ method_config.icon }} me-2" style="color: {{ method_config.color }}"></i>
                                                    {{ method_config.name }}
                                                </label>
                                            </div>
                                            
                                            <div id="{{ method_key }}_fields" class="payment-fields" 
                                                 style="display: {% if current_method %}block{% else %}none{% endif %}">
                                                {% for field in method_config.fields %}
                                                <div class="mb-2">
                                                    <label class="form-label small">
                                                        {% if field == 'phone_number' %}Numéro de Téléphone
                                                        {% elif field == 'account_name' %}Nom du Compte
                                                        {% elif field == 'momo_name' %}Nom MoMo
                                                        {% else %}{{ field|title }}
                                                        {% endif %}
                                                    </label>
                                                    <input type="text" class="form-control form-control-sm" 
                                                           name="{{ method_key }}_{{ field }}"
                                                           value="{{ current_method.details[field] if current_method and current_method.details[field] else '' }}"
                                                           placeholder="{% if field == 'phone_number' %}+221 77 123 45 67{% elif field == 'account_name' %}Nom complet{% elif field == 'momo_name' %}Nom MoMo{% endif %}">
                                                </div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                        
                        <!-- Bank Transfer -->
                        {% if available_methods.bank_transfer %}
                        <div class="payment-category mb-4">
                            <h6 class="text-info mb-3">
                                <i class="fas fa-university me-2"></i>Virement Bancaire
                            </h6>
                            {% for method_key, method_config in available_methods.bank_transfer.items() %}
                            {% set current_method = current_methods|selectattr('type', 'equalto', method_key)|first %}
                            <div class="card border-info">
                                <div class="card-body">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input payment-method-toggle" type="checkbox" 
                                               name="payment_methods" value="{{ method_key }}" 
                                               id="{{ method_key }}" 
                                               {% if current_method %}checked{% endif %}
                                               data-target="#{{ method_key }}_fields">
                                        <label class="form-check-label fw-bold" for="{{ method_key }}">
                                            <i class="{{ method_config.icon }} me-2" style="color: {{ method_config.color }}"></i>
                                            {{ method_config.name }}
                                        </label>
                                    </div>
                                    
                                    <div id="{{ method_key }}_fields" class="payment-fields" 
                                         style="display: {% if current_method %}block{% else %}none{% endif %}">
                                        <div class="row">
                                            {% for field in method_config.fields %}
                                            <div class="col-md-6 mb-2">
                                                <label class="form-label small">
                                                    {% if field == 'bank_name' %}Nom de la Banque
                                                    {% elif field == 'account_number' %}Numéro de Compte
                                                    {% elif field == 'account_name' %}Nom du Titulaire
                                                    {% elif field == 'swift_code' %}Code SWIFT/BIC
                                                    {% else %}{{ field|title }}
                                                    {% endif %}
                                                </label>
                                                <input type="text" class="form-control form-control-sm" 
                                                       name="{{ method_key }}_{{ field }}"
                                                       value="{{ current_method.details[field] if current_method and current_method.details[field] else '' }}"
                                                       placeholder="{% if field == 'swift_code' %}Optionnel{% endif %}">
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}
                        
                        <!-- International Payment Methods (Premium+) -->
                        {% if available_methods.international %}
                        <div class="payment-category mb-4">
                            <h6 class="text-warning mb-3">
                                <i class="fas fa-globe me-2"></i>Paiements Internationaux 
                                <span class="badge bg-warning text-dark">Premium+</span>
                            </h6>
                            <div class="row">
                                {% for method_key, method_config in available_methods.international.items() %}
                                {% set current_method = current_methods|selectattr('type', 'equalto', method_key)|first %}
                                <div class="col-md-6 mb-3">
                                    <div class="card border-warning">
                                        <div class="card-body">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input payment-method-toggle" type="checkbox" 
                                                       name="payment_methods" value="{{ method_key }}" 
                                                       id="{{ method_key }}" 
                                                       {% if current_method %}checked{% endif %}
                                                       data-target="#{{ method_key }}_fields">
                                                <label class="form-check-label fw-bold" for="{{ method_key }}">
                                                    <i class="{{ method_config.icon }} me-2" style="color: {{ method_config.color }}"></i>
                                                    {{ method_config.name }}
                                                </label>
                                            </div>
                                            
                                            <div id="{{ method_key }}_fields" class="payment-fields" 
                                                 style="display: {% if current_method %}block{% else %}none{% endif %}">
                                                {% for field in method_config.fields %}
                                                <div class="mb-2">
                                                    <label class="form-label small">
                                                        {% if field == 'paypal_email' %}Email PayPal
                                                        {% elif field == 'stripe_account_id' %}ID Compte Stripe
                                                        {% else %}{{ field|title }}
                                                        {% endif %}
                                                    </label>
                                                    <input type="text" class="form-control form-control-sm" 
                                                           name="{{ method_key }}_{{ field }}"
                                                           value="{{ current_method.details[field] if current_method and current_method.details[field] else '' }}"
                                                           placeholder="{% if field == 'paypal_email' %}<EMAIL>{% elif field == 'stripe_account_id' %}acct_xxxxx{% endif %}">
                                                </div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                        
                        <!-- Other Payment Methods -->
                        {% if available_methods.other %}
                        <div class="payment-category mb-4">
                            <h6 class="text-secondary mb-3">
                                <i class="fas fa-ellipsis-h me-2"></i>Autres Méthodes
                            </h6>
                            {% for method_key, method_config in available_methods.other.items() %}
                            {% set current_method = current_methods|selectattr('type', 'equalto', method_key)|first %}
                            <div class="card border-secondary">
                                <div class="card-body">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input payment-method-toggle" type="checkbox" 
                                               name="payment_methods" value="{{ method_key }}" 
                                               id="{{ method_key }}" 
                                               {% if current_method %}checked{% endif %}
                                               data-target="#{{ method_key }}_fields">
                                        <label class="form-check-label fw-bold" for="{{ method_key }}">
                                            <i class="{{ method_config.icon }} me-2" style="color: {{ method_config.color }}"></i>
                                            {{ method_config.name }}
                                        </label>
                                    </div>
                                    
                                    <div id="{{ method_key }}_fields" class="payment-fields" 
                                         style="display: {% if current_method %}block{% else %}none{% endif %}">
                                        {% for field in method_config.fields %}
                                        <div class="mb-2">
                                            <label class="form-label small">
                                                {% if field == 'method_name' %}Nom de la Méthode
                                                {% elif field == 'account_details' %}Détails du Compte
                                                {% elif field == 'instructions' %}Instructions pour le Client
                                                {% else %}{{ field|title }}
                                                {% endif %}
                                            </label>
                                            {% if field == 'instructions' %}
                                            <textarea class="form-control form-control-sm" rows="3"
                                                      name="{{ method_key }}_{{ field }}"
                                                      placeholder="Instructions détaillées pour le paiement...">{{ current_method.details[field] if current_method and current_method.details[field] else '' }}</textarea>
                                            {% else %}
                                            <input type="text" class="form-control form-control-sm" 
                                                   name="{{ method_key }}_{{ field }}"
                                                   value="{{ current_method.details[field] if current_method and current_method.details[field] else '' }}"
                                                   placeholder="{% if field == 'method_name' %}Ex: Western Union{% elif field == 'account_details' %}Détails nécessaires{% endif %}">
                                            {% endif %}
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}
                        
                        {% if not available_methods.mobile_money and not available_methods.bank_transfer and not available_methods.international and not available_methods.other %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Aucune méthode de paiement disponible pour votre pays ou niveau d'abonnement.
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('shop.payment_methods') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Enregistrer les Méthodes de Paiement
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Payment method toggle functionality
    document.querySelectorAll('.payment-method-toggle').forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            const targetId = this.getAttribute('data-target');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                if (this.checked) {
                    targetElement.style.display = 'block';
                    // Focus on first input field
                    const firstInput = targetElement.querySelector('input, textarea');
                    if (firstInput) {
                        setTimeout(() => firstInput.focus(), 100);
                    }
                } else {
                    targetElement.style.display = 'none';
                    // Clear all input values when unchecked
                    targetElement.querySelectorAll('input, textarea').forEach(function(input) {
                        input.value = '';
                    });
                }
            }
        });
    });
});
</script>
{% endblock %}
