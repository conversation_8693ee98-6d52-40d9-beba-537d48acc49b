{% extends "base.html" %}

{% block title %}Parcourir les Boutiques - Afroly.org{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-5 fw-bold">Parcourir les Boutiques</h1>
            <p class="lead text-muted">Découvrez des boutiques africaines authentiques</p>
        </div>
        <div class="col-md-4">
            <form method="GET" class="d-flex">
                <input type="hidden" name="search" value="{{ search }}">
                <input type="hidden" name="country" value="{{ country }}">
                <select name="sort" class="form-select" onchange="this.form.submit()">
                    <option value="newest" {% if sort_by == 'newest' %}selected{% endif %}>Plus récentes</option>
                    <option value="oldest" {% if sort_by == 'oldest' %}selected{% endif %}>Plus anciennes</option>
                    <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Nom A-Z</option>
                    <option value="country" {% if sort_by == 'country' %}selected{% endif %}>Par Pays</option>
                </select>
            </form>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="row mb-4">
        <div class="col-md-6">
            <form method="GET" class="d-flex gap-3">
                <div class="flex-grow-1">
                    <input type="text" class="form-control" name="search" placeholder="Rechercher une boutique..."
                           value="{{ search }}">
                </div>
                <input type="hidden" name="country" value="{{ country }}">
                <input type="hidden" name="sort" value="{{ sort_by }}">
                <button type="submit" class="btn btn-outline-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
        <div class="col-md-6">
            <form method="GET" class="d-flex align-items-center">
                <input type="hidden" name="search" value="{{ search }}">
                <input type="hidden" name="sort" value="{{ sort_by }}">
                <label for="country" class="form-label me-2 mb-0">Filtrer par pays :</label>
                <select name="country" id="country" class="form-select" onchange="this.form.submit()">
                    <option value="">Tous les pays africains</option>
                    {% for country_option in countries_list %}
                    <option value="{{ country_option }}" {% if country == country_option %}selected{% endif %}>
                        {{ country_option }}
                    </option>
                    {% endfor %}
                </select>
            </form>
        </div>
    </div>

    <!-- Stats -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                {{ shops.total }} boutiques trouvées
                {% if search or country %}
                <span class="ms-2">
                    {% if search %}
                    • Recherche : "{{ search }}"
                    {% endif %}
                    {% if country %}
                    • Pays : {{ country }}
                    {% endif %}
                    <a href="{{ url_for('shop.browse') }}" class="btn btn-sm btn-outline-primary ms-2">
                        <i class="fas fa-times me-1"></i>Effacer les filtres
                    </a>
                </span>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Shops Grid -->
    {% if shops.items %}
    <div class="row">
        {% for shop in shops.items %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 shop-card">
                <!-- Shop Banner -->
                {% if shop.banner %}
                <img src="/static/uploads/shops/{{ shop.banner }}" class="card-img-top" alt="{{ shop.name }}"
                     style="height: 150px; object-fit: cover;">
                {% else %}
                <div class="card-img-top bg-primary d-flex align-items-center justify-content-center" style="height: 150px;">
                    <i class="fas fa-store fa-3x text-white"></i>
                </div>
                {% endif %}

                <div class="card-body d-flex flex-column">
                    <!-- Shop Info -->
                    <div class="d-flex align-items-center mb-3">
                        {% if shop.logo %}
                        <img src="/static/uploads/shops/{{ shop.logo }}" alt="{{ shop.name }}"
                             class="rounded-circle me-3" style="width: 50px; height: 50px; object-fit: cover;">
                        {% else %}
                        <div class="bg-secondary rounded-circle me-3 d-flex align-items-center justify-content-center"
                             style="width: 50px; height: 50px;">
                            <i class="fas fa-store text-white"></i>
                        </div>
                        {% endif %}
                        <div class="flex-grow-1">
                            <h5 class="card-title mb-1">{{ shop.name }}</h5>
                            <div class="rating">
                                {% for i in range(5) %}
                                    {% if i < shop.get_average_rating() %}
                                    <i class="fas fa-star text-warning"></i>
                                    {% else %}
                                    <i class="far fa-star text-warning"></i>
                                    {% endif %}
                                {% endfor %}
                                <small class="text-muted ms-1">({{ shop.reviews|length }})</small>
                            </div>
                        </div>
                    </div>

                    <!-- Shop Description -->
                    <p class="card-text text-muted">{{ shop.description[:120] }}{% if shop.description|length > 120 %}...{% endif %}</p>

                    <!-- Shop Stats -->
                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <div class="border-end">
                                <h6 class="text-primary mb-0">{{ shop.get_active_products_count() }}</h6>
                                <small class="text-muted">Produits</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h6 class="text-success mb-0">{{ shop.orders|length }}</h6>
                            <small class="text-muted">Commandes</small>
                        </div>
                    </div>

                    <!-- Shop Actions -->
                    <div class="mt-auto">
                        <div class="d-flex gap-2">
                            <a href="{{ url_for('shop.view', slug=shop.slug) }}" class="btn btn-primary flex-grow-1">
                                <i class="fas fa-eye me-2"></i>Visiter
                            </a>
                            <button class="btn btn-outline-primary" data-bs-toggle="tooltip" title="Ajouter aux favoris">
                                <i class="fas fa-heart"></i>
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" title="Partager">
                                    <i class="fas fa-share-alt"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="shareOnFacebook('{{ url_for('shop.view', slug=shop.slug, _external=True) }}', '{{ shop.name }}')">
                                            <i class="fab fa-facebook text-primary me-2"></i>Facebook
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="shareOnWhatsApp('{{ url_for('shop.view', slug=shop.slug, _external=True) }}', '{{ shop.name }}')">
                                            <i class="fab fa-whatsapp text-success me-2"></i>WhatsApp
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="shareOnTwitter('{{ url_for('shop.view', slug=shop.slug, _external=True) }}', '{{ shop.name }}')">
                                            <i class="fab fa-twitter text-info me-2"></i>Twitter
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="shareOnTelegram('{{ url_for('shop.view', slug=shop.slug, _external=True) }}', '{{ shop.name }}')">
                                            <i class="fab fa-telegram text-primary me-2"></i>Telegram
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="copyToClipboard('{{ url_for('shop.view', slug=shop.slug, _external=True) }}')">
                                            <i class="fas fa-copy text-secondary me-2"></i>Copier le lien
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Shop Tags -->
                    {% if shop.owner.tier.value != 'free' %}
                    <div class="mt-2">
                        {% if shop.owner.tier.value == 'premium' %}
                        <span class="badge bg-info">Premium</span>
                        {% elif shop.owner.tier.value == 'gold' %}
                        <span class="badge bg-warning">Gold</span>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>

                <!-- Shop Footer -->
                <div class="card-footer bg-transparent">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            Membre depuis {{ shop.created_at.strftime('%B %Y') }}
                        </small>
                        <small class="text-muted">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            {{ shop.country }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if shops.pages > 1 %}
    <nav aria-label="Navigation des pages" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if shops.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('shop.browse', sort=sort_by, search=search, country=country, page=shops.prev_num) }}">
                    <i class="fas fa-chevron-left me-1"></i>Précédent
                </a>
            </li>
            {% endif %}

            {% for page_num in shops.iter_pages() %}
                {% if page_num %}
                    {% if page_num != shops.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('shop.browse', sort=sort_by, search=search, country=country, page=page_num) }}">
                            {{ page_num }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}

            {% if shops.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('shop.browse', sort=sort_by, search=search, country=country, page=shops.next_num) }}">
                    Suivant<i class="fas fa-chevron-right ms-1"></i>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    {% else %}
    <!-- No Shops Found -->
    <div class="text-center py-5">
        <i class="fas fa-store-slash fa-4x text-muted mb-4"></i>
        <h3>Aucune boutique trouvée</h3>
        <p class="text-muted mb-4">Il n'y a pas encore de boutiques actives sur la plateforme.</p>
        {% if current_user.is_authenticated and current_user.role.value == 'vendor' %}
        <a href="{{ url_for('shop.create') }}" class="btn btn-primary btn-lg">
            <i class="fas fa-plus me-2"></i>Créer Ma Boutique
        </a>
        {% else %}
        <a href="{{ url_for('auth.register') }}" class="btn btn-primary btn-lg">
            <i class="fas fa-user-plus me-2"></i>Devenir Vendeur
        </a>
        {% endif %}
    </div>
    {% endif %}

    <!-- Call to Action -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body text-center py-5">
                    <h3 class="card-title">Vous êtes vendeur ?</h3>
                    <p class="card-text lead">Rejoignez afroly.org et créez votre boutique en ligne dès aujourd'hui !</p>
                    <div class="d-flex gap-3 justify-content-center">
                        {% if current_user.is_authenticated and current_user.role.value == 'vendor' %}
                        <a href="{{ url_for('shop.create') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-plus me-2"></i>Créer Ma Boutique
                        </a>
                        <a href="{{ url_for('shop.vendor_dashboard') }}" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-chart-bar me-2"></i>Mon Tableau de Bord
                        </a>
                        {% else %}
                        <a href="{{ url_for('auth.register') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-user-plus me-2"></i>Devenir Vendeur
                        </a>
                        <a href="{{ url_for('main.about') }}" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-info-circle me-2"></i>En Savoir Plus
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
.shop-card {
    transition: all 0.3s ease;
}

.shop-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.rating i {
    font-size: 0.8rem;
}
</style>
{% endblock %}
