{% extends "base.html" %}

{% block title %}G<PERSON>rer les Commandes - {{ shop.name }} - AfroMall{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-6 fw-bold">
                <i class="fas fa-shopping-cart me-3"></i>Gérer les Commandes
            </h1>
            <p class="lead text-muted">{{ shop.name }}</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="d-flex gap-2 justify-content-end">
                <a href="{{ url_for('shop.create_manual_order', shop_id=shop.id) }}" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>Créer une Commande
                </a>
                <button class="btn btn-outline-primary" onclick="refreshOrders()">
                    <i class="fas fa-sync-alt me-2"></i>Actualiser
                </button>
            </div>
        </div>
    </div>

    <!-- Shop Info Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            {% if shop.logo %}
                            <img src="/static/uploads/shops/{{ shop.logo }}" alt="{{ shop.name }}"
                                 class="rounded-circle" style="width: 60px; height: 60px; object-fit: cover;">
                            {% else %}
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center"
                                 style="width: 60px; height: 60px;">
                                <i class="fas fa-store text-white"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <h5 class="mb-1">{{ shop.name }}</h5>
                            <p class="text-muted mb-0">{{ shop.description[:100] }}{% if shop.description|length > 100 %}...{% endif %}</p>
                        </div>
                        <div class="col-md-4">
                            <div class="row text-center">
                                <div class="col-3">
                                    <h6 class="text-primary mb-0">{{ orders.total }}</h6>
                                    <small class="text-muted">Total</small>
                                </div>
                                <div class="col-3">
                                    <h6 class="text-warning mb-0">{{ pending_count }}</h6>
                                    <small class="text-muted">En Attente</small>
                                </div>
                                <div class="col-3">
                                    <h6 class="text-info mb-0">{{ processing_count }}</h6>
                                    <small class="text-muted">En Cours</small>
                                </div>
                                <div class="col-3">
                                    <h6 class="text-success mb-0">{{ completed_count }}</h6>
                                    <small class="text-muted">Terminées</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-md-8">
            <form method="GET" class="d-flex gap-3">
                <div class="flex-grow-1">
                    <input type="text" class="form-control" name="search" placeholder="Rechercher par numéro de commande ou client..."
                           value="{{ request.args.get('search', '') }}">
                </div>
                <select name="status" class="form-select" style="width: auto;">
                    <option value="">Tous les statuts</option>
                    <option value="pending" {% if request.args.get('status') == 'pending' %}selected{% endif %}>En Attente</option>
                    <option value="confirmed" {% if request.args.get('status') == 'confirmed' %}selected{% endif %}>Confirmée</option>
                    <option value="processing" {% if request.args.get('status') == 'processing' %}selected{% endif %}>En Cours</option>
                    <option value="shipped" {% if request.args.get('status') == 'shipped' %}selected{% endif %}>Expédiée</option>
                    <option value="delivered" {% if request.args.get('status') == 'delivered' %}selected{% endif %}>Livrée</option>
                    <option value="cancelled" {% if request.args.get('status') == 'cancelled' %}selected{% endif %}>Annulée</option>
                </select>
                <button type="submit" class="btn btn-outline-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
        <div class="col-md-4">
            <form method="GET" class="d-flex align-items-center">
                <input type="hidden" name="search" value="{{ request.args.get('search', '') }}">
                <input type="hidden" name="status" value="{{ request.args.get('status', '') }}">
                <label for="sort" class="form-label me-2 mb-0">Trier :</label>
                <select name="sort" id="sort" class="form-select" onchange="this.form.submit()">
                    <option value="newest" {% if sort_by == 'newest' %}selected{% endif %}>Plus récentes</option>
                    <option value="oldest" {% if sort_by == 'oldest' %}selected{% endif %}>Plus anciennes</option>
                    <option value="amount_high" {% if sort_by == 'amount_high' %}selected{% endif %}>Montant décroissant</option>
                    <option value="amount_low" {% if sort_by == 'amount_low' %}selected{% endif %}>Montant croissant</option>
                </select>
            </form>
        </div>
    </div>

    <!-- Orders List -->
    {% if orders.items %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Commandes ({{ orders.total }})</h5>
                </div>
                <div class="card-body p-0">
                    {% for order in orders.items %}
                    <div class="border-bottom p-3 {% if not loop.last %}{% endif %}">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <h6 class="mb-1">Commande #{{ order.id }}</h6>
                                <small class="text-muted">{{ order.created_at.strftime('%d/%m/%Y %H:%M') }}</small>
                                <br>
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i>
                                    {% if order.customer %}
                                        {{ order.customer.get_full_name() }}
                                    {% else %}
                                        Commande manuelle
                                    {% endif %}
                                </small>
                            </div>

                            <div class="col-md-2">
                                <div class="order-items">
                                    <small class="text-muted">{{ order.items|length }} article(s)</small>
                                    {% for item in order.items[:2] %}
                                    <div class="d-flex align-items-center mt-1">
                                        <img src="{{ item.product.get_main_image() }}" alt="{{ item.product.name }}"
                                             class="rounded me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                        <small>{{ item.product.name[:20] }}{% if item.product.name|length > 20 %}...{% endif %}</small>
                                    </div>
                                    {% endfor %}
                                    {% if order.items|length > 2 %}
                                    <small class="text-muted">... et {{ order.items|length - 2 }} autres</small>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="col-md-2">
                                <h6 class="text-primary mb-0">{{ "%.0f"|format(order.total) }} FCFA</h6>
                                <small class="text-muted">
                                    {% if order.payment_status == 'paid' %}
                                    <i class="fas fa-check-circle text-success"></i> Payée
                                    {% elif order.payment_status == 'pending' %}
                                    <i class="fas fa-clock text-warning"></i> En Attente
                                    {% else %}
                                    <i class="fas fa-times-circle text-danger"></i> Non Payée
                                    {% endif %}
                                </small>
                            </div>

                            <div class="col-md-2">
                                {% if order.status.value == 'pending' %}
                                <span class="badge bg-warning">En Attente</span>
                                {% elif order.status.value == 'processing' %}
                                <span class="badge bg-primary">En Cours</span>
                                {% elif order.status.value == 'shipped' %}
                                <span class="badge bg-secondary">Expédiée</span>
                                {% elif order.status.value == 'delivered' %}
                                <span class="badge bg-success">Livrée</span>
                                {% elif order.status.value == 'cancelled' %}
                                <span class="badge bg-danger">Annulée</span>
                                {% elif order.status.value == 'refunded' %}
                                <span class="badge bg-info">Remboursée</span>
                                {% endif %}
                            </div>

                            <div class="col-md-3">
                                <div class="btn-group w-100" role="group">
                                    <button class="btn btn-sm btn-outline-primary"
                                            onclick="viewOrder({{ order.id }})"
                                            data-bs-toggle="tooltip" title="Voir les détails">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <a href="{{ url_for('shop.view_order', shop_id=shop.id, order_id=order.id) }}"
                                       class="btn btn-sm btn-outline-primary"
                                       data-bs-toggle="tooltip" title="Page complète">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>

                                    {% if order.status.value in ['pending', 'processing'] %}
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-success dropdown-toggle"
                                                data-bs-toggle="dropdown">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            {% if order.status.value == 'pending' %}
                                            <li><a class="dropdown-item" href="#" onclick="updateOrderStatus({{ order.id }}, 'processing')">
                                                <i class="fas fa-cog me-2"></i>Mettre en Cours
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="updateOrderStatus({{ order.id }}, 'cancelled')">
                                                <i class="fas fa-times me-2"></i>Annuler
                                            </a></li>
                                            {% elif order.status.value == 'processing' %}
                                            <li><a class="dropdown-item" href="#" onclick="updateOrderStatus({{ order.id }}, 'shipped')">
                                                <i class="fas fa-shipping-fast me-2"></i>Expédier
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="updateOrderStatus({{ order.id }}, 'cancelled')">
                                                <i class="fas fa-times me-2"></i>Annuler
                                            </a></li>
                                            {% endif %}
                                        </ul>
                                    </div>
                                    {% elif order.status.value == 'shipped' %}
                                    <button class="btn btn-sm btn-outline-success"
                                            onclick="updateOrderStatus({{ order.id }}, 'delivered')"
                                            data-bs-toggle="tooltip" title="Marquer comme livrée">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    {% endif %}

                                    {% if order.customer and order.customer.phone %}
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-success dropdown-toggle"
                                                data-bs-toggle="dropdown"
                                                data-bs-toggle="tooltip" title="Contacter le client">
                                            <i class="fas fa-phone"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="contactWhatsApp('{{ order.customer.phone }}')">
                                                <i class="fab fa-whatsapp me-2 text-success"></i>WhatsApp
                                            </a></li>
                                            <li><a class="dropdown-item" href="tel:{{ order.customer.phone }}">
                                                <i class="fas fa-phone me-2 text-primary"></i>Appeler
                                            </a></li>
                                        </ul>
                                    </div>
                                    {% else %}
                                    <button class="btn btn-sm btn-outline-secondary" disabled
                                            data-bs-toggle="tooltip" title="Pas de numéro disponible">
                                        <i class="fas fa-phone"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if orders.pages > 1 %}
    <nav aria-label="Navigation des commandes" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if orders.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('shop.manage_orders', shop_id=shop.id,
                    search=request.args.get('search', ''), status=request.args.get('status', ''),
                    sort=sort_by, page=orders.prev_num) }}">
                    <i class="fas fa-chevron-left me-1"></i>Précédent
                </a>
            </li>
            {% endif %}

            {% for page_num in orders.iter_pages() %}
                {% if page_num %}
                    {% if page_num != orders.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('shop.manage_orders', shop_id=shop.id,
                            search=request.args.get('search', ''), status=request.args.get('status', ''),
                            sort=sort_by, page=page_num) }}">
                            {{ page_num }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}

            {% if orders.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('shop.manage_orders', shop_id=shop.id,
                    search=request.args.get('search', ''), status=request.args.get('status', ''),
                    sort=sort_by, page=orders.next_num) }}">
                    Suivant<i class="fas fa-chevron-right ms-1"></i>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    {% else %}
    <!-- No Orders -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-shopping-cart fa-4x text-muted mb-4"></i>
                    <h4>Aucune commande trouvée</h4>
                    {% if request.args.get('search') or request.args.get('status') %}
                    <p class="text-muted mb-4">Aucune commande ne correspond à vos critères de recherche.</p>
                    <a href="{{ url_for('shop.manage_orders', shop_id=shop.id) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>Effacer les Filtres
                    </a>
                    {% else %}
                    <p class="text-muted mb-4">Vous n'avez pas encore reçu de commandes pour cette boutique.</p>
                    <a href="{{ url_for('shop.manage_products', shop_id=shop.id) }}" class="btn btn-primary">
                        <i class="fas fa-box me-2"></i>Gérer les Produits
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <h6 class="card-title">Actions Rapides</h6>
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="{{ url_for('shop.vendor_dashboard') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Retour au Tableau de Bord
                        </a>
                        <a href="{{ url_for('shop.manage_products', shop_id=shop.id) }}" class="btn btn-outline-primary">
                            <i class="fas fa-box me-2"></i>Gérer les Produits
                        </a>
                        <a href="{{ url_for('shop.edit', shop_id=shop.id) }}" class="btn btn-outline-info">
                            <i class="fas fa-edit me-2"></i>Modifier la Boutique
                        </a>
                        {% if shop.status.value == 'active' %}
                        <a href="{{ url_for('shop.view', slug=shop.slug) }}" class="btn btn-outline-success" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>Voir la Boutique
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Order Details Modal -->
<div class="modal fade" id="orderModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails de la Commande</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="orderDetails">
                <!-- Order details will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function viewOrder(orderId) {
    // Load order details
    fetch(`/shop/order/details/${orderId}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('orderDetails').innerHTML = html;
            const modal = new bootstrap.Modal(document.getElementById('orderModal'));
            modal.show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur lors du chargement des détails de la commande');
        });
}



function contactWhatsApp(phone) {
    // Clean phone number and format for WhatsApp
    let cleanPhone = phone.replace(/[^\d+]/g, '');
    if (!cleanPhone.startsWith('+')) {
        cleanPhone = '+' + cleanPhone;
    }

    const message = encodeURIComponent('Bonjour, je vous contacte concernant votre commande sur Afroly.org.');
    window.open(`https://wa.me/${cleanPhone.replace('+', '')}?text=${message}`, '_blank');
}

function contactCustomer(phone) {
    // For backward compatibility - redirect to WhatsApp
    contactWhatsApp(phone);
}

function updateOrderStatus(orderId, newStatus) {
    if (confirm('Êtes-vous sûr de vouloir changer le statut de cette commande ?')) {
        fetch(`/shop/order/${orderId}/status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: newStatus
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur lors de la mise à jour du statut');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur lors de la mise à jour du statut');
        });
    }
}

function printOrder() {
    window.print();
}

function refreshOrders() {
    location.reload();
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
