{% extends "base.html" %}

{% block title %}{{ category.name }} - Afroly.org{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Category Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Accueil</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('main.categories') }}">Catégories</a></li>
                    <li class="breadcrumb-item active">{{ category.name }}</li>
                </ol>
            </nav>

            <div class="d-flex align-items-center mb-3">
                <i class="{{ category.icon or 'fas fa-tag' }} fa-3x text-primary me-3"></i>
                <div>
                    <h1 class="mb-0">{{ category.name }}</h1>
                    {% if category.description %}
                    <p class="text-muted mb-0">{{ category.description }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Sorting and Filters -->
    <div class="row mb-4">
        <div class="col-md-6">
            <p class="text-muted">{{ products.total }} produits trouvés</p>
        </div>
        <div class="col-md-6">
            <form method="GET" class="d-flex justify-content-end">
                <select name="sort" class="form-select w-auto" onchange="this.form.submit()">
                    <option value="newest" {% if sort_by == 'newest' %}selected{% endif %}>Plus récent</option>
                    <option value="price_low" {% if sort_by == 'price_low' %}selected{% endif %}>Prix croissant</option>
                    <option value="price_high" {% if sort_by == 'price_high' %}selected{% endif %}>Prix décroissant</option>
                    <option value="rating" {% if sort_by == 'rating' %}selected{% endif %}>Mieux notés</option>
                </select>
            </form>
        </div>
    </div>

    <!-- Products Grid -->
    {% if products.items %}
    <div class="row">
        {% for product in products.items %}
        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
            <div class="card h-100 product-card">
                <div class="position-relative">
                    <img src="{{ product.get_main_image() }}" class="card-img-top" alt="{{ product.name }}"
                         style="height: 200px; object-fit: cover;">
                    {% if product.is_on_sale() %}
                    <span class="badge bg-danger position-absolute top-0 end-0 m-2">
                        -{{ product.get_discount_percentage() }}%
                    </span>
                    {% endif %}
                    {% if not product.is_in_stock() %}
                    <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
                         style="background: rgba(0,0,0,0.7);">
                        <span class="badge bg-warning">Rupture de Stock</span>
                    </div>
                    {% endif %}
                </div>
                <div class="card-body d-flex flex-column">
                    <h6 class="card-title">{{ product.name }}</h6>
                    <p class="card-text text-muted small">{{ product.short_description[:100] }}...</p>

                    <!-- Shop Info -->
                    <div class="d-flex align-items-center mb-2">
                        {% if product.shop.logo %}
                        <img src="/static/uploads/shops/{{ product.shop.logo }}" alt="{{ product.shop.name }}"
                             class="rounded-circle me-2" style="width: 20px; height: 20px; object-fit: cover;">
                        {% else %}
                        <i class="fas fa-store text-muted me-2"></i>
                        {% endif %}
                        <small class="text-muted">{{ product.shop.name }}</small>
                    </div>

                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                {% if product.is_on_sale() %}
                                <span class="text-decoration-line-through text-muted">{{ "%.0f"|format(product.price) }} {{ product.shop.get_currency_symbol() }}</span>
                                <span class="fw-bold text-primary">{{ "%.0f"|format(product.sale_price) }} {{ product.shop.get_currency_symbol() }}</span>
                                {% else %}
                                <span class="fw-bold text-primary">{{ "%.0f"|format(product.price) }} {{ product.shop.get_currency_symbol() }}</span>
                                {% endif %}
                            </div>
                            <div class="rating">
                                {% for i in range(5) %}
                                    {% if i < product.get_average_rating() %}
                                    <i class="fas fa-star text-warning"></i>
                                    {% else %}
                                    <i class="far fa-star text-warning"></i>
                                    {% endif %}
                                {% endfor %}
                                <small class="text-muted">({{ product.get_review_count() }})</small>
                            </div>
                        </div>
                        <a href="{{ url_for('product.view', slug=product.slug) }}" class="btn btn-primary btn-sm w-100">
                            Voir Produit
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if products.pages > 1 %}
    <nav aria-label="Navigation des pages" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if products.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('main.category_products', slug=category.slug, sort=sort_by, page=products.prev_num) }}">
                    Précédent
                </a>
            </li>
            {% endif %}

            {% for page_num in products.iter_pages() %}
                {% if page_num %}
                    {% if page_num != products.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('main.category_products', slug=category.slug, sort=sort_by, page=page_num) }}">
                            {{ page_num }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}

            {% if products.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('main.category_products', slug=category.slug, sort=sort_by, page=products.next_num) }}">
                    Suivant
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    {% else %}
    <div class="text-center py-5">
        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
        <h4>Aucun produit dans cette catégorie</h4>
        <p class="text-muted">Les produits seront bientôt disponibles dans cette catégorie.</p>
        <a href="{{ url_for('main.categories') }}" class="btn btn-primary">
            Voir Autres Catégories
        </a>
    </div>
    {% endif %}

    <!-- Subcategories -->
    {% if category.children %}
    <div class="mt-5">
        <h3>Sous-catégories</h3>
        <div class="row">
            {% for subcategory in category.children %}
            <div class="col-md-3 mb-3">
                <a href="{{ url_for('main.category_products', slug=subcategory.slug) }}" class="text-decoration-none">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="{{ subcategory.icon or 'fas fa-tag' }} fa-2x text-primary mb-2"></i>
                            <h6 class="card-title">{{ subcategory.name }}</h6>
                        </div>
                    </div>
                </a>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
