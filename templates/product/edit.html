{% extends "base.html" %}

{% block title %}Modifier {{ product.name }} - AfroMall{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow border-0">
                <div class="card-header bg-warning text-dark">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">
                                <i class="fas fa-edit me-2"></i>Modifier le Produit
                            </h3>
                            <p class="mb-0 mt-2">{{ product.name }}</p>
                        </div>
                        <div>
                            {% if product.status.value == 'active' %}
                            <span class="badge bg-success fs-6">Actif</span>
                            {% elif product.status.value == 'pending' %}
                            <span class="badge bg-warning fs-6">En Attente</span>
                            {% elif product.status.value == 'rejected' %}
                            <span class="badge bg-danger fs-6">Rejeté</span>
                            {% else %}
                            <span class="badge bg-secondary fs-6">Inactif</span>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="card-body p-4">
                    <!-- Current Images Display -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-warning border-bottom pb-2 mb-3">
                                <i class="fas fa-images me-2"></i>Images Actuelles
                            </h5>
                        </div>

                        {% if product.images %}
                        <div class="col-12">
                            <div class="row">
                                {% for image in product.images %}
                                <div class="col-md-3 mb-3">
                                    <div class="position-relative">
                                        <img src="{{ image }}" alt="Image {{ loop.index }}"
                                             class="img-fluid rounded border" style="height: 150px; width: 100%; object-fit: cover;">
                                        <div class="form-check position-absolute top-0 end-0 m-2">
                                            <input class="form-check-input" type="checkbox" name="remove_images"
                                                   value="{{ image }}" id="remove_{{ loop.index }}">
                                            <label class="form-check-label bg-danger text-white px-2 py-1 rounded"
                                                   for="remove_{{ loop.index }}">
                                                <i class="fas fa-trash"></i>
                                            </label>
                                        </div>
                                        {% if loop.first %}
                                        <span class="badge bg-primary position-absolute bottom-0 start-0 m-2">
                                            Image Principale
                                        </span>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% else %}
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>Aucune image actuellement. Ajoutez des images ci-dessous.
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <form method="POST" enctype="multipart/form-data">
                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-warning border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle me-2"></i>Informations de Base
                                </h5>
                            </div>

                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Nom du Produit *</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="name" name="name" required
                                               value="{{ request.form.get('name', product.name) }}">
                                        {% if current_user.tier.value in ['premium', 'gold'] %}
                                        <button type="button" class="btn btn-outline-primary" id="aiRewriteTitle" title="Réécrire avec l'IA">
                                            <i class="fas fa-magic"></i> IA
                                        </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="category_id" class="form-label">Catégorie *</label>
                                    <select class="form-select" id="category_id" name="category_id" required>
                                        <option value="">Choisir une catégorie</option>
                                        {% for category in categories %}
                                        <option value="{{ category.id }}"
                                                {% if request.form.get('category_id', product.category_id, type=int) == category.id %}selected{% endif %}>
                                            {{ category.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="short_description" class="form-label">Description Courte</label>
                                    <input type="text" class="form-control" id="short_description" name="short_description"
                                           value="{{ request.form.get('short_description', product.short_description or '') }}">
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description Détaillée *</label>
                                    <textarea class="form-control" id="description" name="description" rows="6" required>{{ request.form.get('description', product.description) }}</textarea>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="form-text">Minimum 20 caractères</div>
                                        {% if current_user.tier.value in ['premium', 'gold'] %}
                                        <button type="button" class="btn btn-outline-primary btn-sm" id="aiRewriteDescription" title="Réécrire avec l'IA">
                                            <i class="fas fa-magic me-1"></i>Améliorer avec l'IA
                                        </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pricing -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-warning border-bottom pb-2 mb-3">
                                    <i class="fas fa-coins me-2"></i>Prix et Promotion
                                </h5>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="price" class="form-label">Prix Normal *</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="price" name="price" step="1" min="0" required
                                               value="{{ request.form.get('price', product.price|int) }}">
                                        <span class="input-group-text">FCFA</span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="sale_price" class="form-label">Prix Promotionnel</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="sale_price" name="sale_price" step="1" min="0"
                                               value="{{ request.form.get('sale_price', product.sale_price|int if product.sale_price else '') }}">
                                        <span class="input-group-text">FCFA</span>
                                    </div>
                                    <div class="form-text">Doit être inférieur au prix normal</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="stock_quantity" class="form-label">Stock Disponible *</label>
                                    <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" min="0" required
                                           value="{{ request.form.get('stock_quantity', product.stock_quantity) }}">
                                </div>
                            </div>
                        </div>

                        <!-- Product Details -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-warning border-bottom pb-2 mb-3">
                                    <i class="fas fa-cogs me-2"></i>Détails du Produit
                                </h5>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="sku" class="form-label">SKU / Référence</label>
                                    <input type="text" class="form-control" id="sku" name="sku"
                                           value="{{ request.form.get('sku', product.sku or '') }}">
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="weight" class="form-label">Poids</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="weight" name="weight" step="0.01" min="0"
                                               value="{{ request.form.get('weight', product.weight or '') }}">
                                        <span class="input-group-text">kg</span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Type de Produit</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="digital" name="digital"
                                               {% if request.form.get('digital') or product.digital %}checked{% endif %}>
                                        <label class="form-check-label" for="digital">
                                            Produit numérique (téléchargeable)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Add New Images -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-warning border-bottom pb-2 mb-3">
                                    <i class="fas fa-upload me-2"></i>Ajouter de Nouvelles Images
                                </h5>
                                <p class="text-muted mb-3">Ajoutez jusqu'à 5 nouvelles images. Elles s'ajouteront aux images existantes.</p>
                            </div>

                            {% for i in range(5) %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="image-upload-box">
                                    <label for="image_{{ i }}" class="form-label">Nouvelle Image {{ i + 1 }}</label>
                                    <input type="file" class="form-control" id="image_{{ i }}" name="image_{{ i }}" accept="image/*">
                                    <div class="image-preview mt-2" id="preview_{{ i }}" style="display: none;">
                                        <img src="" alt="Aperçu" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- Product Stats -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-warning border-bottom pb-2 mb-3">
                                    <i class="fas fa-chart-bar me-2"></i>Statistiques du Produit
                                </h5>
                            </div>

                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h4 class="text-primary">{{ product.reviews|length }}</h4>
                                        <small class="text-muted">Avis</small>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h4 class="text-success">{{ "%.1f"|format(product.get_average_rating()) }}</h4>
                                        <small class="text-muted">Note Moyenne</small>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h4 class="text-info">{{ product.created_at.strftime('%d/%m/%Y') }}</h4>
                                        <small class="text-muted">Date de Création</small>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        {% if product.featured %}
                                        <h4 class="text-warning"><i class="fas fa-star"></i></h4>
                                        <small class="text-muted">Produit Vedette</small>
                                        {% else %}
                                        <h4 class="text-muted"><i class="far fa-star"></i></h4>
                                        <small class="text-muted">Produit Standard</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-warning border-bottom pb-2 mb-3">
                                    <i class="fas fa-bolt me-2"></i>Actions Rapides
                                </h5>
                            </div>

                            <div class="col-md-3">
                                {% if product.status.value == 'active' %}
                                <a href="{{ url_for('product.view', slug=product.slug) }}" class="btn btn-outline-primary w-100 mb-2" target="_blank">
                                    <i class="fas fa-external-link-alt me-2"></i>Voir le Produit
                                </a>
                                {% else %}
                                <button class="btn btn-outline-secondary w-100 mb-2" disabled>
                                    <i class="fas fa-eye-slash me-2"></i>Non Publique
                                </button>
                                {% endif %}
                            </div>

                            <div class="col-md-3">
                                <a href="{{ url_for('shop.manage_products', shop_id=product.shop_id) }}" class="btn btn-outline-info w-100 mb-2">
                                    <i class="fas fa-list me-2"></i>Tous les Produits
                                </a>
                            </div>

                            <div class="col-md-3">
                                <a href="{{ url_for('product.create', shop_id=product.shop_id) }}" class="btn btn-outline-success w-100 mb-2">
                                    <i class="fas fa-plus me-2"></i>Nouveau Produit
                                </a>
                            </div>

                            <div class="col-md-3">
                                <button class="btn btn-outline-danger w-100 mb-2" onclick="deleteProduct()">
                                    <i class="fas fa-trash me-2"></i>Supprimer
                                </button>
                            </div>
                        </div>

                        <!-- Submit -->
                        <div class="row">
                            <div class="col-12">
                                {% if product.status.value == 'rejected' %}
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Produit Rejeté :</strong> Votre produit a été rejeté.
                                    Après modification, il sera soumis à nouveau pour approbation.
                                </div>
                                {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Note :</strong> Après modification, votre produit sera soumis à nouveau pour approbation.
                                </div>
                                {% endif %}

                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('shop.manage_products', shop_id=product.shop_id) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Retour aux Produits
                                    </a>
                                    <button type="submit" class="btn btn-warning btn-lg">
                                        <i class="fas fa-save me-2"></i>Enregistrer les Modifications
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la Suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer ce produit ? Cette action est irréversible.</p>
                <p><strong>{{ product.name }}</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Supprimer Définitivement</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
.image-upload-box {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s ease;
}

.image-upload-box:hover {
    border-color: #ffc107;
    background-color: #fff3cd;
}

.form-control:focus {
    border-color: #ffc107;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

.card {
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}
</style>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Image preview functionality
    for (let i = 0; i < 5; i++) {
        const fileInput = document.getElementById(`image_${i}`);
        const preview = document.getElementById(`preview_${i}`);

        fileInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.style.display = 'block';
                    preview.querySelector('img').src = e.target.result;
                };
                reader.readAsDataURL(file);
            } else {
                preview.style.display = 'none';
            }
        });
    }

    // Character counter for description
    const descriptionInput = document.getElementById('description');
    const charCounter = document.createElement('div');
    charCounter.className = 'form-text text-end';
    charCounter.style.marginTop = '-15px';
    charCounter.style.marginBottom = '15px';
    descriptionInput.parentNode.appendChild(charCounter);

    function updateCharCounter() {
        const length = descriptionInput.value.length;
        charCounter.textContent = `${length} caractères`;
        charCounter.className = `form-text text-end ${length < 20 ? 'text-danger' : 'text-success'}`;
    }

    descriptionInput.addEventListener('input', updateCharCounter);
    updateCharCounter();

    // AI Rewrite functionality
    {% if current_user.tier.value in ['premium', 'gold'] %}
    const aiRewriteTitleBtn = document.getElementById('aiRewriteTitle');
    const aiRewriteDescBtn = document.getElementById('aiRewriteDescription');

    if (aiRewriteTitleBtn) {
        aiRewriteTitleBtn.addEventListener('click', function() {
            aiRewriteContent('title');
        });
    }

    if (aiRewriteDescBtn) {
        aiRewriteDescBtn.addEventListener('click', function() {
            aiRewriteContent('description');
        });
    }
    {% endif %}
});

{% if current_user.tier.value in ['premium', 'gold'] %}
function aiRewriteContent(type) {
    const button = type === 'title' ? document.getElementById('aiRewriteTitle') : document.getElementById('aiRewriteDescription');
    const originalText = button.innerHTML;

    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> IA...';
    button.disabled = true;

    fetch(`/product/{{ product.id }}/ai-rewrite`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            type: type
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (type === 'title' && data.data.title) {
                document.getElementById('name').value = data.data.title;
                showToast('Titre réécrit avec succès!', 'success');
            }
            if (type === 'description' && data.data.description) {
                document.getElementById('description').value = data.data.description;
                showToast('Description réécrite avec succès!', 'success');
            }
        } else {
            showToast(data.message || 'Erreur lors de la réécriture', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Erreur lors de la réécriture', 'error');
    })
    .finally(() => {
        // Restore button state
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function showToast(message, type) {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}
{% endif %}

function deleteProduct() {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

document.getElementById('confirmDelete').addEventListener('click', function() {
    // Send delete request
    fetch(`/product/{{ product.id }}/delete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Close modal and redirect
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
            modal.hide();
            window.location.href = "{{ url_for('shop.manage_products', shop_id=product.shop_id) }}";
        } else {
            alert('Erreur lors de la suppression du produit: ' + (data.message || 'Erreur inconnue'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Erreur lors de la suppression du produit');
    });
});
</script>
{% endblock %}
