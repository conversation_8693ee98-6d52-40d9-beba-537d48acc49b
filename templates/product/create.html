{% extends "base.html" %}

{% block title %}Ajouter un Produit - {{ shop.name }} - AfroMall{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow border-0">
                <div class="card-header bg-success text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">
                                <i class="fas fa-plus me-2"></i>Ajouter un Nouveau Produit
                            </h3>
                            <p class="mb-0 mt-2">{{ shop.name }}</p>
                        </div>
                        <div>
                            <span class="badge bg-light text-dark fs-6">{{ shop.get_active_products_count() }} produits actifs</span>
                        </div>
                    </div>
                </div>

                <div class="card-body p-4">
                    <!-- Tier Limits Info -->
                    <div class="alert alert-info alert-permanent tier-info mb-4">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>Limites de votre Niveau : {{ current_user.tier.value.title() }}
                        </h6>
                        <p class="mb-0">
                            {% set product_limit = get_product_limit(current_user.tier.value) %}
                            {% if current_user.tier.value == 'free' %}
                            <strong>Gratuit :</strong> {{ product_limit }} produits maximum par boutique • Commission 15%
                            {% elif current_user.tier.value == 'premium' %}
                            <strong>Premium :</strong> {{ product_limit }} produits maximum par boutique • Commission 10%
                            {% elif current_user.tier.value == 'gold' %}
                            <strong>Gold :</strong> {{ product_limit }} produits maximum par boutique • Commission 5%
                            {% endif %}
                        </p>
                    </div>

                    <form method="POST" enctype="multipart/form-data">
                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-success border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle me-2"></i>Informations de Base
                                </h5>
                            </div>

                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Nom du Produit *</label>
                                    <input type="text" class="form-control" id="name" name="name" required
                                           placeholder="Ex: Boubou Traditionnel Brodé" value="{{ request.form.get('name', '') }}">
                                    <div class="form-text">Nom descriptif et attractif pour votre produit</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="category_id" class="form-label">Catégorie *</label>
                                    <select class="form-select" id="category_id" name="category_id" required>
                                        <option value="">Choisir une catégorie</option>
                                        {% for category in categories %}
                                        <option value="{{ category.id }}" {% if request.form.get('category_id', '', type=int) == category.id %}selected{% endif %}>
                                            {{ category.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="short_description" class="form-label">Description Courte</label>
                                    <input type="text" class="form-control" id="short_description" name="short_description"
                                           placeholder="Résumé en une ligne de votre produit" value="{{ request.form.get('short_description', '') }}">
                                    <div class="form-text">Apparaît dans les listes de produits (optionnel)</div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description Détaillée *</label>
                                    <textarea class="form-control" id="description" name="description" rows="6" required
                                              placeholder="Décrivez votre produit en détail : matériaux, dimensions, utilisation, origine...">{{ request.form.get('description', '') }}</textarea>
                                    <div class="form-text">Minimum 20 caractères. Plus c'est détaillé, mieux c'est !</div>
                                </div>
                            </div>
                        </div>

                        <!-- Pricing -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-success border-bottom pb-2 mb-3">
                                    <i class="fas fa-coins me-2"></i>Prix et Promotion
                                </h5>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="price" class="form-label">Prix Normal *</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="price" name="price" step="1" min="0" required
                                               placeholder="0" value="{{ request.form.get('price', '') }}">
                                        <span class="input-group-text">FCFA</span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="sale_price" class="form-label">Prix Promotionnel</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="sale_price" name="sale_price" step="1" min="0"
                                               placeholder="0" value="{{ request.form.get('sale_price', '') }}">
                                        <span class="input-group-text">FCFA</span>
                                    </div>
                                    <div class="form-text">Optionnel - doit être inférieur au prix normal</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="stock_quantity" class="form-label">Stock Disponible *</label>
                                    <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" min="0" required
                                           placeholder="0" value="{{ request.form.get('stock_quantity', '') }}">
                                    <div class="form-text">Nombre d'unités disponibles</div>
                                </div>
                            </div>
                        </div>

                        <!-- Product Details -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-success border-bottom pb-2 mb-3">
                                    <i class="fas fa-cogs me-2"></i>Détails du Produit
                                </h5>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="sku" class="form-label">SKU / Référence</label>
                                    <input type="text" class="form-control" id="sku" name="sku"
                                           placeholder="REF-001" value="{{ request.form.get('sku', '') }}">
                                    <div class="form-text">Code de référence unique (optionnel)</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="weight" class="form-label">Poids</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="weight" name="weight" step="0.01" min="0"
                                               placeholder="0.00" value="{{ request.form.get('weight', '') }}">
                                        <span class="input-group-text">kg</span>
                                    </div>
                                    <div class="form-text">Pour calculer les frais de livraison</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Type de Produit</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="digital" name="digital"
                                               {% if request.form.get('digital') %}checked{% endif %}>
                                        <label class="form-check-label" for="digital">
                                            Produit numérique (téléchargeable)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Images -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-success border-bottom pb-2 mb-3">
                                    <i class="fas fa-images me-2"></i>Images du Produit
                                </h5>
                                <p class="text-muted mb-3">Ajoutez jusqu'à 5 images de votre produit. La première image sera l'image principale.</p>
                            </div>

                            {% for i in range(5) %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="image-upload-box">
                                    <label for="image_{{ i }}" class="form-label">
                                        Image {{ i + 1 }} {% if i == 0 %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    <input type="file" class="form-control" id="image_{{ i }}" name="image_{{ i }}"
                                           accept="image/*" {% if i == 0 %}required{% endif %}>
                                    <div class="image-preview mt-2" id="preview_{{ i }}" style="display: none;">
                                        <img src="" alt="Aperçu" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- Preview Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-success border-bottom pb-2 mb-3">
                                    <i class="fas fa-eye me-2"></i>Aperçu du Produit
                                </h5>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="product-preview-image bg-white rounded d-flex align-items-center justify-content-center"
                                                     style="height: 120px; border: 2px dashed #dee2e6;">
                                                    <i class="fas fa-image fa-2x text-muted"></i>
                                                </div>
                                            </div>
                                            <div class="col-md-9">
                                                <h6 class="product-name-preview mb-1">Nom de votre produit</h6>
                                                <p class="product-description-preview text-muted mb-2">Description courte apparaîtra ici...</p>
                                                <div class="price-preview">
                                                    <span class="h6 text-primary">0,00 €</span>
                                                </div>
                                                <small class="text-muted">Catégorie : Non sélectionnée</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit -->
                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Important :</strong> Votre produit sera soumis à approbation avant d'être visible publiquement.
                                    Vous recevrez une notification par email une fois approuvé.
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('shop.manage_products', shop_id=shop.id) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Retour aux Produits
                                    </a>
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="fas fa-plus me-2"></i>Créer le Produit
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
.image-upload-box {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s ease;
}

.image-upload-box:hover {
    border-color: #28a745;
    background-color: #f8f9fa;
}

.form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.card {
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}
</style>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview functionality
    const nameInput = document.getElementById('name');
    const shortDescInput = document.getElementById('short_description');
    const priceInput = document.getElementById('price');
    const salePriceInput = document.getElementById('sale_price');
    const categorySelect = document.getElementById('category_id');

    const namePreview = document.querySelector('.product-name-preview');
    const descPreview = document.querySelector('.product-description-preview');
    const pricePreview = document.querySelector('.price-preview');
    const categoryPreview = document.querySelector('.text-muted');

    // Update name preview
    nameInput.addEventListener('input', function() {
        namePreview.textContent = this.value || 'Nom de votre produit';
    });

    // Update description preview
    shortDescInput.addEventListener('input', function() {
        descPreview.textContent = this.value || 'Description courte apparaîtra ici...';
    });

    // Update price preview
    function updatePricePreview() {
        const price = parseFloat(priceInput.value) || 0;
        const salePrice = parseFloat(salePriceInput.value);

        if (salePrice && salePrice < price) {
            pricePreview.innerHTML = `
                <span class="h6 text-primary">${salePrice.toLocaleString()} FCFA</span>
                <span class="text-muted text-decoration-line-through ms-2">${price.toLocaleString()} FCFA</span>
            `;
        } else {
            pricePreview.innerHTML = `<span class="h6 text-primary">${price.toLocaleString()} FCFA</span>`;
        }
    }

    priceInput.addEventListener('input', updatePricePreview);
    salePriceInput.addEventListener('input', updatePricePreview);

    // Update category preview
    categorySelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const categoryText = selectedOption.text || 'Non sélectionnée';
        categoryPreview.textContent = `Catégorie : ${categoryText}`;
    });

    // Image preview functionality
    for (let i = 0; i < 5; i++) {
        const fileInput = document.getElementById(`image_${i}`);
        const preview = document.getElementById(`preview_${i}`);

        fileInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.style.display = 'block';
                    preview.querySelector('img').src = e.target.result;

                    // Update main preview if it's the first image
                    if (i === 0) {
                        const mainPreview = document.querySelector('.product-preview-image');
                        mainPreview.innerHTML = `<img src="${e.target.result}" class="img-fluid rounded" style="max-height: 120px; object-fit: cover;">`;
                    }
                };
                reader.readAsDataURL(file);
            } else {
                preview.style.display = 'none';
                if (i === 0) {
                    const mainPreview = document.querySelector('.product-preview-image');
                    mainPreview.innerHTML = '<i class="fas fa-image fa-2x text-muted"></i>';
                }
            }
        });
    }

    // Character counter for description
    const descriptionInput = document.getElementById('description');
    const charCounter = document.createElement('div');
    charCounter.className = 'form-text text-end';
    charCounter.style.marginTop = '-15px';
    charCounter.style.marginBottom = '15px';
    descriptionInput.parentNode.appendChild(charCounter);

    function updateCharCounter() {
        const length = descriptionInput.value.length;
        charCounter.textContent = `${length} caractères`;
        charCounter.className = `form-text text-end ${length < 20 ? 'text-danger' : 'text-success'}`;
    }

    descriptionInput.addEventListener('input', updateCharCounter);
    updateCharCounter();
});
</script>
{% endblock %}
