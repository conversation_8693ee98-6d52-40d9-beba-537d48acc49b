{% extends "base.html" %}

{% block title %}{{ product.name }} - Afroly.org{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('shop.view', slug=product.shop.slug) }}">{{ product.shop.name }}</a></li>
            {% if product.category %}
            <li class="breadcrumb-item"><a href="{{ url_for('main.categories') }}">{{ product.category.name }}</a></li>
            {% endif %}
            <li class="breadcrumb-item active">{{ product.name }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Product Images -->
        <div class="col-md-6">
            <div class="product-images">
                {% if product.images %}
                <div id="productCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-inner">
                        {% for image in product.images %}
                        <div class="carousel-item {% if loop.first %}active{% endif %}">
                            <img src="{{ image }}" class="d-block w-100 rounded" alt="{{ product.name }}"
                                 style="height: 400px; object-fit: cover;">
                        </div>
                        {% endfor %}
                    </div>

                    {% if product.images|length > 1 %}
                    <button class="carousel-control-prev" type="button" data-bs-target="#productCarousel" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon"></span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#productCarousel" data-bs-slide="next">
                        <span class="carousel-control-next-icon"></span>
                    </button>

                    <!-- Thumbnails -->
                    <div class="carousel-indicators position-relative mt-3">
                        {% for image in product.images %}
                        <button type="button" data-bs-target="#productCarousel" data-bs-slide-to="{{ loop.index0 }}"
                                class="{% if loop.first %}active{% endif %}" style="width: 60px; height: 60px; margin: 0 5px;">
                            <img src="{{ image }}" class="w-100 h-100 rounded" style="object-fit: cover;">
                        </button>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
                {% else %}
                <img src="/static/images/no-image.png" class="img-fluid rounded" alt="{{ product.name }}"
                     style="height: 400px; width: 100%; object-fit: cover;">
                {% endif %}
            </div>
        </div>

        <!-- Product Info -->
        <div class="col-md-6">
            <div class="product-info">
                <!-- Product Title -->
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <h1 class="product-title mb-0">{{ product.name }}</h1>
                    <div class="share-buttons">
                        <div class="dropdown">
                            <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-share-alt me-1"></i>Partager
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="#" onclick="shareOnFacebook('{{ request.url }}', '{{ product.name }}')">
                                        <i class="fab fa-facebook text-primary me-2"></i>Facebook
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#" onclick="shareOnWhatsApp('{{ request.url }}', '{{ product.name }}')">
                                        <i class="fab fa-whatsapp text-success me-2"></i>WhatsApp
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#" onclick="shareOnTwitter('{{ request.url }}', '{{ product.name }}')">
                                        <i class="fab fa-twitter text-info me-2"></i>Twitter
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#" onclick="shareOnTelegram('{{ request.url }}', '{{ product.name }}')">
                                        <i class="fab fa-telegram text-primary me-2"></i>Telegram
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="#" onclick="copyToClipboard('{{ request.url }}')">
                                        <i class="fas fa-copy text-secondary me-2"></i>Copier le lien
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Shop Info -->
                <div class="shop-info mb-3">
                    <div class="d-flex align-items-center">
                        {% if product.shop.logo %}
                        <img src="/static/uploads/shops/{{ product.shop.logo }}" alt="{{ product.shop.name }}"
                             class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                        {% endif %}
                        <span class="text-muted">Vendu par </span>
                        <a href="{{ url_for('shop.view', slug=product.shop.slug) }}" class="text-decoration-none fw-bold">
                            {{ product.shop.name }}
                        </a>
                    </div>
                </div>

                <!-- Rating -->
                {% if reviews %}
                <div class="rating mb-3">
                    <div class="d-flex align-items-center">
                        {% set avg_rating = product.get_average_rating() %}
                        {% for i in range(5) %}
                            {% if i < avg_rating %}
                            <i class="fas fa-star text-warning"></i>
                            {% else %}
                            <i class="far fa-star text-warning"></i>
                            {% endif %}
                        {% endfor %}
                        <span class="ms-2 text-muted">({{ reviews|length }} avis)</span>
                    </div>
                </div>
                {% endif %}

                <!-- Price -->
                <div class="price-section mb-4">
                    {% if product.sale_price %}
                    <div class="d-flex align-items-center">
                        <span class="h3 text-primary mb-0 me-3">{{ "%.0f"|format(product.sale_price) }} {{ product.shop.get_currency_symbol() }}</span>
                        <span class="h5 text-muted text-decoration-line-through mb-0">{{ "%.0f"|format(product.price) }} {{ product.shop.get_currency_symbol() }}</span>
                        <span class="badge bg-danger ms-2">
                            -{{ "%.0f"|format(((product.price - product.sale_price) / product.price * 100)) }}%
                        </span>
                    </div>
                    {% else %}
                    <span class="h3 text-primary mb-0">{{ "%.0f"|format(product.price) }} {{ product.shop.get_currency_symbol() }}</span>
                    {% endif %}
                </div>

                <!-- Short Description -->
                {% if product.short_description %}
                <div class="short-description mb-4">
                    <p class="lead">{{ product.short_description }}</p>
                </div>
                {% endif %}

                <!-- Stock Status -->
                <div class="stock-status mb-4">
                    {% if product.stock_quantity > 10 %}
                    <span class="badge bg-success fs-6">
                        <i class="fas fa-check-circle me-1"></i>En stock ({{ product.stock_quantity }} disponibles)
                    </span>
                    {% elif product.stock_quantity > 0 %}
                    <span class="badge bg-warning fs-6">
                        <i class="fas fa-exclamation-triangle me-1"></i>Stock limité ({{ product.stock_quantity }} restants)
                    </span>
                    {% else %}
                    <span class="badge bg-danger fs-6">
                        <i class="fas fa-times-circle me-1"></i>Rupture de stock
                    </span>
                    {% endif %}
                </div>

                <!-- Purchase Actions -->
                {% if product.stock_quantity > 0 %}
                <div class="purchase-actions mb-4">
                    <div class="row g-2">
                        <!-- Quantity Selector -->
                        <div class="col-12 mb-3">
                            <label for="quantity" class="form-label fw-bold">Quantité :</label>
                            <div class="input-group" style="max-width: 150px;">
                                <button class="btn btn-outline-secondary" type="button" onclick="decreaseQuantity()">-</button>
                                <input type="number" class="form-control text-center" id="quantity" value="1" min="1" max="{{ product.stock_quantity }}">
                                <button class="btn btn-outline-secondary" type="button" onclick="increaseQuantity()">+</button>
                            </div>
                        </div>

                        <!-- Add to Cart Button -->
                        <div class="col-md-6 mb-2">
                            <form action="{{ url_for('cart.add_to_cart') }}" method="POST" class="d-inline">
                                <input type="hidden" name="product_id" value="{{ product.id }}">
                                <input type="hidden" name="quantity" id="cart_quantity" value="1">
                                <button type="submit" class="btn btn-outline-primary btn-lg w-100">
                                    <i class="fas fa-shopping-cart me-2"></i>Ajouter au Panier
                                </button>
                            </form>
                        </div>

                        <!-- Buy Now Button -->
                        <div class="col-md-6 mb-2">
                            <button type="button" class="btn btn-primary btn-lg w-100" onclick="buyNow()">
                                <i class="fas fa-bolt me-2"></i>Achat
                            </button>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="out-of-stock-notice mb-4">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Ce produit est actuellement en rupture de stock.
                    </div>
                </div>
                {% endif %}

                <!-- Contact & Payment Info -->
                <div class="contact-payment-info mb-4">
                    <!-- WhatsApp Contact -->
                    {% if product.shop.get_whatsapp_number() %}
                    <div class="whatsapp-contact mb-3">
                        <a href="{{ product.shop.get_whatsapp_link() }}" target="_blank"
                           class="btn btn-success btn-lg w-100">
                            <i class="fab fa-whatsapp me-2"></i>
                            Contacter sur WhatsApp
                            <small class="d-block">{{ product.shop.get_whatsapp_number() }}</small>
                        </a>
                    </div>
                    {% endif %}

                    <!-- Payment Methods -->
                    {% set payment_methods = product.shop.get_payment_methods() %}
                    {% if payment_methods %}
                    <div class="payment-methods">
                        <h6 class="text-primary mb-2">
                            <i class="fas fa-credit-card me-2"></i>Méthodes de Paiement Acceptées
                        </h6>
                        <div class="row">
                            {% for method in payment_methods[:4] %}
                            {% set method_config = None %}
                            {% for category, methods in config.PAYMENT_METHODS.items() %}
                                {% if method.get('type') in methods and not method_config %}
                                    {% set method_config = methods[method.get('type')] %}
                                {% endif %}
                            {% endfor %}

                            {% if method_config %}
                            <div class="col-6 mb-2">
                                <div class="payment-method-badge">
                                    <span class="badge bg-light text-dark border p-2 w-100">
                                        <i class="{{ method_config.icon }} me-1" style="color: {{ method_config.color }}"></i>
                                        {{ method_config.name }}
                                    </span>
                                </div>
                            </div>
                            {% endif %}
                            {% endfor %}

                            {% if payment_methods|length > 4 %}
                            <div class="col-12">
                                <small class="text-muted">
                                    +{{ payment_methods|length - 4 }} autre(s) méthode(s)
                                </small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Contact Shop Button -->
                    <div class="contact-shop mt-3">
                        <a href="{{ url_for('shop.view', slug=product.shop.slug) }}"
                           class="btn btn-outline-primary btn-lg w-100">
                            <i class="fas fa-store me-2"></i>Visiter la Boutique
                        </a>
                    </div>

                    <!-- Share Product -->
                    <div class="share-product mt-3">
                        <h6 class="mb-2">Partager ce produit :</h6>
                        <div class="d-flex gap-2 flex-wrap">
                            <button class="btn btn-outline-primary btn-sm" onclick="shareOnFacebook('{{ request.url }}', '{{ product.name }}')">
                                <i class="fab fa-facebook me-1"></i>Facebook
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="shareOnWhatsApp('{{ request.url }}', '{{ product.name }}')">
                                <i class="fab fa-whatsapp me-1"></i>WhatsApp
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="shareOnTelegram('{{ request.url }}', '{{ product.name }}')">
                                <i class="fab fa-telegram me-1"></i>Telegram
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="copyToClipboard('{{ request.url }}')">
                                <i class="fas fa-link me-1"></i>Copier
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product Features -->
                <div class="product-features">
                    <ul class="list-unstyled">
                        {% if product.category %}
                        <li class="mb-2">
                            <i class="fas fa-tag text-primary me-2"></i>
                            <strong>Catégorie :</strong> {{ product.category.name }}
                        </li>
                        {% endif %}
                        {% if product.sku %}
                        <li class="mb-2">
                            <i class="fas fa-barcode text-primary me-2"></i>
                            <strong>SKU :</strong> {{ product.sku }}
                        </li>
                        {% endif %}
                        {% if product.weight %}
                        <li class="mb-2">
                            <i class="fas fa-weight text-primary me-2"></i>
                            <strong>Poids :</strong> {{ product.weight }} kg
                        </li>
                        {% endif %}
                        {% if product.digital %}
                        <li class="mb-2">
                            <i class="fas fa-download text-primary me-2"></i>
                            <strong>Produit numérique</strong>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Details Tabs -->
    <div class="row mt-5">
        <div class="col-12">
            <ul class="nav nav-tabs" id="productTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="description-tab" data-bs-toggle="tab"
                            data-bs-target="#description" type="button" role="tab">
                        Description
                    </button>
                </li>
                {% if reviews %}
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="reviews-tab" data-bs-toggle="tab"
                            data-bs-target="#reviews" type="button" role="tab">
                        Avis ({{ reviews|length }})
                    </button>
                </li>
                {% endif %}
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="shipping-tab" data-bs-toggle="tab"
                            data-bs-target="#shipping" type="button" role="tab">
                        Livraison
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="productTabsContent">
                <!-- Description Tab -->
                <div class="tab-pane fade show active" id="description" role="tabpanel">
                    <div class="p-4">
                        <div class="product-description">
                            {{ product.description|replace('\n', '<br>')|safe }}
                        </div>
                    </div>
                </div>

                <!-- Reviews Tab -->
                {% if reviews %}
                <div class="tab-pane fade" id="reviews" role="tabpanel">
                    <div class="p-4">
                        <!-- Add Review Form -->
                        {% if can_review %}
                        <div class="add-review mb-4">
                            <h5>Laisser un Avis</h5>
                            <form method="POST" action="{{ url_for('product.add_review', product_id=product.id) }}">
                                <div class="mb-3">
                                    <label class="form-label">Note :</label>
                                    <div class="rating-input">
                                        {% for i in range(1, 6) %}
                                        <input type="radio" name="rating" value="{{ i }}" id="star{{ i }}" required>
                                        <label for="star{{ i }}"><i class="fas fa-star"></i></label>
                                        {% endfor %}
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="title" class="form-label">Titre (optionnel) :</label>
                                    <input type="text" class="form-control" id="title" name="title">
                                </div>
                                <div class="mb-3">
                                    <label for="comment" class="form-label">Votre avis :</label>
                                    <textarea class="form-control" id="comment" name="comment" rows="4" required></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">Publier l'Avis</button>
                            </form>
                        </div>
                        <hr>
                        {% endif %}

                        <!-- Reviews List -->
                        <div class="reviews-list">
                            {% for review in reviews %}
                            <div class="review-item {% if not loop.last %}border-bottom pb-3 mb-3{% endif %}">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <div class="rating mb-1">
                                            {% for i in range(5) %}
                                                {% if i < review.rating %}
                                                <i class="fas fa-star text-warning"></i>
                                                {% else %}
                                                <i class="far fa-star text-warning"></i>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                        {% if review.title %}
                                        <h6 class="mb-1">{{ review.title }}</h6>
                                        {% endif %}
                                        <p class="mb-1">{{ review.comment }}</p>
                                        <small class="text-muted">
                                            Par {{ review.user.get_full_name() }} • {{ review.created_at.strftime('%d/%m/%Y') }}
                                            {% if review.is_verified_purchase %}
                                            <span class="badge bg-success ms-1">Achat vérifié</span>
                                            {% endif %}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Shipping Tab -->
                <div class="tab-pane fade" id="shipping" role="tabpanel">
                    <div class="p-4">
                        <h5>Informations de Livraison</h5>
                        {% if product.shop.policies and product.shop.policies.shipping %}
                        <p>{{ product.shop.policies.shipping }}</p>
                        {% else %}
                        <p>Contactez le vendeur pour plus d'informations sur la livraison.</p>
                        {% endif %}

                        <div class="contact-seller mt-3">
                            <h6>Contacter le Vendeur</h6>
                            <p>
                                <i class="fas fa-envelope me-2"></i>
                                <a href="mailto:{{ product.shop.contact_email }}">{{ product.shop.contact_email }}</a>
                            </p>
                            {% if product.shop.contact_phone %}
                            <p>
                                <i class="fas fa-phone me-2"></i>
                                <a href="tel:{{ product.shop.contact_phone }}">{{ product.shop.contact_phone }}</a>
                            </p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Products -->
    {% if related_products %}
    <div class="row mt-5">
        <div class="col-12">
            <h3 class="mb-4">Autres Produits de cette Boutique</h3>
            <div class="row">
                {% for related in related_products %}
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card h-100">
                        <img src="{{ related.get_main_image() }}" class="card-img-top" alt="{{ related.name }}"
                             style="height: 200px; object-fit: cover;">
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">{{ related.name }}</h6>
                            <div class="price-section mt-auto">
                                {% if related.sale_price %}
                                <span class="h6 text-primary">{{ "%.0f"|format(related.sale_price) }} {{ product.shop.get_currency_symbol() }}</span>
                                <span class="text-muted text-decoration-line-through ms-2">{{ "%.0f"|format(related.price) }} {{ product.shop.get_currency_symbol() }}</span>
                                {% else %}
                                <span class="h6 text-primary">{{ "%.0f"|format(related.price) }} {{ product.shop.get_currency_symbol() }}</span>
                                {% endif %}
                            </div>
                            <a href="{{ url_for('product.view', slug=related.slug) }}" class="btn btn-outline-primary btn-sm mt-2">
                                Voir le Produit
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
</div>

{% block extra_head %}
<style>
.rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
}

.rating-input input {
    display: none;
}

.rating-input label {
    cursor: pointer;
    color: #ddd;
    font-size: 1.2rem;
    margin-right: 5px;
}

.rating-input label:hover,
.rating-input label:hover ~ label,
.rating-input input:checked ~ label {
    color: #ffc107;
}

.carousel-indicators button {
    border: 2px solid #fff;
    border-radius: 5px;
}

.product-images .carousel-control-prev,
.product-images .carousel-control-next {
    background: rgba(0,0,0,0.5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
}
</style>

<script>
// Quantity controls
function increaseQuantity() {
    const quantityInput = document.getElementById('quantity');
    const cartQuantityInput = document.getElementById('cart_quantity');
    const maxQuantity = parseInt(quantityInput.getAttribute('max'));
    const currentQuantity = parseInt(quantityInput.value);

    if (currentQuantity < maxQuantity) {
        const newQuantity = currentQuantity + 1;
        quantityInput.value = newQuantity;
        cartQuantityInput.value = newQuantity;
    }
}

function decreaseQuantity() {
    const quantityInput = document.getElementById('quantity');
    const cartQuantityInput = document.getElementById('cart_quantity');
    const currentQuantity = parseInt(quantityInput.value);

    if (currentQuantity > 1) {
        const newQuantity = currentQuantity - 1;
        quantityInput.value = newQuantity;
        cartQuantityInput.value = newQuantity;
    }
}

// Update cart quantity when input changes
document.addEventListener('DOMContentLoaded', function() {
    const quantityInput = document.getElementById('quantity');
    const cartQuantityInput = document.getElementById('cart_quantity');

    if (quantityInput && cartQuantityInput) {
        quantityInput.addEventListener('input', function() {
            cartQuantityInput.value = this.value;
        });
    }
});

// Buy Now function
function buyNow() {
    {% if current_user.is_authenticated %}
        const quantity = document.getElementById('quantity').value;
        const productId = {{ product.id }};

        // Show loading state
        const buyButton = event.target;
        const originalText = buyButton.innerHTML;
        buyButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Traitement...';
        buyButton.disabled = true;

        // Create form data
        const formData = new FormData();
        formData.append('product_id', productId);
        formData.append('quantity', quantity);

        // Submit buy now request
        fetch('{{ url_for("cart.buy_now") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Redirect to checkout
                window.location.href = data.checkout_url;
            } else {
                alert(data.message || 'Une erreur est survenue');
                buyButton.innerHTML = originalText;
                buyButton.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Une erreur est survenue');
            buyButton.innerHTML = originalText;
            buyButton.disabled = false;
        });
    {% else %}
        // Redirect to login
        window.location.href = '{{ url_for("auth.login", next=request.url) }}';
    {% endif %}
}

// Share functions
function shareOnFacebook(url, title) {
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank', 'width=600,height=400');
}

function shareOnWhatsApp(url, title) {
    window.open(`https://wa.me/?text=${encodeURIComponent(title + ' ' + url)}`, '_blank');
}

function shareOnTelegram(url, title) {
    window.open(`https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`, '_blank');
}

function copyToClipboard(url) {
    navigator.clipboard.writeText(url).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'alert alert-success alert-dismissible fade show position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
        toast.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            Lien copié dans le presse-papiers!
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(toast);

        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    });
}
</script>
{% endblock %}
{% endblock %}
