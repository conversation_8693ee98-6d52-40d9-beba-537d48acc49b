{% extends "base.html" %}

{% block title %}Parcourir Produits AliExpress - Afroly.org{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-6 fw-bold">
                <i class="fas fa-search me-3 text-primary"></i>Parcourir Produits AliExpress
            </h1>
            <p class="lead text-muted">Trouvez et importez des produits pour votre boutique</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('dropshipping.dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Retour Dashboard
            </a>
        </div>
    </div>

    <!-- Search Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ url_for('dropshipping.browse_products') }}">
                        <div class="row g-3">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" name="keywords"
                                           placeholder="Rechercher des produits (ex: téléphone, vêtements, électronique...)"
                                           value="{{ keywords }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="shop_id">
                                    <option value="">Choisir boutique</option>
                                    {% for shop in shops %}
                                    <option value="{{ shop.id }}">{{ shop.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-1"></i>Rechercher
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Featured Promos -->
    {% if promos and not keywords %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-fire me-2 text-danger"></i>Promotions en Vedette
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for promo in promos[:6] %}
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card h-100 border-warning">
                                <div class="card-body">
                                    <h6 class="card-title">{{ promo.get('friendly_name', promo.get('promo_name', 'Promotion')) }}</h6>
                                    <p class="card-text small text-muted">
                                        {{ promo.get('product_num', 0) }} produits disponibles
                                    </p>
                                    <button class="btn btn-outline-warning btn-sm load-promo-products"
                                            data-promo="{{ promo.get('promo_name', '') }}">
                                        <i class="fas fa-eye me-1"></i>Voir Produits
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Products Grid -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        {% if keywords %}
                        Résultats pour "{{ keywords }}"
                        {% else %}
                        Produits Recommandés
                        {% endif %}
                    </h5>
                    <span class="badge bg-primary">{{ products|length }} produits</span>
                </div>
                <div class="card-body">
                    {% if products %}
                    <div class="row" id="products-grid">
                        {% for product in products %}
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card h-100 product-card">
                                <!-- Product Image -->
                                <div class="position-relative">
                                    {% set formatted_images = product.get('formatted_images', []) %}
                                    {% set image_url = '' %}

                                    {% if formatted_images and formatted_images|length > 0 %}
                                        {% set image_url = formatted_images[0] %}
                                    {% endif %}

                                    {% if not image_url %}
                                        {% set image_url = product.get('product_main_image_url', '') %}
                                    {% endif %}

                                    {% if not image_url and product.get('product_small_image_urls') %}
                                        {% set small_images = product.get('product_small_image_urls', {}) %}
                                        {% if small_images.get('string') %}
                                            {% if small_images.string is iterable and small_images.string is not string %}
                                                {% set image_url = small_images.string[0] if small_images.string else '' %}
                                            {% else %}
                                                {% set image_url = small_images.string %}
                                            {% endif %}
                                        {% endif %}
                                    {% endif %}

                                    {% if image_url and image_url != '/static/images/no-image.svg' %}
                                    <img src="{{ image_url }}" class="card-img-top" alt="{{ product.get('product_title', '') }}"
                                         style="height: 200px; object-fit: cover;"
                                         onerror="this.onerror=null; this.src='/static/images/no-image.svg';">
                                    {% else %}
                                    <img src="/static/images/no-image.svg" class="card-img-top" alt="Aucune image"
                                         style="height: 200px; object-fit: cover;">
                                    {% endif %}

                                    <!-- Price Badge -->
                                    <div class="position-absolute top-0 end-0 m-2">
                                        <span class="badge bg-success">
                                            ${{ product.get('target_sale_price', '0') }}
                                        </span>
                                    </div>
                                </div>

                                <div class="card-body d-flex flex-column">
                                    <!-- Product Title -->
                                    <h6 class="card-title">
                                        {{ product.get('product_title', 'Produit sans titre')[:60] }}
                                        {% if product.get('product_title', '')|length > 60 %}...{% endif %}
                                    </h6>

                                    <!-- Product Info -->
                                    <div class="mb-2">
                                        {% if product.get('evaluate_rate') %}
                                        <small class="text-muted">
                                            <i class="fas fa-star text-warning"></i>
                                            {{ product.get('evaluate_rate', '0') }}
                                        </small>
                                        {% endif %}

                                        {% if product.get('orders_count') %}
                                        <small class="text-muted ms-2">
                                            <i class="fas fa-shopping-cart"></i>
                                            {{ product.get('orders_count', 0) }} commandes
                                        </small>
                                        {% endif %}
                                    </div>

                                    <!-- Price Info -->
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong class="text-success">
                                                    ${{ product.get('target_sale_price', '0') }}
                                                </strong>
                                                {% if product.get('target_original_price') and product.get('target_original_price') != product.get('target_sale_price') %}
                                                <small class="text-muted text-decoration-line-through ms-1">
                                                    ${{ product.get('target_original_price') }}
                                                </small>
                                                {% endif %}
                                            </div>
                                            <small class="text-muted">
                                                Min: {{ product.get('product_min_order_quantity', 1) }}
                                            </small>
                                        </div>
                                    </div>

                                    <!-- Import Button -->
                                    <div class="mt-auto">
                                        <button class="btn btn-primary btn-sm w-100 import-product-btn"
                                                data-product-id="{{ product.get('product_id', '') }}"
                                                data-product-title="{{ product.get('product_title', '') }}"
                                                data-product-price="{{ product.get('target_sale_price', '0') }}">
                                            <i class="fas fa-download me-1"></i>Importer
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Load More Button -->
                    {% if products|length == 20 %}
                    <div class="text-center mt-4">
                        <button class="btn btn-outline-primary" id="load-more-btn"
                                data-page="{{ current_page + 1 }}"
                                data-keywords="{{ keywords }}">
                            <i class="fas fa-plus me-2"></i>Charger Plus
                        </button>
                    </div>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-4x text-muted mb-3"></i>
                        <h4>Aucun produit trouvé</h4>
                        {% if keywords %}
                        <p class="text-muted">Essayez avec d'autres mots-clés ou parcourez les promotions en vedette.</p>
                        {% else %}
                        <p class="text-muted">Utilisez la barre de recherche pour trouver des produits à importer.</p>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Product Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Importer Produit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="import-form">
                <div class="modal-body">
                    <input type="hidden" id="aliexpress_id" name="aliexpress_id">

                    <div class="mb-3">
                        <label for="shop_id" class="form-label">Boutique de destination *</label>
                        <select class="form-select" id="shop_id" name="shop_id" required>
                            <option value="">Choisir une boutique</option>
                            {% for shop in shops %}
                            <option value="{{ shop.id }}">{{ shop.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="custom_name" class="form-label">Nom du produit (optionnel)</label>
                        <input type="text" class="form-control" id="custom_name" name="custom_name"
                               placeholder="Laissez vide pour utiliser le nom original">
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="markup_percentage" class="form-label">Marge (%)</label>
                                <input type="number" class="form-control" id="markup_percentage"
                                       name="markup_percentage" value="50" min="0" max="500">
                                <small class="text-muted">Marge à appliquer sur le prix original</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="custom_price" class="form-label">Prix personnalisé (€)</label>
                                <input type="number" class="form-control" id="custom_price"
                                       name="custom_price" step="0.01" min="0">
                                <small class="text-muted">Ou définissez un prix fixe</small>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Prix original:</strong> $<span id="original-price">0</span><br>
                        <strong>Prix final estimé:</strong> €<span id="final-price">0</span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-download me-1"></i>Importer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
.product-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-img-top {
    transition: transform 0.3s ease;
}

.product-card:hover .card-img-top {
    transform: scale(1.05);
}

.import-product-btn {
    transition: all 0.3s ease;
}

.import-product-btn:hover {
    transform: translateY(-2px);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Import product functionality
    const importButtons = document.querySelectorAll('.import-product-btn');
    const importModal = new bootstrap.Modal(document.getElementById('importModal'));
    const importForm = document.getElementById('import-form');

    // Handle import button clicks
    importButtons.forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.dataset.productId;
            const productTitle = this.dataset.productTitle;
            const productPrice = this.dataset.productPrice;

            // Store product data for import
            window.currentProductData = {
                aliexpress_id: productId,
                name: productTitle,
                price: parseFloat(productPrice) || 0,
                images: [], // Will be populated by the backend
                source: 'aliexpress'
            };

            // Fill modal with product data
            document.getElementById('aliexpress_id').value = productId;
            document.getElementById('custom_name').value = productTitle;
            document.getElementById('original-price').textContent = productPrice;

            // Calculate initial final price
            calculateFinalPrice();

            // Show modal
            importModal.show();
        });
    });

    // Handle markup percentage change
    document.getElementById('markup_percentage').addEventListener('input', calculateFinalPrice);
    document.getElementById('custom_price').addEventListener('input', calculateFinalPrice);

    function calculateFinalPrice() {
        const originalPrice = parseFloat(document.getElementById('original-price').textContent) || 0;
        const markupPercentage = parseFloat(document.getElementById('markup_percentage').value) || 0;
        const customPrice = parseFloat(document.getElementById('custom_price').value) || 0;

        let finalPrice;
        if (customPrice > 0) {
            finalPrice = customPrice;
        } else {
            finalPrice = originalPrice * (1 + markupPercentage / 100);
        }

        // Convert USD to EUR (simplified rate: 1 USD = 0.85 EUR)
        const finalPriceEur = finalPrice * 0.85;
        document.getElementById('final-price').textContent = finalPriceEur.toFixed(2);
    }

    // Handle form submission
    importForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;

        // Add product data to the request
        const requestData = {
            aliexpress_id: formData.get('aliexpress_id'),
            shop_id: formData.get('shop_id'),
            custom_name: formData.get('custom_name'),
            custom_price: formData.get('custom_price'),
            markup_percentage: formData.get('markup_percentage'),
            product_data: window.currentProductData || {}
        };

        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Importation...';
        submitBtn.disabled = true;

        fetch('{{ url_for("dropshipping.import_product") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showAlert('success', data.message);
                importModal.hide();

                // Optionally redirect to product
                if (data.product_url) {
                    setTimeout(() => {
                        window.open(data.product_url, '_blank');
                    }, 1000);
                }
            } else {
                showAlert('error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'Erreur lors de l\'importation du produit');
        })
        .finally(() => {
            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });

    // Load promo products
    document.querySelectorAll('.load-promo-products').forEach(button => {
        button.addEventListener('click', function() {
            const promoName = this.dataset.promo;
            loadPromoProducts(promoName);
        });
    });

    function loadPromoProducts(promoName) {
        const productsGrid = document.getElementById('products-grid');

        // Show loading
        productsGrid.innerHTML = '<div class="col-12 text-center py-5"><i class="fas fa-spinner fa-spin fa-3x text-primary"></i><p class="mt-3">Chargement des produits...</p></div>';

        fetch(`{{ url_for("dropshipping.api_promo_products", promo_name="PLACEHOLDER") }}`.replace('PLACEHOLDER', encodeURIComponent(promoName)))
        .then(response => response.json())
        .then(data => {
            if (data.success && data.products) {
                displayProducts(data.products);
            } else {
                productsGrid.innerHTML = '<div class="col-12 text-center py-5"><i class="fas fa-exclamation-triangle fa-3x text-warning"></i><p class="mt-3">Aucun produit trouvé pour cette promotion.</p></div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            productsGrid.innerHTML = '<div class="col-12 text-center py-5"><i class="fas fa-times fa-3x text-danger"></i><p class="mt-3">Erreur lors du chargement des produits.</p></div>';
        });
    }

    function displayProducts(products) {
        const productsGrid = document.getElementById('products-grid');
        let html = '';

        products.forEach(product => {
            const imageUrl = product.images && product.images.length > 0 ? product.images[0] : '';
            const imageHtml = imageUrl ?
                `<img src="${imageUrl}" class="card-img-top" alt="${product.name}" style="height: 200px; object-fit: cover;">` :
                `<div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;"><i class="fas fa-image fa-3x text-muted"></i></div>`;

            html += `
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card h-100 product-card">
                        <div class="position-relative">
                            ${imageHtml}
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-success">$${product.price}</span>
                            </div>
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">${product.name.substring(0, 60)}${product.name.length > 60 ? '...' : ''}</h6>
                            <div class="mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-star text-warning"></i> ${product.rating}
                                    <i class="fas fa-shopping-cart ms-2"></i> ${product.orders_count} commandes
                                </small>
                            </div>
                            <div class="mb-3">
                                <strong class="text-success">$${product.price}</strong>
                                ${product.original_price && product.original_price !== product.price ?
                                    `<small class="text-muted text-decoration-line-through ms-1">$${product.original_price}</small>` : ''}
                            </div>
                            <div class="mt-auto">
                                <button class="btn btn-primary btn-sm w-100 import-product-btn"
                                        data-product-id="${product.aliexpress_id}"
                                        data-product-title="${product.name}"
                                        data-product-price="${product.price}">
                                    <i class="fas fa-download me-1"></i>Importer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        productsGrid.innerHTML = html;

        // Re-attach event listeners to new buttons
        document.querySelectorAll('.import-product-btn').forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.dataset.productId;
                const productTitle = this.dataset.productTitle;
                const productPrice = this.dataset.productPrice;

                document.getElementById('aliexpress_id').value = productId;
                document.getElementById('custom_name').value = productTitle;
                document.getElementById('original-price').textContent = productPrice;

                calculateFinalPrice();
                importModal.show();
            });
        });
    }

    function showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';

        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            <i class="fas ${iconClass} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alert);

        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 5000);
    }
});
</script>
{% endblock %}