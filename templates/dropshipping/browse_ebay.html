{% extends "base.html" %}

{% block title %}Parcourir Produits eBay - Afroly.org{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-6 fw-bold">
                <i class="fas fa-gavel me-3 text-primary"></i>Parcourir Produits eBay
            </h1>
            <p class="lead text-muted">Trouvez des produits de qualité sur eBay pour votre boutique</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group" role="group">
                <a href="{{ url_for('dropshipping.dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour Dashboard
                </a>
                <a href="{{ url_for('dropshipping.browse_products') }}" class="btn btn-outline-info">
                    <i class="fas fa-shopping-cart me-2"></i>AliExpress
                </a>
            </div>
        </div>
    </div>

    <!-- Search Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ url_for('dropshipping.browse_ebay') }}">
                        <div class="row g-3">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" name="keywords"
                                           placeholder="Rechercher sur eBay (ex: iPhone, MacBook, sneakers...)"
                                           value="{{ keywords }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="shop_id">
                                    <option value="">Choisir boutique</option>
                                    {% for shop in shops %}
                                    <option value="{{ shop.id }}">{{ shop.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-1"></i>Rechercher
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Categories & Trending -->
    {% if not keywords %}
    <div class="row mb-4">
        <!-- Categories -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tags me-2 text-primary"></i>Catégories Populaires
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for category in categories[:8] %}
                        <div class="col-6 mb-2">
                            <button class="btn btn-outline-secondary btn-sm w-100 category-btn"
                                    data-category="{{ category }}">
                                {{ category }}
                            </button>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Trending Searches -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-fire me-2 text-danger"></i>Recherches Tendances
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex flex-wrap gap-2">
                        {% for search in trending_searches[:10] %}
                        <button class="btn btn-outline-warning btn-sm trending-btn"
                                data-search="{{ search }}">
                            {{ search }}
                        </button>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Products Grid -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        {% if keywords %}
                        Résultats eBay pour "{{ keywords }}"
                        {% else %}
                        Produits Recommandés eBay
                        {% endif %}
                    </h5>
                    <span class="badge bg-primary">{{ products|length }} produits</span>
                </div>
                <div class="card-body">
                    {% if products %}
                    <div class="row" id="products-grid">
                        {% for product in products %}
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card h-100 product-card">
                                <!-- Product Image -->
                                <div class="position-relative">
                                    {% if product.get('image') %}
                                    <img src="{{ product.get('image') }}" class="card-img-top" alt="{{ product.get('title', '') }}"
                                         style="height: 200px; object-fit: cover;">
                                    {% else %}
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                        <i class="fas fa-image fa-3x text-muted"></i>
                                    </div>
                                    {% endif %}

                                    <!-- Price Badge -->
                                    <div class="position-absolute top-0 end-0 m-2">
                                        <span class="badge bg-success">
                                            {{ product.get('price', 'N/A') }}
                                        </span>
                                    </div>

                                    <!-- eBay Badge -->
                                    <div class="position-absolute top-0 start-0 m-2">
                                        <span class="badge bg-warning">
                                            <i class="fas fa-gavel me-1"></i>eBay
                                        </span>
                                    </div>
                                </div>

                                <div class="card-body d-flex flex-column">
                                    <!-- Product Title -->
                                    <h6 class="card-title">
                                        {{ product.get('title', 'Produit sans titre')[:60] }}
                                        {% if product.get('title', '')|length > 60 %}...{% endif %}
                                    </h6>

                                    <!-- Product Info -->
                                    <div class="mb-2">
                                        {% if product.get('rating') %}
                                        <small class="text-muted">
                                            <i class="fas fa-star text-warning"></i>
                                            {{ product.get('rating', 'N/A') }}
                                        </small>
                                        {% endif %}

                                        {% if product.get('shipping') %}
                                        <small class="text-muted ms-2">
                                            <i class="fas fa-truck"></i>
                                            {{ product.get('shipping', 'N/A')[:20] }}
                                        </small>
                                        {% endif %}
                                    </div>

                                    <!-- Price Info -->
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong class="text-success">
                                                    {{ product.get('price', 'N/A') }}
                                                </strong>
                                            </div>
                                            <small class="text-muted">
                                                <i class="fas fa-shield-alt text-success"></i>
                                                Protection
                                            </small>
                                        </div>
                                    </div>

                                    <!-- Import Button -->
                                    <div class="mt-auto">
                                        <button class="btn btn-primary btn-sm w-100 import-ebay-product-btn"
                                                data-product='{{ product|tojson }}'>
                                            <i class="fas fa-download me-1"></i>Importer depuis eBay
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Load More Button -->
                    {% if products|length >= 20 %}
                    <div class="text-center mt-4">
                        <button class="btn btn-outline-primary" id="load-more-btn"
                                data-page="{{ current_page + 1 }}"
                                data-keywords="{{ keywords }}">
                            <i class="fas fa-plus me-2"></i>Charger Plus
                        </button>
                    </div>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-gavel fa-4x text-muted mb-3"></i>
                        <h4>Aucun produit trouvé</h4>
                        {% if keywords %}
                        <p class="text-muted">Essayez avec d'autres mots-clés ou explorez les catégories populaires.</p>
                        {% else %}
                        <p class="text-muted">Utilisez la barre de recherche ou cliquez sur une catégorie pour commencer.</p>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import eBay Product Modal -->
<div class="modal fade" id="importEbayModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-gavel me-2"></i>Importer Produit eBay
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="import-ebay-form">
                <div class="modal-body">
                    <input type="hidden" id="ebay_product_data" name="product_data">

                    <div class="mb-3">
                        <label for="ebay_shop_id" class="form-label">Boutique de destination *</label>
                        <select class="form-select" id="ebay_shop_id" name="shop_id" required>
                            <option value="">Choisir une boutique</option>
                            {% for shop in shops %}
                            <option value="{{ shop.id }}">{{ shop.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="ebay_custom_name" class="form-label">Nom du produit (optionnel)</label>
                        <input type="text" class="form-control" id="ebay_custom_name" name="custom_name"
                               placeholder="Laissez vide pour utiliser le nom original">
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ebay_markup_percentage" class="form-label">Marge (%)</label>
                                <input type="number" class="form-control" id="ebay_markup_percentage"
                                       name="markup_percentage" value="50" min="0" max="500">
                                <small class="text-muted">Marge à appliquer sur le prix original</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ebay_custom_price" class="form-label">Prix personnalisé (€)</label>
                                <input type="number" class="form-control" id="ebay_custom_price"
                                       name="custom_price" step="0.01" min="0">
                                <small class="text-muted">Ou définissez un prix fixe</small>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Prix original:</strong> <span id="ebay-original-price">$0</span><br>
                        <strong>Prix final estimé:</strong> €<span id="ebay-final-price">0</span>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Note:</strong> Vérifiez les conditions de vente du vendeur eBay et les frais de livraison avant d'importer.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-download me-1"></i>Importer depuis eBay
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
.product-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-img-top {
    transition: transform 0.3s ease;
}

.product-card:hover .card-img-top {
    transform: scale(1.05);
}

.import-ebay-product-btn {
    transition: all 0.3s ease;
}

.import-ebay-product-btn:hover {
    transform: translateY(-2px);
}

.category-btn, .trending-btn {
    transition: all 0.2s ease;
}

.category-btn:hover, .trending-btn:hover {
    transform: scale(1.05);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Import eBay product functionality
    const importButtons = document.querySelectorAll('.import-ebay-product-btn');
    const importModal = new bootstrap.Modal(document.getElementById('importEbayModal'));
    const importForm = document.getElementById('import-ebay-form');

    // Handle import button clicks
    importButtons.forEach(button => {
        button.addEventListener('click', function() {
            const productData = JSON.parse(this.dataset.product);

            // Fill modal with product data
            document.getElementById('ebay_product_data').value = JSON.stringify(productData);
            document.getElementById('ebay_custom_name').value = productData.title || '';

            // Extract and display price
            const priceStr = productData.price || '$0';
            const price = extractPriceFromString(priceStr);
            document.getElementById('ebay-original-price').textContent = priceStr;

            // Calculate initial final price
            calculateEbayFinalPrice();

            // Show modal
            importModal.show();
        });
    });

    // Handle markup percentage change
    document.getElementById('ebay_markup_percentage').addEventListener('input', calculateEbayFinalPrice);
    document.getElementById('ebay_custom_price').addEventListener('input', calculateEbayFinalPrice);

    function calculateEbayFinalPrice() {
        const originalPriceStr = document.getElementById('ebay-original-price').textContent;
        const originalPrice = extractPriceFromString(originalPriceStr);
        const markupPercentage = parseFloat(document.getElementById('ebay_markup_percentage').value) || 0;
        const customPrice = parseFloat(document.getElementById('ebay_custom_price').value) || 0;

        let finalPrice;
        if (customPrice > 0) {
            finalPrice = customPrice;
        } else {
            finalPrice = originalPrice * (1 + markupPercentage / 100);
        }

        // Convert USD to EUR (simplified rate: 1 USD = 0.85 EUR)
        const finalPriceEur = finalPrice * 0.85;
        document.getElementById('ebay-final-price').textContent = finalPriceEur.toFixed(2);
    }

    function extractPriceFromString(priceStr) {
        // Extract numeric value from price string
        const match = priceStr.match(/[\d,]+\.?\d*/);
        if (match) {
            return parseFloat(match[0].replace(',', ''));
        }
        return 0;
    }

    // Handle form submission
    importForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = {
            product_data: JSON.parse(document.getElementById('ebay_product_data').value),
            shop_id: parseInt(document.getElementById('ebay_shop_id').value),
            custom_name: document.getElementById('ebay_custom_name').value,
            custom_price: parseFloat(document.getElementById('ebay_custom_price').value) || null,
            markup_percentage: parseFloat(document.getElementById('ebay_markup_percentage').value) || 50
        };

        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;

        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Importation...';
        submitBtn.disabled = true;

        fetch('{{ url_for("dropshipping.import_ebay_product") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showAlert('success', data.message);
                importModal.hide();

                // Optionally redirect to product
                if (data.product_url) {
                    setTimeout(() => {
                        window.open(data.product_url, '_blank');
                    }, 1000);
                }
            } else {
                showAlert('error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'Erreur lors de l\'importation du produit eBay');
        })
        .finally(() => {
            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });

    // Category button clicks
    document.querySelectorAll('.category-btn').forEach(button => {
        button.addEventListener('click', function() {
            const category = this.dataset.category;
            searchProducts(category);
        });
    });

    // Trending search button clicks
    document.querySelectorAll('.trending-btn').forEach(button => {
        button.addEventListener('click', function() {
            const search = this.dataset.search;
            searchProducts(search);
        });
    });

    function searchProducts(keywords) {
        // Update search input
        const searchInput = document.querySelector('input[name="keywords"]');
        if (searchInput) {
            searchInput.value = keywords;
        }

        // Submit form or redirect
        const form = document.querySelector('form');
        if (form) {
            form.submit();
        } else {
            window.location.href = `{{ url_for('dropshipping.browse_ebay') }}?keywords=${encodeURIComponent(keywords)}`;
        }
    }

    function showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';

        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            <i class="fas ${iconClass} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alert);

        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 5000);
    }
});
</script>
{% endblock %}