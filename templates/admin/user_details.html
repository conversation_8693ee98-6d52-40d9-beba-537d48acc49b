{% extends "base.html" %}

{% block title %}Détails Utilisateur - {{ user.get_full_name() }} - Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-user me-3 text-primary"></i>{{ user.get_full_name() }}
                    </h2>
                    <p class="text-muted mb-0">Détails de l'utilisateur et activité</p>
                </div>
                <div>
                    <a href="{{ url_for('admin.manage_users') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Retour aux Utilisateurs
                    </a>
                    <a href="{{ url_for('admin.edit_user', user_id=user.id) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Modifier
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- User Info Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center border-primary">
                <div class="card-body">
                    <i class="fas fa-crown fa-2x text-primary mb-2"></i>
                    <h6>Niveau</h6>
                    <span class="badge bg-primary fs-6">{{ user.tier.value.title() }}</span>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-success">
                <div class="card-body">
                    <i class="fas fa-store fa-2x text-success mb-2"></i>
                    <h6>Boutiques</h6>
                    <strong class="fs-5">{{ user_shops|length }}</strong>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-info">
                <div class="card-body">
                    <i class="fas fa-shopping-cart fa-2x text-info mb-2"></i>
                    <h6>Commandes</h6>
                    <strong class="fs-5">{{ user_orders|length }}</strong>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-warning">
                <div class="card-body">
                    <i class="fas fa-coins fa-2x text-warning mb-2"></i>
                    <h6>Crédits</h6>
                    <strong class="fs-5">{{ user.credit_balance }}</strong>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- User Details -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Informations Personnelles
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>{{ user.email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Téléphone:</strong></td>
                                    <td>{{ user.phone or 'Non renseigné' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Rôle:</strong></td>
                                    <td>
                                        {% if user.role.value == 'admin' %}
                                        <span class="badge bg-danger">Administrateur</span>
                                        {% elif user.role.value == 'vendor' %}
                                        <span class="badge bg-success">Vendeur</span>
                                        {% else %}
                                        <span class="badge bg-primary">Acheteur</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Statut:</strong></td>
                                    <td>
                                        {% if user.is_active %}
                                        <span class="badge bg-success">Actif</span>
                                        {% else %}
                                        <span class="badge bg-danger">Inactif</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Email Vérifié:</strong></td>
                                    <td>
                                        {% if user.email_verified %}
                                        <span class="badge bg-success">Oui</span>
                                        {% else %}
                                        <span class="badge bg-warning">Non</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Date d'Inscription:</strong></td>
                                    <td>{{ user.created_at.strftime('%d/%m/%Y à %H:%M') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Dernière Mise à Jour:</strong></td>
                                    <td>{{ user.updated_at.strftime('%d/%m/%Y à %H:%M') }}</td>
                                </tr>
                                {% if user.expiration_date %}
                                <tr>
                                    <td><strong>Date d'Expiration:</strong></td>
                                    <td>
                                        {% if user.is_expired() %}
                                        <span class="text-danger">{{ user.expiration_date.strftime('%d/%m/%Y') }} (Expiré)</span>
                                        {% else %}
                                        <span class="text-success">{{ user.expiration_date.strftime('%d/%m/%Y') }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User's Shops -->
            {% if user_shops %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-store me-2"></i>Boutiques ({{ user_shops|length }})
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for shop in user_shops %}
                        <div class="col-md-6 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        {% if shop.logo %}
                                        <img src="/static/uploads/shops/{{ shop.logo }}" alt="{{ shop.name }}"
                                             class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                        {% else %}
                                        <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center"
                                             style="width: 50px; height: 50px;">
                                            <i class="fas fa-store text-muted"></i>
                                        </div>
                                        {% endif %}
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                <a href="{{ url_for('admin.view_shop', shop_id=shop.id) }}" class="text-decoration-none">
                                                    {{ shop.name }}
                                                </a>
                                            </h6>
                                            <small class="text-muted">{{ shop.country }}</small>
                                            <div class="mt-1">
                                                {% if shop.status.value == 'active' %}
                                                <span class="badge bg-success">Actif</span>
                                                {% elif shop.status.value == 'pending' %}
                                                <span class="badge bg-warning">En Attente</span>
                                                {% elif shop.status.value == 'rejected' %}
                                                <span class="badge bg-danger">Rejeté</span>
                                                {% else %}
                                                <span class="badge bg-secondary">Suspendu</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Recent Orders -->
            {% if user_orders %}
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>Commandes Récentes
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Commande</th>
                                    <th>Boutique</th>
                                    <th>Total</th>
                                    <th>Statut</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in user_orders %}
                                <tr>
                                    <td>
                                        <a href="{{ url_for('admin.view_order', order_id=order.id) }}" class="text-decoration-none">
                                            #{{ order.id }}
                                        </a>
                                    </td>
                                    <td>
                                        {% if order.shop %}
                                        <a href="{{ url_for('admin.view_shop', shop_id=order.shop.id) }}" class="text-decoration-none">
                                            {{ order.shop.name }}
                                        </a>
                                        {% else %}
                                        <span class="text-muted">Boutique supprimée</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ "%.0f"|format(order.total) }} FCFA</td>
                                    <td>
                                        {% if order.status.value == 'pending' %}
                                        <span class="badge bg-warning">En Attente</span>
                                        {% elif order.status.value == 'processing' %}
                                        <span class="badge bg-primary">En Cours</span>
                                        {% elif order.status.value == 'shipped' %}
                                        <span class="badge bg-secondary">Expédiée</span>
                                        {% elif order.status.value == 'delivered' %}
                                        <span class="badge bg-success">Livrée</span>
                                        {% elif order.status.value == 'cancelled' %}
                                        <span class="badge bg-danger">Annulée</span>
                                        {% elif order.status.value == 'refunded' %}
                                        <span class="badge bg-info">Remboursée</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ order.created_at.strftime('%d/%m/%Y') }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>Actions Rapides
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('admin.edit_user', user_id=user.id) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>Modifier l'Utilisateur
                        </a>
                        {% if user.phone %}
                        <a href="https://wa.me/{{ user.phone.replace('+', '').replace(' ', '') }}" 
                           target="_blank" class="btn btn-success">
                            <i class="fab fa-whatsapp me-2"></i>Contacter WhatsApp
                        </a>
                        {% endif %}
                        <a href="mailto:{{ user.email }}" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-2"></i>Envoyer Email
                        </a>
                    </div>
                </div>
            </div>

            <!-- Credit Transactions -->
            {% if credit_transactions %}
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-coins me-2"></i>Transactions Crédits
                    </h5>
                </div>
                <div class="card-body">
                    {% for transaction in credit_transactions %}
                    <div class="d-flex justify-content-between align-items-center mb-2 {% if not loop.last %}border-bottom pb-2{% endif %}">
                        <div>
                            <small class="fw-bold">
                                {% if transaction.amount > 0 %}
                                <span class="text-success">+{{ transaction.amount }}</span>
                                {% else %}
                                <span class="text-danger">{{ transaction.amount }}</span>
                                {% endif %}
                            </small>
                            <div>
                                <small class="text-muted">{{ transaction.description }}</small>
                            </div>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">{{ transaction.created_at.strftime('%d/%m') }}</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
