{% extends "base.html" %}

{% block title %}Gestion des Commandes - Admin - Afroly.org{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-6 fw-bold">
                <i class="fas fa-shopping-cart me-3 text-info"></i>Gestion des Commandes
            </h1>
            <p class="lead text-muted">Superviser toutes les commandes de la plateforme</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Retour au Dashboard
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">Recherche</label>
                            <input type="text" class="form-control" name="search" value="{{ search }}" 
                                   placeholder="Numéro de commande...">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Statut</label>
                            <select class="form-select" name="status">
                                <option value="all" {% if status_filter == 'all' %}selected{% endif %}>Tous</option>
                                <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>En Attente</option>
                                <option value="processing" {% if status_filter == 'processing' %}selected{% endif %}>En Traitement</option>
                                <option value="shipped" {% if status_filter == 'shipped' %}selected{% endif %}>Expédiée</option>
                                <option value="delivered" {% if status_filter == 'delivered' %}selected{% endif %}>Livrée</option>
                                <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>Annulée</option>
                                <option value="refunded" {% if status_filter == 'refunded' %}selected{% endif %}>Remboursée</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Filtrer
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-list me-2"></i>Liste des Commandes
                        <span class="badge bg-info ms-2">{{ orders.total }} total</span>
                    </h6>
                </div>
                <div class="card-body">
                    {% if orders.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Commande</th>
                                    <th>Client</th>
                                    <th>Boutique</th>
                                    <th>Montant</th>
                                    <th>Statut</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders.items %}
                                <tr>
                                    <td>
                                        <div>
                                            <h6 class="mb-0">{{ order.order_number or '#' + order.id|string }}</h6>
                                            <small class="text-muted">{{ order.items|length }} article(s)</small>
                                            {% if order.payment_method %}
                                            <br><span class="badge bg-light text-dark">{{ order.payment_method }}</span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if order.customer %}
                                        <div>
                                            <h6 class="mb-0">{{ order.customer.get_full_name() }}</h6>
                                            <small class="text-muted">{{ order.customer.email }}</small>
                                            {% if order.customer.phone %}
                                            <br><small class="text-success">
                                                <i class="fab fa-whatsapp me-1"></i>{{ order.customer.phone }}
                                            </small>
                                            {% endif %}
                                        </div>
                                        {% else %}
                                        <div>
                                            {% set addr = order.shipping_address %}
                                            {% if addr and addr.name %}
                                            <h6 class="mb-0">{{ addr.name }}</h6>
                                            <small class="text-muted">{{ addr.email or 'Email non fourni' }}</small>
                                            {% if addr.phone %}
                                            <br><small class="text-success">
                                                <i class="fas fa-phone me-1"></i>{{ addr.phone }}
                                            </small>
                                            {% endif %}
                                            {% else %}
                                            <span class="text-muted">Commande manuelle</span>
                                            {% endif %}
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div>
                                            <h6 class="mb-0">{{ order.shop.name }}</h6>
                                            <small class="text-muted">{{ order.shop.owner.get_full_name() }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ "%.0f"|format(order.total) }} FCFA</strong>
                                            {% if order.commission_amount %}
                                            <br><small class="text-success">Commission: {{ "%.0f"|format(order.commission_amount) }} FCFA</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if order.status.value == 'pending' %}
                                        <span class="badge bg-warning">En Attente</span>
                                        {% elif order.status.value == 'processing' %}
                                        <span class="badge bg-info">En Traitement</span>
                                        {% elif order.status.value == 'shipped' %}
                                        <span class="badge bg-primary">Expédiée</span>
                                        {% elif order.status.value == 'delivered' %}
                                        <span class="badge bg-success">Livrée</span>
                                        {% elif order.status.value == 'cancelled' %}
                                        <span class="badge bg-danger">Annulée</span>
                                        {% elif order.status.value == 'refunded' %}
                                        <span class="badge bg-secondary">Remboursée</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ order.created_at.strftime('%d/%m/%Y %H:%M') }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-info" 
                                                    onclick="viewOrderDetails({{ order.id }})">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            {% if order.customer and order.customer.phone %}
                                            <a href="https://wa.me/{{ order.customer.phone.replace('+', '').replace(' ', '') }}?text={{ 'Bonjour, concernant votre commande ' + (order.order_number or '#' + order.id|string) + ' sur Afroly.org'|urlencode }}" 
                                               target="_blank" class="btn btn-sm btn-outline-success">
                                                <i class="fab fa-whatsapp"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if orders.pages > 1 %}
                    <nav aria-label="Navigation des commandes" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if orders.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.manage_orders', 
                                    search=search, status=status_filter, page=orders.prev_num) }}">
                                    <i class="fas fa-chevron-left me-1"></i>Précédent
                                </a>
                            </li>
                            {% endif %}

                            {% for page_num in orders.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != orders.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.manage_orders', 
                                            search=search, status=status_filter, page=page_num) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if orders.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.manage_orders', 
                                    search=search, status=status_filter, page=orders.next_num) }}">
                                    Suivant<i class="fas fa-chevron-right ms-1"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-shopping-cart fa-4x text-muted mb-4"></i>
                        <h4>Aucune commande trouvée</h4>
                        <p class="text-muted">Aucune commande ne correspond à vos critères de recherche.</p>
                        <a href="{{ url_for('admin.manage_orders') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Effacer les Filtres
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Order Details Modal -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails de la Commande</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="orderDetailsContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function viewOrderDetails(orderId) {
    const modal = new bootstrap.Modal(document.getElementById('orderDetailsModal'));
    const content = document.getElementById('orderDetailsContent');
    
    // Show loading
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>
    `;
    
    modal.show();
    
    // Load order details
    fetch(`/shop/order/details/${orderId}`)
        .then(response => response.text())
        .then(html => {
            content.innerHTML = html;
        })
        .catch(error => {
            content.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Erreur lors du chargement des détails de la commande.
                </div>
            `;
        });
}
</script>
{% endblock %}
