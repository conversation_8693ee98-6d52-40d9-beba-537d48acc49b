{% extends "base.html" %}

{% block title %}Paramètres Admin - Afroly.org{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-6 fw-bold">
                <i class="fas fa-cog me-3 text-secondary"></i>Paramètres Administrateur
            </h1>
            <p class="lead text-muted">Configuration de la plateforme Afroly.org</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Retour au Dashboard
            </a>
        </div>
    </div>

    <!-- Settings Sections -->
    <div class="row">
        <!-- Platform Settings -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-globe me-2"></i>Paramètres de la Plateforme
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">Nom de la Plateforme</label>
                            <input type="text" class="form-control" name="platform_name" value="Afroly.org" readonly>
                            <small class="text-muted">Le nom de votre marketplace</small>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Email de Contact</label>
                            <input type="email" class="form-control" name="contact_email" value="<EMAIL>">
                            <small class="text-muted">Email principal pour les contacts</small>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Numéro WhatsApp</label>
                            <input type="text" class="form-control" name="whatsapp_number" value="+44 7951 658211">
                            <small class="text-muted">Numéro WhatsApp pour le support</small>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Devise par Défaut</label>
                            <select class="form-select" name="default_currency">
                                <option value="FCFA" selected>FCFA (Franc CFA)</option>
                                <option value="EUR">EUR (Euro)</option>
                                <option value="USD">USD (Dollar US)</option>
                            </select>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Sauvegarder
                        </button>
                    </form>

                    <!-- Email Settings Link -->
                    <div class="mt-4 pt-3 border-top">
                        <h6 class="text-muted mb-3">
                            <i class="fas fa-envelope me-2"></i>Configuration Email
                        </h6>
                        <div class="d-grid gap-2">
                            <a href="{{ url_for('email.settings') }}" class="btn btn-outline-primary">
                                <i class="fas fa-cog me-2"></i>Paramètres SMTP & Email
                            </a>
                            <a href="{{ url_for('email.templates') }}" class="btn btn-outline-info">
                                <i class="fas fa-file-alt me-2"></i>Templates Email
                            </a>
                            <a href="{{ url_for('email.newsletters') }}" class="btn btn-outline-success">
                                <i class="fas fa-newspaper me-2"></i>Newsletters
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Product Limits Settings -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-box me-2"></i>Limites de Produits par Tier
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">Limite Gratuit</label>
                            <input type="number" class="form-control" name="free_product_limit" value="{{ free_limit }}" min="1" max="1000">
                            <small class="text-muted">Nombre maximum de produits pour les utilisateurs gratuits</small>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Limite Premium</label>
                            <input type="number" class="form-control" name="premium_product_limit" value="{{ premium_limit }}" min="1" max="1000">
                            <small class="text-muted">Nombre maximum de produits pour les utilisateurs premium</small>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Limite Gold</label>
                            <input type="number" class="form-control" name="gold_product_limit" value="{{ gold_limit }}" min="1" max="1000">
                            <small class="text-muted">Nombre maximum de produits pour les utilisateurs gold</small>
                        </div>

                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>Mettre à Jour les Limites
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- User Management Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-users me-2"></i>Gestion des Utilisateurs
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Utilisateur</th>
                                    <th>Email</th>
                                    <th>Tier</th>
                                    <th>Produits</th>
                                    <th>Limite Personnalisée</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ user.get_full_name() }}</strong>
                                            <br><small class="text-muted">{{ user.role.value.title() }}</small>
                                        </div>
                                    </td>
                                    <td>{{ user.email }}</td>
                                    <td>
                                        <span class="badge bg-{% if user.tier.value == 'gold' %}warning{% elif user.tier.value == 'premium' %}info{% else %}secondary{% endif %}">
                                            {{ user.tier.value.title() }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if user.role.value == 'vendor' %}
                                        {{ user.shops|map(attribute='products')|map('length')|sum }}
                                        {% else %}
                                        N/A
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.role.value == 'vendor' %}
                                        <input type="number" class="form-control form-control-sm"
                                               id="limit_{{ user.id }}"
                                               value="{{ user.custom_product_limit or '' }}"
                                               placeholder="Défaut: {{ get_default_limit(user.tier.value) }}"
                                               min="0" max="1000" style="width: 120px;">
                                        {% else %}
                                        N/A
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.role.value == 'vendor' %}
                                        <button class="btn btn-sm btn-primary"
                                                onclick="updateUserLimit({{ user.id }})">
                                            <i class="fas fa-save"></i>
                                        </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Blog Management Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-blog me-2"></i>Gestion du Blog
                    </h6>
                    <div>
                        <a href="{{ url_for('admin.manage_blog') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-list me-2"></i>Voir Tous les Articles
                        </a>
                        <a href="{{ url_for('admin.create_blog_post') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-plus me-2"></i>Nouvel Article
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary">{{ blog_stats.total_posts }}</h4>
                                <small class="text-muted">Articles Totaux</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success">{{ blog_stats.published_posts }}</h4>
                                <small class="text-muted">Articles Publiés</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning">{{ blog_stats.draft_posts }}</h4>
                                <small class="text-muted">Brouillons</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info">{{ blog_stats.total_views }}</h4>
                                <small class="text-muted">Vues Totales</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function updateUserLimit(userId) {
    const limitInput = document.getElementById(`limit_${userId}`);
    const limit = limitInput.value.trim();

    fetch(`/admin/users/${userId}/update-limit`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            limit: limit === '' ? null : parseInt(limit)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show';
            alert.innerHTML = `
                ${data.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('.container-fluid').insertBefore(alert, document.querySelector('.container-fluid').firstChild);

            // Auto-dismiss after 3 seconds
            setTimeout(() => {
                alert.remove();
            }, 3000);
        } else {
            alert('Erreur: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Erreur lors de la mise à jour de la limite.');
    });
}
</script>
{% endblock %}
