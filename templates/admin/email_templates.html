{% extends "admin/base.html" %}

{% block title %}Templates Email - Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>Templates Email
                    </h5>
                    <div>
                        <a href="{{ url_for('email.settings') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-cog me-1"></i>Paramètres SMTP
                        </a>
                        <a href="{{ url_for('email.create_template') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>Nouveau Template
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if templates %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Nom</th>
                                    <th>Type</th>
                                    <th>Sujet</th>
                                    <th>Statut</th>
                                    <th>Dernière Modification</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for template in templates %}
                                <tr>
                                    <td>
                                        <strong>{{ template.name }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if template.email_type.value == 'welcome' %}success{% elif template.email_type.value == 'newsletter' %}info{% elif 'shop' in template.email_type.value %}primary{% elif 'order' in template.email_type.value %}warning{% else %}secondary{% endif %}">
                                            {{ template.email_type.value.replace('_', ' ').title() }}
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ template.subject[:50] }}{% if template.subject|length > 50 %}...{% endif %}</small>
                                    </td>
                                    <td>
                                        {% if template.is_active %}
                                        <span class="badge bg-success">Actif</span>
                                        {% else %}
                                        <span class="badge bg-secondary">Inactif</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ template.updated_at.strftime('%d/%m/%Y %H:%M') }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('email.edit_template', template_id=template.id) }}" 
                                               class="btn btn-outline-primary" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-outline-info" 
                                                    onclick="previewTemplate({{ template.id }})" title="Aperçu">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" 
                                                    onclick="testTemplate({{ template.id }})" title="Test">
                                                <i class="fas fa-paper-plane"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucun template email</h5>
                        <p class="text-muted">Créez votre premier template email pour commencer.</p>
                        <a href="{{ url_for('email.create_template') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Créer un Template
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Email Types Info -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Types d'Email Disponibles
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Emails Utilisateur</h6>
                            <ul class="list-unstyled">
                                <li><span class="badge bg-success me-2">WELCOME</span>Email de bienvenue</li>
                                <li><span class="badge bg-info me-2">NEWSLETTER</span>Newsletter</li>
                                <li><span class="badge bg-secondary me-2">SYSTEM_NOTIFICATION</span>Notification système</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">Emails Boutique</h6>
                            <ul class="list-unstyled">
                                <li><span class="badge bg-primary me-2">SHOP_CREATED</span>Boutique créée</li>
                                <li><span class="badge bg-success me-2">SHOP_APPROVED</span>Boutique approuvée</li>
                                <li><span class="badge bg-danger me-2">SHOP_REJECTED</span>Boutique rejetée</li>
                                <li><span class="badge bg-info me-2">PRODUCT_APPROVED</span>Produit approuvé</li>
                                <li><span class="badge bg-warning me-2">PRODUCT_REJECTED</span>Produit rejeté</li>
                            </ul>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6 class="text-primary">Emails Commande</h6>
                            <ul class="list-unstyled">
                                <li><span class="badge bg-warning me-2">ORDER_CONFIRMATION</span>Confirmation commande</li>
                                <li><span class="badge bg-info me-2">ORDER_SHIPPED</span>Commande expédiée</li>
                                <li><span class="badge bg-success me-2">ORDER_DELIVERED</span>Commande livrée</li>
                                <li><span class="badge bg-primary me-2">PAYMENT_CONFIRMATION</span>Confirmation paiement</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">Variables Disponibles</h6>
                            <small class="text-muted">
                                Utilisez ces variables dans vos templates :<br>
                                <code>{{ user_name }}</code>, <code>{{ user_email }}</code>, <code>{{ shop_name }}</code>,<br>
                                <code>{{ platform_name }}</code>, <code>{{ order_number }}</code>, etc.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Aperçu du Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- Preview content will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function previewTemplate(templateId) {
    fetch(`/admin/email/templates/${templateId}/preview`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('previewContent').innerHTML = html;
            new bootstrap.Modal(document.getElementById('previewModal')).show();
        })
        .catch(error => {
            alert('Erreur lors du chargement de l\'aperçu');
        });
}

function testTemplate(templateId) {
    if (confirm('Envoyer un email de test à votre adresse ?')) {
        fetch(`/admin/email/templates/${templateId}/test`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Email de test envoyé avec succès !');
            } else {
                alert('Erreur : ' + data.message);
            }
        })
        .catch(error => {
            alert('Erreur lors de l\'envoi du test');
        });
    }
}
</script>
{% endblock %}
