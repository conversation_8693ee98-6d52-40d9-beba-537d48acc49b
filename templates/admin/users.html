{% extends "base.html" %}

{% block title %}Gestion des Utilisateurs - Admin - Afroly.org{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-6 fw-bold">
                <i class="fas fa-users me-3 text-primary"></i>Gestion des Utilisateurs
            </h1>
            <p class="lead text-muted">Gérer tous les utilisateurs de la plateforme</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Retour au Dashboard
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Recherche</label>
                            <input type="text" class="form-control" name="search" value="{{ search }}"
                                   placeholder="Nom, email...">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Rôle</label>
                            <select class="form-select" name="role">
                                <option value="all" {% if role_filter == 'all' %}selected{% endif %}>Tous</option>
                                <option value="admin" {% if role_filter == 'admin' %}selected{% endif %}>Admin</option>
                                <option value="vendor" {% if role_filter == 'vendor' %}selected{% endif %}>Vendeur</option>
                                <option value="shopper" {% if role_filter == 'shopper' %}selected{% endif %}>Acheteur</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Niveau</label>
                            <select class="form-select" name="tier">
                                <option value="all" {% if tier_filter == 'all' %}selected{% endif %}>Tous</option>
                                <option value="free" {% if tier_filter == 'free' %}selected{% endif %}>Gratuit</option>
                                <option value="premium" {% if tier_filter == 'premium' %}selected{% endif %}>Premium</option>
                                <option value="gold" {% if tier_filter == 'gold' %}selected{% endif %}>Gold</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Statut</label>
                            <select class="form-select" name="status">
                                <option value="all" {% if status_filter == 'all' %}selected{% endif %}>Tous</option>
                                <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Actifs</option>
                                <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>Inactifs</option>
                                <option value="expired" {% if status_filter == 'expired' %}selected{% endif %}>Expirés</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Filtrer
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-list me-2"></i>Liste des Utilisateurs
                        <span class="badge bg-primary ms-2">{{ users.total }} total</span>
                    </h6>
                </div>
                <div class="card-body">
                    {% if users.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Utilisateur</th>
                                    <th>Rôle</th>
                                    <th>Niveau</th>
                                    <th>Statut</th>
                                    <th>Date d'Expiration</th>
                                    <th>Inscription</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users.items %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-user-circle fa-2x text-primary me-3"></i>
                                            <div>
                                                <h6 class="mb-0">{{ user.get_full_name() }}</h6>
                                                <small class="text-muted">{{ user.email }}</small>
                                                {% if user.phone %}
                                                <br><small class="text-muted">{{ user.phone }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'danger' if user.role.value == 'admin' else 'success' if user.role.value == 'vendor' else 'primary' }}">
                                            {{ user.role.value.title() }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'warning' if user.tier.value == 'gold' else 'info' if user.tier.value == 'premium' else 'secondary' }}">
                                            {{ user.tier.value.title() }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if user.is_active %}
                                        <span class="badge bg-success">Actif</span>
                                        {% else %}
                                        <span class="badge bg-danger">Inactif</span>
                                        {% endif %}
                                        {% if user.email_verified %}
                                        <span class="badge bg-info">Vérifié</span>
                                        {% else %}
                                        <span class="badge bg-warning">Non vérifié</span>
                                        {% endif %}
                                        {% if user.expiration_date and user.is_expired() %}
                                        <br><span class="badge bg-danger mt-1">Expiré</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="expiration-date-container" data-user-id="{{ user.id }}">
                                            {% if user.expiration_date %}
                                            <input type="date" class="form-control form-control-sm expiration-date-input"
                                                   value="{{ user.expiration_date.strftime('%Y-%m-%d') }}"
                                                   onchange="updateExpiration({{ user.id }}, this.value)">
                                            <small class="text-muted">
                                                {% if user.is_expired() %}
                                                <span class="text-danger">Expiré le {{ user.expiration_date.strftime('%d/%m/%Y') }}</span>
                                                {% else %}
                                                Expire le {{ user.expiration_date.strftime('%d/%m/%Y') }}
                                                {% endif %}
                                            </small>
                                            {% else %}
                                            <input type="date" class="form-control form-control-sm expiration-date-input"
                                                   placeholder="Aucune expiration"
                                                   onchange="updateExpiration({{ user.id }}, this.value)">
                                            <small class="text-muted">Aucune expiration</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <small>{{ user.created_at.strftime('%d/%m/%Y') }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('admin.edit_user', user_id=user.id) }}"
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if user.role.value == 'vendor' %}
                                            <a href="{{ url_for('admin.manage_shops') }}?search={{ user.email }}"
                                               class="btn btn-sm btn-outline-success">
                                                <i class="fas fa-store"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if users.pages > 1 %}
                    <nav aria-label="Navigation des utilisateurs" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if users.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.manage_users',
                                    search=search, role=role_filter, tier=tier_filter, status=status_filter, page=users.prev_num) }}">
                                    <i class="fas fa-chevron-left me-1"></i>Précédent
                                </a>
                            </li>
                            {% endif %}

                            {% for page_num in users.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != users.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.manage_users',
                                            search=search, role=role_filter, tier=tier_filter, status=status_filter, page=page_num) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if users.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.manage_users',
                                    search=search, role=role_filter, tier=tier_filter, status=status_filter, page=users.next_num) }}">
                                    Suivant<i class="fas fa-chevron-right ms-1"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-4x text-muted mb-4"></i>
                        <h4>Aucun utilisateur trouvé</h4>
                        <p class="text-muted">Aucun utilisateur ne correspond à vos critères de recherche.</p>
                        <a href="{{ url_for('admin.manage_users') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Effacer les Filtres
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function updateExpiration(userId, expirationDate) {
    // Show loading indicator
    const container = document.querySelector(`[data-user-id="${userId}"]`);
    const input = container.querySelector('.expiration-date-input');
    const originalValue = input.value;

    // Add loading class
    input.classList.add('loading');
    input.disabled = true;

    fetch(`/admin/users/${userId}/update-expiration`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify({
            expiration_date: expirationDate
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            showToast('success', data.message);

            // Update the display text
            const small = container.querySelector('small');
            if (expirationDate) {
                const date = new Date(expirationDate);
                const formattedDate = date.toLocaleDateString('fr-FR');
                if (data.expired) {
                    small.innerHTML = `<span class="text-danger">Expiré le ${formattedDate}</span>`;
                } else {
                    small.innerHTML = `Expire le ${formattedDate}`;
                    small.className = 'text-muted';
                }
            } else {
                small.innerHTML = 'Aucune expiration';
                small.className = 'text-muted';
            }

            // If user was marked as expired, refresh the page to update status badges
            if (data.expired) {
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            }
        } else {
            // Show error message and revert value
            showToast('error', data.message);
            input.value = originalValue;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', 'Erreur de connexion');
        input.value = originalValue;
    })
    .finally(() => {
        // Remove loading state
        input.classList.remove('loading');
        input.disabled = false;
    });
}

function getCsrfToken() {
    // Get CSRF token from meta tag or form
    const token = document.querySelector('meta[name="csrf-token"]');
    return token ? token.getAttribute('content') : '';
}

function showToast(type, message) {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 5000);
}

// Add CSS for loading state
const style = document.createElement('style');
style.textContent = `
    .expiration-date-input.loading {
        opacity: 0.6;
        pointer-events: none;
    }
    .expiration-date-input.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        right: 10px;
        width: 16px;
        height: 16px;
        border: 2px solid #ccc;
        border-top: 2px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
