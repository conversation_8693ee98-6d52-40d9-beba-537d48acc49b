{% extends "base.html" %}

{% block title %}Gestion du Blog - Admin - Afroly.org{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-6 fw-bold">
                <i class="fas fa-blog me-3 text-info"></i>Gestion du Blog
            </h1>
            <p class="lead text-muted">Gérer les articles et le contenu du blog</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group" role="group">
                <a href="{{ url_for('admin.create_blog_post') }}" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>Nouvel Article
                </a>
            </div>
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-arrow-left me-2"></i>Dashboard
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ posts.total }}</h4>
                            <small>Articles Totaux</small>
                        </div>
                        <i class="fas fa-file-alt fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ posts.items|selectattr('is_published')|list|length }}</h4>
                            <small>Articles Publiés</small>
                        </div>
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-dark h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ posts.items|rejectattr('is_published')|list|length }}</h4>
                            <small>Brouillons</small>
                        </div>
                        <i class="fas fa-edit fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ posts.items|map(attribute='views')|sum }}</h4>
                            <small>Vues Totales</small>
                        </div>
                        <i class="fas fa-eye fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">Recherche</label>
                            <input type="text" class="form-control" name="search" value="{{ search }}"
                                   placeholder="Titre de l'article...">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Statut</label>
                            <select class="form-select" name="status">
                                <option value="all" {% if status_filter == 'all' %}selected{% endif %}>Tous</option>
                                <option value="published" {% if status_filter == 'published' %}selected{% endif %}>Publiés</option>
                                <option value="draft" {% if status_filter == 'draft' %}selected{% endif %}>Brouillons</option>
                                <option value="featured" {% if status_filter == 'featured' %}selected{% endif %}>En Vedette</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Filtrer
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Articles Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-list me-2"></i>Liste des Articles
                        <span class="badge bg-info ms-2">{{ posts.total }} total</span>
                    </h6>
                </div>
                <div class="card-body">
                    {% if posts.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Article</th>
                                    <th>Auteur</th>
                                    <th>Catégorie</th>
                                    <th>Statut</th>
                                    <th>Vues</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for post in posts.items %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if post.featured_image %}
                                            <img src="{{ post.featured_image }}" alt="{{ post.title }}"
                                                 class="rounded me-3" style="width: 60px; height: 40px; object-fit: cover;">
                                            {% else %}
                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center"
                                                 style="width: 60px; height: 40px;">
                                                <i class="fas fa-file-alt text-muted"></i>
                                            </div>
                                            {% endif %}
                                            <div>
                                                <h6 class="mb-0">{{ post.title }}</h6>
                                                <small class="text-muted">{{ (post.excerpt[:80] if post.excerpt else post.content[:80]) }}{% if (post.excerpt or post.content)|length > 80 %}...{% endif %}</small>
                                                {% if post.tags %}
                                                <div class="mt-1">
                                                    {% for tag in post.tags.split(',')[:3] %}
                                                    <span class="badge bg-light text-dark me-1">{{ tag.strip() }}</span>
                                                    {% endfor %}
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <h6 class="mb-0">{{ post.author.get_full_name() }}</h6>
                                            <small class="text-muted">{{ post.author.email }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        {% if post.category %}
                                        <span class="badge bg-secondary">{{ post.category }}</span>
                                        {% else %}
                                        <span class="text-muted">Non catégorisé</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if post.is_published %}
                                        <span class="badge bg-success">Publié</span>
                                        {% if post.is_featured %}
                                        <br><span class="badge bg-warning mt-1">En Vedette</span>
                                        {% endif %}
                                        {% else %}
                                        <span class="badge bg-secondary">Brouillon</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>{{ post.views or 0 }}</strong>
                                        <br><small class="text-muted">{{ (post.content|length / 200)|round|int }} min</small>
                                    </td>
                                    <td>
                                        <small>{{ post.created_at.strftime('%d/%m/%Y') }}</small>
                                        {% if post.published_at %}
                                        <br><small class="text-success">Publié: {{ post.published_at.strftime('%d/%m/%Y') }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <!-- View Post -->
                                            <a href="{{ url_for('blog.view_post', slug=post.slug) }}"
                                               class="btn btn-sm btn-outline-info" target="_blank"
                                               data-bs-toggle="tooltip" title="Voir l'article">
                                                <i class="fas fa-eye"></i>
                                            </a>

                                            <!-- Edit Post -->
                                            <a href="{{ url_for('admin.edit_blog_post', post_id=post.id) }}"
                                               class="btn btn-sm btn-outline-primary"
                                               data-bs-toggle="tooltip" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>

                                            <!-- Toggle Status -->
                                            {% if post.is_published %}
                                            <form method="POST" action="{{ url_for('admin.unpublish_blog_post', post_id=post.id) }}"
                                                  style="display: inline;">
                                                <button type="submit" class="btn btn-sm btn-outline-warning"
                                                        onclick="return confirm('Dépublier cet article ?')"
                                                        data-bs-toggle="tooltip" title="Dépublier">
                                                    <i class="fas fa-eye-slash"></i>
                                                </button>
                                            </form>
                                            {% else %}
                                            <form method="POST" action="{{ url_for('admin.publish_blog_post', post_id=post.id) }}"
                                                  style="display: inline;">
                                                <button type="submit" class="btn btn-sm btn-outline-success"
                                                        onclick="return confirm('Publier cet article ?')"
                                                        data-bs-toggle="tooltip" title="Publier">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                            {% endif %}

                                            <!-- Delete Post -->
                                            <button class="btn btn-sm btn-outline-danger"
                                                    onclick="deleteBlogPost({{ post.id }}, '{{ post.title }}')"
                                                    data-bs-toggle="tooltip" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if posts.pages > 1 %}
                    <nav aria-label="Navigation des articles" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if posts.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.manage_blog',
                                    search=search, status=status_filter, page=posts.prev_num) }}">
                                    <i class="fas fa-chevron-left me-1"></i>Précédent
                                </a>
                            </li>
                            {% endif %}

                            {% for page_num in posts.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != posts.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.manage_blog',
                                            search=search, status=status_filter, page=page_num) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if posts.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.manage_blog',
                                    search=search, status=status_filter, page=posts.next_num) }}">
                                    Suivant<i class="fas fa-chevron-right ms-1"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-blog fa-4x text-muted mb-4"></i>
                        <h4>Aucun article trouvé</h4>
                        <p class="text-muted">Commencez par créer votre premier article de blog.</p>
                        <a href="{{ url_for('admin.create_blog_post') }}" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Créer un Article
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteBlogModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>Supprimer l'Article
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer cet article ?</p>
                <p class="text-danger"><strong>Cette action est irréversible.</strong></p>
                <div id="blogPostTitle" class="alert alert-light"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBlog">Supprimer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentPostId = null;

function deleteBlogPost(postId, postTitle) {
    currentPostId = postId;
    document.getElementById('blogPostTitle').textContent = postTitle;
    const modal = new bootstrap.Modal(document.getElementById('deleteBlogModal'));
    modal.show();
}

document.getElementById('confirmDeleteBlog').addEventListener('click', function() {
    if (currentPostId) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/blog/posts/${currentPostId}/delete`;
        document.body.appendChild(form);
        form.submit();
    }
});

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
