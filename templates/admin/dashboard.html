{% extends "base.html" %}

{% block title %}Tableau de Bord Admin - Afroly.org{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-5 fw-bold">
                <i class="fas fa-tachometer-alt me-3 text-primary"></i>Tableau de Bord Administrateur
            </h1>
            <p class="lead text-muted">Vue d'ensemble de la plateforme Afroly.org</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group" role="group">
                <a href="{{ url_for('admin.manage_users') }}" class="btn btn-outline-primary">
                    <i class="fas fa-users me-2"></i>Utilisateurs
                </a>
                <a href="{{ url_for('admin.manage_shops') }}" class="btn btn-outline-success">
                    <i class="fas fa-store me-2"></i>Boutiques
                </a>
                <a href="{{ url_for('admin.manage_products') }}" class="btn btn-outline-info">
                    <i class="fas fa-box me-2"></i>Produits
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_users }}</h4>
                            <small>Utilisateurs Totaux</small>
                        </div>
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_vendors }}</h4>
                            <small>Vendeurs</small>
                        </div>
                        <i class="fas fa-user-tie fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_shops }}</h4>
                            <small>Boutiques</small>
                        </div>
                        <i class="fas fa-store fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ total_products }}</h4>
                            <small>Produits</small>
                        </div>
                        <i class="fas fa-box fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Secondary Stats -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <h5 class="text-success">{{ active_shops }}</h5>
                    <small class="text-muted">Boutiques Actives</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <h5 class="text-warning">{{ pending_shops }}</h5>
                    <small class="text-muted">Boutiques en Attente</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <h5 class="text-info">{{ active_products }}</h5>
                    <small class="text-muted">Produits Actifs</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-secondary">
                <div class="card-body text-center">
                    <h5 class="text-secondary">{{ pending_products }}</h5>
                    <small class="text-muted">Produits en Attente</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <!-- Recent Users -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>Nouveaux Utilisateurs
                    </h6>
                </div>
                <div class="card-body">
                    {% if recent_users %}
                    {% for user in recent_users %}
                    <div class="d-flex align-items-center mb-3 {% if not loop.last %}border-bottom pb-3{% endif %}">
                        <div class="me-3">
                            <i class="fas fa-user-circle fa-2x text-primary"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ user.get_full_name() }}</h6>
                            <small class="text-muted">{{ user.email }}</small>
                            <div>
                                <span class="badge bg-{{ 'success' if user.role.value == 'vendor' else 'primary' }}">
                                    {{ user.role.value.title() }}
                                </span>
                                <span class="badge bg-secondary">{{ user.tier.value.title() }}</span>
                            </div>
                        </div>
                        <small class="text-muted">{{ user.created_at.strftime('%d/%m') }}</small>
                    </div>
                    {% endfor %}
                    {% else %}
                    <p class="text-muted text-center">Aucun nouvel utilisateur</p>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('admin.manage_users') }}" class="btn btn-sm btn-outline-primary w-100">
                        Voir Tous les Utilisateurs
                    </a>
                </div>
            </div>
        </div>

        <!-- Recent Shops -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-store me-2"></i>Nouvelles Boutiques
                    </h6>
                </div>
                <div class="card-body">
                    {% if recent_shops %}
                    {% for shop in recent_shops %}
                    <div class="d-flex align-items-center mb-3 {% if not loop.last %}border-bottom pb-3{% endif %}">
                        <div class="me-3">
                            {% if shop.logo %}
                            <img src="/static/uploads/shops/{{ shop.logo }}" alt="{{ shop.name }}"
                                 class="rounded" style="width: 40px; height: 40px; object-fit: cover;">
                            {% else %}
                            <i class="fas fa-store fa-2x text-success"></i>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ shop.name }}</h6>
                            <small class="text-muted">{{ shop.owner.get_full_name() }}</small>
                            <div>
                                <span class="badge bg-{{ 'success' if shop.status.value == 'active' else 'warning' }}">
                                    {{ shop.status.value.title() }}
                                </span>
                            </div>
                        </div>
                        <small class="text-muted">{{ shop.created_at.strftime('%d/%m') }}</small>
                    </div>
                    {% endfor %}
                    {% else %}
                    <p class="text-muted text-center">Aucune nouvelle boutique</p>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('admin.manage_shops') }}" class="btn btn-sm btn-outline-success w-100">
                        Voir Toutes les Boutiques
                    </a>
                </div>
            </div>
        </div>

        <!-- Recent Orders -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>Commandes Récentes
                    </h6>
                </div>
                <div class="card-body">
                    {% if recent_orders %}
                    {% for order in recent_orders %}
                    <div class="d-flex align-items-center mb-3 {% if not loop.last %}border-bottom pb-3{% endif %}">
                        <div class="me-3">
                            <i class="fas fa-receipt fa-2x text-info"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ order.order_number or '#' + order.id|string }}</h6>
                            <small class="text-muted">{{ order.shop.name }}</small>
                            <div>
                                <span class="badge bg-{{ 'warning' if order.status.value == 'pending' else 'success' }}">
                                    {{ order.status.value.title() }}
                                </span>
                                <span class="text-primary fw-bold">{{ "%.0f"|format(order.total) }} FCFA</span>
                            </div>
                        </div>
                        <small class="text-muted">{{ order.created_at.strftime('%d/%m') }}</small>
                    </div>
                    {% endfor %}
                    {% else %}
                    <p class="text-muted text-center">Aucune commande récente</p>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('admin.manage_orders') }}" class="btn btn-sm btn-outline-info w-100">
                        Voir Toutes les Commandes
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <h6 class="card-title">Actions Rapides</h6>
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="{{ url_for('admin.manage_users') }}" class="btn btn-outline-primary">
                            <i class="fas fa-users me-2"></i>Gérer les Utilisateurs
                        </a>
                        <a href="{{ url_for('admin.manage_shops') }}" class="btn btn-outline-success">
                            <i class="fas fa-store me-2"></i>Gérer les Boutiques
                        </a>
                        <a href="{{ url_for('admin.manage_products') }}" class="btn btn-outline-info">
                            <i class="fas fa-box me-2"></i>Gérer les Produits
                        </a>
                        <a href="{{ url_for('admin.manage_categories') }}" class="btn btn-outline-warning">
                            <i class="fas fa-tags me-2"></i>Gérer les Catégories
                        </a>
                        <a href="{{ url_for('admin.manage_ads') }}" class="btn btn-outline-danger">
                            <i class="fas fa-ad me-2"></i>Gérer les Publicités
                        </a>
                        <a href="{{ url_for('admin.manage_credit_packages') }}" class="btn btn-outline-warning">
                            <i class="fas fa-coins me-2"></i>Packages de Crédits
                        </a>
                        <a href="{{ url_for('admin.manage_credits') }}" class="btn btn-outline-primary">
                            <i class="fas fa-credit-card me-2"></i>Approbation Crédits
                        </a>
                        <a href="{{ url_for('admin.statistics') }}" class="btn btn-outline-info">
                            <i class="fas fa-chart-bar me-2"></i>Statistiques Détaillées
                        </a>
                        <a href="{{ url_for('admin.settings') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-cog me-2"></i>Paramètres
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
