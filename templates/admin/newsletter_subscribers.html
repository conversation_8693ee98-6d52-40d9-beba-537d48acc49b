{% extends "base.html" %}

{% block title %}Abonnés Newsletter - Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>Abonnés Newsletter
                    </h5>
                    <div>
                        <a href="{{ url_for('email.newsletters') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-newspaper me-1"></i>Newsletters
                        </a>
                        <button class="btn btn-outline-primary btn-sm" onclick="exportSubscribers()">
                            <i class="fas fa-download me-1"></i>Exporter
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form method="GET" class="d-flex">
                                <select name="status" class="form-select me-2">
                                    <option value="">Tous les statuts</option>
                                    <option value="active" {% if request.args.get('status') == 'active' %}selected{% endif %}>
                                        Actifs
                                    </option>
                                    <option value="inactive" {% if request.args.get('status') == 'inactive' %}selected{% endif %}>
                                        Inactifs
                                    </option>
                                </select>
                                <input type="text" name="search" class="form-control me-2" 
                                       placeholder="Rechercher par email..." 
                                       value="{{ request.args.get('search', '') }}">
                                <button type="submit" class="btn btn-outline-primary">Filtrer</button>
                            </form>
                        </div>
                        <div class="col-md-6 text-end">
                            <small class="text-muted">
                                Page {{ subscribers.page }} sur {{ subscribers.pages }} 
                                ({{ subscribers.total }} abonnés au total)
                            </small>
                        </div>
                    </div>

                    {% if subscribers.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>Email</th>
                                    <th>Nom</th>
                                    <th>Statut</th>
                                    <th>Fréquence</th>
                                    <th>Date d'Inscription</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for subscriber in subscribers.items %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="subscriber-checkbox" value="{{ subscriber.id }}">
                                    </td>
                                    <td>
                                        <strong>{{ subscriber.email }}</strong>
                                        {% if subscriber.user %}
                                        <br><small class="text-muted">
                                            <i class="fas fa-user me-1"></i>Utilisateur enregistré
                                        </small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ subscriber.name or '-' }}
                                        {% if subscriber.user %}
                                        <br><small class="text-muted">{{ subscriber.user.role.value.title() }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if subscriber.is_active %}
                                        <span class="badge bg-success">Actif</span>
                                        {% else %}
                                        <span class="badge bg-secondary">Inactif</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if subscriber.frequency == 'daily' %}info{% elif subscriber.frequency == 'weekly' %}primary{% else %}secondary{% endif %}">
                                            {{ subscriber.frequency.title() if subscriber.frequency else 'Weekly' }}
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ subscriber.subscribed_at.strftime('%d/%m/%Y %H:%M') }}
                                            {% if subscriber.unsubscribed_at %}
                                            <br>Désabonné: {{ subscriber.unsubscribed_at.strftime('%d/%m/%Y') }}
                                            {% endif %}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            {% if subscriber.is_active %}
                                            <button class="btn btn-outline-warning" 
                                                    onclick="unsubscribeUser({{ subscriber.id }})" title="Désabonner">
                                                <i class="fas fa-user-times"></i>
                                            </button>
                                            {% else %}
                                            <button class="btn btn-outline-success" 
                                                    onclick="resubscribeUser({{ subscriber.id }})" title="Réabonner">
                                                <i class="fas fa-user-check"></i>
                                            </button>
                                            {% endif %}
                                            <button class="btn btn-outline-info" 
                                                    onclick="viewSubscriber({{ subscriber.id }})" title="Voir détails">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" 
                                                    onclick="deleteSubscriber({{ subscriber.id }})" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Bulk Actions -->
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <span class="me-3">Actions groupées :</span>
                                <button class="btn btn-outline-success btn-sm me-2" onclick="bulkResubscribe()">
                                    <i class="fas fa-user-check me-1"></i>Réabonner
                                </button>
                                <button class="btn btn-outline-warning btn-sm me-2" onclick="bulkUnsubscribe()">
                                    <i class="fas fa-user-times me-1"></i>Désabonner
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="bulkDelete()">
                                    <i class="fas fa-trash me-1"></i>Supprimer
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Pagination -->
                    {% if subscribers.pages > 1 %}
                    <nav aria-label="Pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if subscribers.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('email.subscribers', page=subscribers.prev_num) }}">Précédent</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in subscribers.iter_pages() %}
                            {% if page_num %}
                            {% if page_num != subscribers.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('email.subscribers', page=page_num) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                            {% endfor %}
                            
                            {% if subscribers.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('email.subscribers', page=subscribers.next_num) }}">Suivant</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucun abonné trouvé</h5>
                        <p class="text-muted">
                            {% if request.args.get('search') or request.args.get('status') %}
                            Aucun abonné ne correspond aux critères de recherche.
                            {% else %}
                            Aucun abonné à la newsletter pour le moment.
                            {% endif %}
                        </p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Subscription Statistics -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-success">{{ subscribers.items|selectattr('is_active', 'equalto', true)|list|length }}</h3>
                    <small class="text-muted">Abonnés Actifs</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-secondary">{{ subscribers.items|selectattr('is_active', 'equalto', false)|list|length }}</h3>
                    <small class="text-muted">Désabonnés</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-info">{{ subscribers.items|selectattr('user', 'defined')|list|length }}</h3>
                    <small class="text-muted">Utilisateurs Enregistrés</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-primary">{{ subscribers.total }}</h3>
                    <small class="text-muted">Total Abonnés</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Subscriber Details Modal -->
<div class="modal fade" id="subscriberModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails de l'Abonné</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="subscriberDetails">
                <!-- Subscriber details will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.subscriber-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

function getSelectedSubscribers() {
    const checkboxes = document.querySelectorAll('.subscriber-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

function unsubscribeUser(subscriberId) {
    if (confirm('Désabonner cet utilisateur de la newsletter ?')) {
        fetch(`/admin/email/subscribers/${subscriberId}/unsubscribe`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur : ' + data.message);
            }
        });
    }
}

function resubscribeUser(subscriberId) {
    if (confirm('Réabonner cet utilisateur à la newsletter ?')) {
        fetch(`/admin/email/subscribers/${subscriberId}/resubscribe`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur : ' + data.message);
            }
        });
    }
}

function deleteSubscriber(subscriberId) {
    if (confirm('Supprimer définitivement cet abonné ? Cette action est irréversible.')) {
        fetch(`/admin/email/subscribers/${subscriberId}/delete`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur : ' + data.message);
            }
        });
    }
}

function viewSubscriber(subscriberId) {
    fetch(`/admin/email/subscribers/${subscriberId}/details`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('subscriberDetails').innerHTML = `
                <div class="row">
                    <div class="col-sm-4"><strong>Email:</strong></div>
                    <div class="col-sm-8">${data.email}</div>
                </div>
                <div class="row mt-2">
                    <div class="col-sm-4"><strong>Nom:</strong></div>
                    <div class="col-sm-8">${data.name || '-'}</div>
                </div>
                <div class="row mt-2">
                    <div class="col-sm-4"><strong>Statut:</strong></div>
                    <div class="col-sm-8">
                        <span class="badge bg-${data.is_active ? 'success' : 'secondary'}">
                            ${data.is_active ? 'Actif' : 'Inactif'}
                        </span>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-sm-4"><strong>Fréquence:</strong></div>
                    <div class="col-sm-8">${data.frequency || 'Weekly'}</div>
                </div>
                <div class="row mt-2">
                    <div class="col-sm-4"><strong>Inscription:</strong></div>
                    <div class="col-sm-8">${new Date(data.subscribed_at).toLocaleDateString('fr-FR')}</div>
                </div>
                ${data.unsubscribed_at ? `
                <div class="row mt-2">
                    <div class="col-sm-4"><strong>Désabonnement:</strong></div>
                    <div class="col-sm-8">${new Date(data.unsubscribed_at).toLocaleDateString('fr-FR')}</div>
                </div>
                ` : ''}
                ${data.user ? `
                <div class="row mt-2">
                    <div class="col-sm-4"><strong>Utilisateur:</strong></div>
                    <div class="col-sm-8">
                        ${data.user.first_name} ${data.user.last_name}
                        <br><small class="text-muted">${data.user.role}</small>
                    </div>
                </div>
                ` : ''}
            `;
            new bootstrap.Modal(document.getElementById('subscriberModal')).show();
        });
}

function bulkResubscribe() {
    const selected = getSelectedSubscribers();
    if (selected.length === 0) {
        alert('Veuillez sélectionner au moins un abonné');
        return;
    }
    
    if (confirm(`Réabonner ${selected.length} utilisateur(s) ?`)) {
        fetch('/admin/email/subscribers/bulk-resubscribe', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ subscriber_ids: selected })
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            location.reload();
        });
    }
}

function bulkUnsubscribe() {
    const selected = getSelectedSubscribers();
    if (selected.length === 0) {
        alert('Veuillez sélectionner au moins un abonné');
        return;
    }
    
    if (confirm(`Désabonner ${selected.length} utilisateur(s) ?`)) {
        fetch('/admin/email/subscribers/bulk-unsubscribe', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ subscriber_ids: selected })
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            location.reload();
        });
    }
}

function bulkDelete() {
    const selected = getSelectedSubscribers();
    if (selected.length === 0) {
        alert('Veuillez sélectionner au moins un abonné');
        return;
    }
    
    if (confirm(`Supprimer définitivement ${selected.length} abonné(s) ? Cette action est irréversible.`)) {
        fetch('/admin/email/subscribers/bulk-delete', {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ subscriber_ids: selected })
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            location.reload();
        });
    }
}

function exportSubscribers() {
    window.open('/admin/email/subscribers/export', '_blank');
}
</script>
{% endblock %}
