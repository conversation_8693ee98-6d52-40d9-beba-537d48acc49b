{% extends "base.html" %}

{% block title %}Gestion des Emails - Admin{% endblock %}

{% block content %}
<!-- Email-specific Flash Messages -->
{% include 'components/email_flash_messages.html' %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-envelope me-3 text-primary"></i>Gestion des Emails
                    </h2>
                    <p class="text-muted mb-0">Centre de contrôle pour tous les emails et newsletters</p>
                </div>
                <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour au Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Email Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center border-primary">
                <div class="card-body">
                    <i class="fas fa-paper-plane fa-2x text-primary mb-2"></i>
                    <h3 class="text-primary">{{ email_stats.total_sent|default(0) }}</h3>
                    <small class="text-muted">Emails Envoyés</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-warning">
                <div class="card-body">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h3 class="text-warning">{{ email_stats.total_pending|default(0) }}</h3>
                    <small class="text-muted">En Attente</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-success">
                <div class="card-body">
                    <i class="fas fa-users fa-2x text-success mb-2"></i>
                    <h3 class="text-success">{{ email_stats.total_subscribers|default(0) }}</h3>
                    <small class="text-muted">Abonnés Newsletter</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-info">
                <div class="card-body">
                    <i class="fas fa-file-alt fa-2x text-info mb-2"></i>
                    <h3 class="text-info">{{ email_stats.total_templates|default(0) }}</h3>
                    <small class="text-muted">Templates</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Email Management Sections -->
    <div class="row">
        <!-- SMTP Configuration -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>Configuration SMTP
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Configurez vos paramètres de serveur email pour l'envoi automatique.</p>
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('email.settings') }}" class="btn btn-primary">
                            <i class="fas fa-server me-2"></i>Paramètres SMTP
                        </a>
                        <a href="{{ url_for('email.test_email') }}" class="btn btn-outline-primary">
                            <i class="fas fa-paper-plane me-2"></i>Tester la Configuration
                        </a>
                    </div>
                    <hr>
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Supports Gmail, Outlook, SendGrid et autres providers SMTP
                    </small>
                </div>
            </div>
        </div>

        <!-- Email Templates -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>Templates Email
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Gérez et personnalisez tous vos templates d'emails automatiques.</p>
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('email.templates') }}" class="btn btn-info">
                            <i class="fas fa-list me-2"></i>Voir tous les Templates
                        </a>
                        <a href="{{ url_for('email.create_template') }}" class="btn btn-outline-info">
                            <i class="fas fa-plus me-2"></i>Créer un Template
                        </a>
                    </div>
                    <hr>
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Bienvenue, boutique approuvée, commandes, etc.
                    </small>
                </div>
            </div>
        </div>

        <!-- Newsletter Management -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-newspaper me-2"></i>Newsletters
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Créez et envoyez des newsletters à vos utilisateurs et vendeurs.</p>
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('email.newsletters') }}" class="btn btn-success">
                            <i class="fas fa-newspaper me-2"></i>Gérer les Newsletters
                        </a>
                        <a href="{{ url_for('email.create_newsletter') }}" class="btn btn-outline-success">
                            <i class="fas fa-plus me-2"></i>Créer une Newsletter
                        </a>
                        <a href="{{ url_for('email.subscribers') }}" class="btn btn-outline-success">
                            <i class="fas fa-users me-2"></i>Gérer les Abonnés
                        </a>
                    </div>
                    <hr>
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Ciblage par audience : tous, vendeurs, acheteurs
                    </small>
                </div>
            </div>
        </div>

        <!-- Email Queue -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>File d'Attente
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Surveillez et gérez la file d'attente des emails en cours d'envoi.</p>
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('email.queue') }}" class="btn btn-warning">
                            <i class="fas fa-eye me-2"></i>Voir la File d'Attente
                        </a>
                        <a href="{{ url_for('email.process_queue') }}" class="btn btn-outline-warning">
                            <i class="fas fa-play me-2"></i>Traiter la File
                        </a>
                    </div>
                    <hr>
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Retry automatique et gestion des échecs
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Email Activity -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>Activité Récente
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_emails %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Type</th>
                                    <th>Destinataire</th>
                                    <th>Sujet</th>
                                    <th>Statut</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for email in recent_emails %}
                                <tr>
                                    <td>
                                        <span class="badge bg-{% if email.email_type.value == 'welcome' %}success{% elif email.email_type.value == 'newsletter' %}info{% else %}secondary{% endif %}">
                                            {{ email.email_type.value.replace('_', ' ').title() }}
                                        </span>
                                    </td>
                                    <td>{{ email.recipient_email }}</td>
                                    <td>{{ email.subject[:40] }}{% if email.subject|length > 40 %}...{% endif %}</td>
                                    <td>
                                        {% if email.status.value == 'sent' %}
                                        <span class="badge bg-success">Envoyé</span>
                                        {% elif email.status.value == 'pending' %}
                                        <span class="badge bg-warning">En attente</span>
                                        {% elif email.status.value == 'failed' %}
                                        <span class="badge bg-danger">Échec</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ email.created_at.strftime('%d/%m/%Y %H:%M') }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('email.queue') }}" class="btn btn-outline-primary btn-sm">
                            Voir tous les emails
                        </a>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">Aucune activité email récente</h6>
                        <p class="text-muted">Les emails envoyés apparaîtront ici.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Tips -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Conseils Rapides
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6 class="text-primary">Configuration Initiale</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success me-1"></i>Configurez vos paramètres SMTP</li>
                                <li><i class="fas fa-check text-success me-1"></i>Testez l'envoi d'emails</li>
                                <li><i class="fas fa-check text-success me-1"></i>Personnalisez vos templates</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-primary">Newsletters</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success me-1"></i>Créez du contenu engageant</li>
                                <li><i class="fas fa-check text-success me-1"></i>Segmentez votre audience</li>
                                <li><i class="fas fa-check text-success me-1"></i>Programmez vos envois</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-primary">Surveillance</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success me-1"></i>Surveillez la file d'attente</li>
                                <li><i class="fas fa-check text-success me-1"></i>Vérifiez les échecs d'envoi</li>
                                <li><i class="fas fa-check text-success me-1"></i>Analysez les statistiques</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Auto-refresh statistics every 30 seconds
setInterval(function() {
    fetch('/admin/email/stats')
        .then(response => response.json())
        .then(data => {
            // Update statistics cards
            document.querySelector('.border-primary h3').textContent = data.total_sent || 0;
            document.querySelector('.border-warning h3').textContent = data.total_pending || 0;
            document.querySelector('.border-success h3').textContent = data.total_subscribers || 0;
            document.querySelector('.border-info h3').textContent = data.total_templates || 0;
        })
        .catch(error => console.log('Stats update failed:', error));
}, 30000);
</script>
{% endblock %}
