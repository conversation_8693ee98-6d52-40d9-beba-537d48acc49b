{% extends "base.html" %}

{% block title %}Paramètres Email - Admin{% endblock %}

{% block content %}
<!-- Email-specific Flash Messages -->
{% include 'components/email_flash_messages.html' %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-envelope me-2"></i>Configuration Email & SMTP
                    </h5>
                    <div>
                        <a href="{{ url_for('email.test_email') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-paper-plane me-1"></i>Tester Email
                        </a>
                        <a href="{{ url_for('email.templates') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-file-alt me-1"></i>Templates
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <!-- SMTP Configuration -->
                            <div class="col-lg-6">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-server me-2"></i>Configuration SMTP
                                </h6>
                                
                                <div class="mb-3">
                                    <label class="form-label">Serveur SMTP</label>
                                    <input type="text" class="form-control" name="mail_server" 
                                           value="{{ smtp_settings.mail_server }}" 
                                           placeholder="smtp.gmail.com">
                                    <small class="text-muted">Exemple: smtp.gmail.com, smtp.outlook.com</small>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Port</label>
                                            <input type="number" class="form-control" name="mail_port" 
                                                   value="{{ smtp_settings.mail_port }}" 
                                                   placeholder="587">
                                            <small class="text-muted">587 (TLS) ou 465 (SSL)</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Sécurité</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="mail_use_tls" 
                                                       value="true" {{ 'checked' if smtp_settings.mail_use_tls == 'true' }}>
                                                <label class="form-check-label">Utiliser TLS</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="mail_use_ssl" 
                                                       value="true" {{ 'checked' if smtp_settings.mail_use_ssl == 'true' }}>
                                                <label class="form-check-label">Utiliser SSL</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Nom d'utilisateur</label>
                                    <input type="email" class="form-control" name="mail_username" 
                                           value="{{ smtp_settings.mail_username }}" 
                                           placeholder="<EMAIL>">
                                    <small class="text-muted">Votre adresse email complète</small>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Mot de passe</label>
                                    <input type="password" class="form-control" name="mail_password" 
                                           value="{{ smtp_settings.mail_password }}" 
                                           placeholder="Mot de passe ou mot de passe d'application">
                                    <small class="text-muted">Pour Gmail, utilisez un mot de passe d'application</small>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Email expéditeur par défaut</label>
                                    <input type="email" class="form-control" name="mail_default_sender" 
                                           value="{{ smtp_settings.mail_default_sender }}" 
                                           placeholder="<EMAIL>">
                                    <small class="text-muted">Adresse qui apparaîtra comme expéditeur</small>
                                </div>
                            </div>

                            <!-- Configuration Guide -->
                            <div class="col-lg-6">
                                <h6 class="text-success border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle me-2"></i>Guide de Configuration
                                </h6>

                                <div class="alert alert-info">
                                    <h6><i class="fas fa-lightbulb me-2"></i>Configurations Populaires</h6>
                                    
                                    <strong>Gmail :</strong><br>
                                    • Serveur: smtp.gmail.com<br>
                                    • Port: 587 (TLS)<br>
                                    • Utilisez un mot de passe d'application<br><br>

                                    <strong>Outlook/Hotmail :</strong><br>
                                    • Serveur: smtp-mail.outlook.com<br>
                                    • Port: 587 (TLS)<br><br>

                                    <strong>Yahoo :</strong><br>
                                    • Serveur: smtp.mail.yahoo.com<br>
                                    • Port: 587 (TLS)<br>
                                </div>

                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Important</h6>
                                    <ul class="mb-0">
                                        <li>Pour Gmail, activez l'authentification à 2 facteurs</li>
                                        <li>Créez un mot de passe d'application spécifique</li>
                                        <li>Testez toujours après configuration</li>
                                        <li>Vérifiez les limites d'envoi de votre fournisseur</li>
                                    </ul>
                                </div>

                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-chart-line me-2"></i>Statistiques Email
                                        </h6>
                                        <div class="row text-center">
                                            <div class="col-4">
                                                <div class="border-end">
                                                    <h4 class="text-primary mb-0">{{ email_stats.total_sent|default(0) }}</h4>
                                                    <small class="text-muted">Envoyés</small>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div class="border-end">
                                                    <h4 class="text-warning mb-0">{{ email_stats.pending|default(0) }}</h4>
                                                    <small class="text-muted">En attente</small>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <h4 class="text-danger mb-0">{{ email_stats.failed|default(0) }}</h4>
                                                <small class="text-muted">Échecs</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <a href="{{ url_for('email.queue') }}" class="btn btn-outline-secondary">
                                            <i class="fas fa-list me-1"></i>File d'attente
                                        </a>
                                        <a href="{{ url_for('email.newsletters') }}" class="btn btn-outline-info">
                                            <i class="fas fa-newspaper me-1"></i>Newsletters
                                        </a>
                                    </div>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>Sauvegarder Configuration
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
