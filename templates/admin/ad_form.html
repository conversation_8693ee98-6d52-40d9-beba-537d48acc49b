{% extends "admin/base.html" %}

{% block title %}{% if ad %}Modifier{% else %}<PERSON><PERSON>er{% endif %} une Publicité - Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">{% if ad %}Modifier{% else %}<PERSON><PERSON>er{% endif %} une Publicité</h1>
                    <p class="text-muted">{% if ad %}Modifiez les détails de la publicité{% else %}Créez une nouvelle publicité pour le site{% endif %}</p>
                </div>
                <a href="{{ url_for('admin.manage_ads') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour
                </a>
            </div>

            <!-- Form -->
            <div class="card">
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Nom de la publicité <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="{{ ad.name if ad else '' }}" required>
                                    <div class="form-text">Nom interne pour identifier la publicité</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="ad_type" class="form-label">Type de publicité <span class="text-danger">*</span></label>
                                    <select class="form-select" id="ad_type" name="ad_type" required onchange="toggleAdTypeFields()">
                                        <option value="">Sélectionnez un type</option>
                                        <option value="text" {% if ad and ad.ad_type.value == 'text' %}selected{% endif %}>Texte</option>
                                        <option value="image" {% if ad and ad.ad_type.value == 'image' %}selected{% endif %}>Image</option>
                                        <option value="html" {% if ad and ad.ad_type.value == 'html' %}selected{% endif %}>HTML</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Statut</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="draft" {% if not ad or ad.status.value == 'draft' %}selected{% endif %}>Brouillon</option>
                                        <option value="active" {% if ad and ad.status.value == 'active' %}selected{% endif %}>Actif</option>
                                        <option value="inactive" {% if ad and ad.status.value == 'inactive' %}selected{% endif %}>Inactif</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="display_order" class="form-label">Ordre d'affichage</label>
                                    <input type="number" class="form-control" id="display_order" name="display_order" 
                                           value="{{ ad.display_order if ad else 0 }}" min="0">
                                    <div class="form-text">Plus le nombre est élevé, plus la publicité sera affichée en premier</div>
                                </div>
                            </div>

                            <!-- Content Fields -->
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="title" class="form-label">Titre (optionnel)</label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="{{ ad.title if ad else '' }}">
                                </div>
                            </div>

                            <div class="col-12" id="content-field">
                                <div class="mb-3">
                                    <label for="content" class="form-label">Contenu</label>
                                    <textarea class="form-control" id="content" name="content" rows="5">{{ ad.content if ad else '' }}</textarea>
                                    <div class="form-text" id="content-help">Contenu de la publicité</div>
                                </div>
                            </div>

                            <div class="col-12" id="image-field" style="display: none;">
                                <div class="mb-3">
                                    <label for="image_url" class="form-label">URL de l'image</label>
                                    <input type="url" class="form-control" id="image_url" name="image_url" 
                                           value="{{ ad.image_url if ad else '' }}">
                                    <div class="form-text">URL complète de l'image à afficher</div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="link_url" class="form-label">URL de destination (optionnel)</label>
                                    <input type="url" class="form-control" id="link_url" name="link_url" 
                                           value="{{ ad.link_url if ad else '' }}">
                                    <div class="form-text">URL vers laquelle rediriger quand on clique sur la publicité</div>
                                </div>
                            </div>

                            <!-- Scheduling -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">Date de début (optionnel)</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" 
                                           value="{{ ad.start_date.strftime('%Y-%m-%d') if ad and ad.start_date else '' }}">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_date" class="form-label">Date de fin (optionnel)</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" 
                                           value="{{ ad.end_date.strftime('%Y-%m-%d') if ad and ad.end_date else '' }}">
                                </div>
                            </div>

                            <!-- Limits -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max_impressions" class="form-label">Limite d'impressions (optionnel)</label>
                                    <input type="number" class="form-control" id="max_impressions" name="max_impressions" 
                                           value="{{ ad.max_impressions if ad and ad.max_impressions else '' }}" min="1">
                                    <div class="form-text">Nombre maximum d'affichages de la publicité</div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('admin.manage_ads') }}" class="btn btn-secondary">Annuler</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{% if ad %}Mettre à jour{% else %}Créer{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleAdTypeFields() {
    const adType = document.getElementById('ad_type').value;
    const contentField = document.getElementById('content-field');
    const imageField = document.getElementById('image-field');
    const contentHelp = document.getElementById('content-help');
    const contentTextarea = document.getElementById('content');
    
    // Reset visibility
    contentField.style.display = 'block';
    imageField.style.display = 'none';
    
    if (adType === 'text') {
        contentHelp.textContent = 'Texte de la publicité';
        contentTextarea.placeholder = 'Entrez le texte de votre publicité...';
    } else if (adType === 'image') {
        imageField.style.display = 'block';
        contentField.style.display = 'none';
    } else if (adType === 'html') {
        contentHelp.textContent = 'Code HTML de la publicité';
        contentTextarea.placeholder = 'Entrez le code HTML de votre publicité...';
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleAdTypeFields();
});
</script>
{% endblock %}
