{% extends "base.html" %}

{% block title %}Gestion des Boutiques - Admin - Afroly.org{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-6 fw-bold">
                <i class="fas fa-store me-3 text-success"></i>Gestion des Boutiques
            </h1>
            <p class="lead text-muted">Gérer toutes les boutiques de la plateforme</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Retour au Dashboard
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">Recherche</label>
                            <input type="text" class="form-control" name="search" value="{{ search }}"
                                   placeholder="Nom de boutique...">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Statut</label>
                            <select class="form-select" name="status">
                                <option value="all" {% if status_filter == 'all' %}selected{% endif %}>Tous</option>
                                <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Active</option>
                                <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>En Attente</option>
                                <option value="suspended" {% if status_filter == 'suspended' %}selected{% endif %}>Suspendue</option>
                                <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>Rejetée</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Filtrer
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Shops Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-list me-2"></i>Liste des Boutiques
                        <span class="badge bg-success ms-2">{{ shops.total }} total</span>
                    </h6>
                </div>
                <div class="card-body">
                    {% if shops.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Boutique</th>
                                    <th>Propriétaire</th>
                                    <th>Statut</th>
                                    <th>Pays</th>
                                    <th>Création</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for shop in shops.items %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if shop.logo %}
                                            <img src="/static/uploads/shops/{{ shop.logo }}" alt="{{ shop.name }}"
                                                 class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                            {% else %}
                                            <i class="fas fa-store fa-2x text-success me-3"></i>
                                            {% endif %}
                                            <div>
                                                <h6 class="mb-0">{{ shop.name }}</h6>
                                                <small class="text-muted">{{ shop.description[:50] }}{% if shop.description|length > 50 %}...{% endif %}</small>
                                                {% if shop.whatsapp_number %}
                                                <br><small class="text-success">
                                                    <i class="fab fa-whatsapp me-1"></i>{{ shop.whatsapp_number }}
                                                </small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <h6 class="mb-0">{{ shop.owner.get_full_name() }}</h6>
                                            <small class="text-muted">{{ shop.owner.email }}</small>
                                            <br><span class="badge bg-{{ 'warning' if shop.owner.tier.value == 'gold' else 'info' if shop.owner.tier.value == 'premium' else 'secondary' }}">
                                                {{ shop.owner.tier.value.title() }}
                                            </span>
                                        </div>
                                    </td>
                                    <td>
                                        {% if shop.status.value == 'active' %}
                                        <span class="badge bg-success">Active</span>
                                        {% elif shop.status.value == 'pending' %}
                                        <span class="badge bg-warning">En Attente</span>
                                        {% elif shop.status.value == 'suspended' %}
                                        <span class="badge bg-danger">Suspendue</span>
                                        {% elif shop.status.value == 'rejected' %}
                                        <span class="badge bg-secondary">Rejetée</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">{{ shop.country or 'Non spécifié' }}</span>
                                    </td>
                                    <td>
                                        <small>{{ shop.created_at.strftime('%d/%m/%Y') }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('shop.view', slug=shop.slug) }}"
                                               class="btn btn-sm btn-outline-info" target="_blank">
                                                <i class="fas fa-eye"></i>
                                            </a>

                                            {% if shop.status.value == 'pending' %}
                                            <form method="POST" action="{{ url_for('admin.approve_shop', shop_id=shop.id) }}"
                                                  style="display: inline;">
                                                <button type="submit" class="btn btn-sm btn-outline-success"
                                                        onclick="return confirm('Approuver cette boutique ?')">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                            <form method="POST" action="{{ url_for('admin.reject_shop', shop_id=shop.id) }}"
                                                  style="display: inline;">
                                                <button type="submit" class="btn btn-sm btn-outline-danger"
                                                        onclick="return confirm('Rejeter cette boutique ?')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </form>
                                            {% elif shop.status.value == 'active' %}
                                            <form method="POST" action="{{ url_for('admin.suspend_shop', shop_id=shop.id) }}"
                                                  style="display: inline;">
                                                <button type="submit" class="btn btn-sm btn-outline-warning"
                                                        onclick="return confirm('Suspendre cette boutique ?')">
                                                    <i class="fas fa-pause"></i>
                                                </button>
                                            </form>
                                            {% elif shop.status.value == 'suspended' %}
                                            <form method="POST" action="{{ url_for('admin.approve_shop', shop_id=shop.id) }}"
                                                  style="display: inline;">
                                                <button type="submit" class="btn btn-sm btn-outline-success"
                                                        onclick="return confirm('Réactiver cette boutique ?')">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                            </form>
                                            {% endif %}

                                            <!-- Delete Button (Always Available) -->
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-danger dropdown-toggle"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <button class="dropdown-item text-danger"
                                                                onclick="deleteShop({{ shop.id }}, '{{ shop.name }}', {{ shop.products|length }}, {{ shop.orders|length }})">
                                                            <i class="fas fa-trash me-2"></i>Supprimer Définitivement
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if shops.pages > 1 %}
                    <nav aria-label="Navigation des boutiques" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if shops.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.manage_shops',
                                    search=search, status=status_filter, page=shops.prev_num) }}">
                                    <i class="fas fa-chevron-left me-1"></i>Précédent
                                </a>
                            </li>
                            {% endif %}

                            {% for page_num in shops.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != shops.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.manage_shops',
                                            search=search, status=status_filter, page=page_num) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if shops.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.manage_shops',
                                    search=search, status=status_filter, page=shops.next_num) }}">
                                    Suivant<i class="fas fa-chevron-right ms-1"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-store fa-4x text-muted mb-4"></i>
                        <h4>Aucune boutique trouvée</h4>
                        <p class="text-muted">Aucune boutique ne correspond à vos critères de recherche.</p>
                        <a href="{{ url_for('admin.manage_shops') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Effacer les Filtres
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteShopModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>Supprimer la Boutique
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-warning me-2"></i>
                    <strong>Attention !</strong> Cette action est irréversible.
                </div>

                <p>Vous êtes sur le point de supprimer définitivement la boutique :</p>
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 id="deleteShopName" class="mb-2"></h6>
                        <div id="deleteShopStats"></div>
                    </div>
                </div>

                <div class="mt-3">
                    <h6>Cette action supprimera :</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-danger me-2"></i>La boutique et toutes ses informations</li>
                        <li><i class="fas fa-check text-danger me-2"></i>Tous les produits de la boutique</li>
                        <li><i class="fas fa-check text-danger me-2"></i>Toutes les images associées</li>
                        <li><i class="fas fa-check text-danger me-2"></i>Les avis et évaluations</li>
                        <li><i class="fas fa-check text-danger me-2"></i>Les articles dans les paniers</li>
                    </ul>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note :</strong> Les commandes seront conservées pour les archives comptables,
                        mais la référence à la boutique sera supprimée.
                    </div>
                </div>

                <div class="form-check mt-3">
                    <input class="form-check-input" type="checkbox" id="confirmDelete">
                    <label class="form-check-label" for="confirmDelete">
                        Je comprends que cette action est irréversible
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Annuler
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn" disabled>
                    <i class="fas fa-trash me-2"></i>Supprimer Définitivement
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentShopId = null;

function deleteShop(shopId, shopName, productCount, orderCount) {
    currentShopId = shopId;

    // Update modal content
    document.getElementById('deleteShopName').textContent = shopName;
    document.getElementById('deleteShopStats').innerHTML = `
        <small class="text-muted">
            <i class="fas fa-box me-1"></i>${productCount} produits •
            <i class="fas fa-shopping-cart me-1"></i>${orderCount} commandes
        </small>
    `;

    // Reset confirmation checkbox
    const confirmCheckbox = document.getElementById('confirmDelete');
    const confirmBtn = document.getElementById('confirmDeleteBtn');
    confirmCheckbox.checked = false;
    confirmBtn.disabled = true;

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('deleteShopModal'));
    modal.show();
}

// Enable/disable confirm button based on checkbox
document.getElementById('confirmDelete').addEventListener('change', function() {
    document.getElementById('confirmDeleteBtn').disabled = !this.checked;
});

// Handle delete confirmation
document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
    if (currentShopId && document.getElementById('confirmDelete').checked) {
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/shops/${currentShopId}/delete`;
        document.body.appendChild(form);
        form.submit();
    }
});
</script>
{% endblock %}
