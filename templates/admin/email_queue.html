{% extends "admin/base.html" %}

{% block title %}File d'attente Email - Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>File d'attente Email
                    </h5>
                    <div>
                        <a href="{{ url_for('email.process_queue') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-play me-1"></i>Traiter la File
                        </a>
                        <a href="{{ url_for('email.settings') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-cog me-1"></i>Paramètres
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form method="GET" class="d-flex">
                                <select name="status" class="form-select me-2">
                                    <option value="">Tous les statuts</option>
                                    {% for status in email_statuses %}
                                    <option value="{{ status.value }}" {% if status_filter == status.value %}selected{% endif %}>
                                        {{ status.value.title() }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <button type="submit" class="btn btn-outline-primary">Filtrer</button>
                            </form>
                        </div>
                        <div class="col-md-6 text-end">
                            <small class="text-muted">
                                Page {{ emails.page }} sur {{ emails.pages }} 
                                ({{ emails.total }} emails au total)
                            </small>
                        </div>
                    </div>

                    {% if emails.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Destinataire</th>
                                    <th>Type</th>
                                    <th>Sujet</th>
                                    <th>Statut</th>
                                    <th>Tentatives</th>
                                    <th>Date Création</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for email in emails.items %}
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ email.recipient_email }}</strong>
                                            {% if email.recipient_name %}
                                            <br><small class="text-muted">{{ email.recipient_name }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if email.email_type.value == 'welcome' %}success{% elif email.email_type.value == 'newsletter' %}info{% elif 'shop' in email.email_type.value %}primary{% elif 'order' in email.email_type.value %}warning{% else %}secondary{% endif %}">
                                            {{ email.email_type.value.replace('_', ' ').title() }}
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ email.subject[:40] }}{% if email.subject|length > 40 %}...{% endif %}</small>
                                    </td>
                                    <td>
                                        {% if email.status.value == 'pending' %}
                                        <span class="badge bg-warning">En attente</span>
                                        {% elif email.status.value == 'sent' %}
                                        <span class="badge bg-success">Envoyé</span>
                                        {% elif email.status.value == 'failed' %}
                                        <span class="badge bg-danger">Échec</span>
                                        {% elif email.status.value == 'cancelled' %}
                                        <span class="badge bg-secondary">Annulé</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="{% if email.attempts >= email.max_attempts %}text-danger{% elif email.attempts > 0 %}text-warning{% else %}text-muted{% endif %}">
                                            {{ email.attempts }}/{{ email.max_attempts }}
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ email.created_at.strftime('%d/%m/%Y %H:%M') }}
                                            {% if email.sent_at %}
                                            <br>Envoyé: {{ email.sent_at.strftime('%d/%m/%Y %H:%M') }}
                                            {% endif %}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-info" 
                                                    onclick="viewEmail({{ email.id }})" title="Voir">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            {% if email.status.value == 'pending' %}
                                            <button class="btn btn-outline-success" 
                                                    onclick="retryEmail({{ email.id }})" title="Réessayer">
                                                <i class="fas fa-redo"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" 
                                                    onclick="cancelEmail({{ email.id }})" title="Annuler">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            {% endif %}
                                            {% if email.status.value == 'failed' and email.error_message %}
                                            <button class="btn btn-outline-warning" 
                                                    onclick="showError('{{ email.error_message|e }}')" title="Voir l'erreur">
                                                <i class="fas fa-exclamation-triangle"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if emails.pages > 1 %}
                    <nav aria-label="Pagination">
                        <ul class="pagination justify-content-center">
                            {% if emails.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('email.queue', page=emails.prev_num, status=status_filter) }}">Précédent</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in emails.iter_pages() %}
                            {% if page_num %}
                            {% if page_num != emails.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('email.queue', page=page_num, status=status_filter) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                            {% endfor %}
                            
                            {% if emails.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('email.queue', page=emails.next_num, status=status_filter) }}">Suivant</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucun email en file d'attente</h5>
                        <p class="text-muted">
                            {% if status_filter %}
                            Aucun email avec le statut "{{ status_filter }}" trouvé.
                            {% else %}
                            La file d'attente email est vide.
                            {% endif %}
                        </p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Queue Statistics -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-warning">{{ emails.items|selectattr('status.value', 'equalto', 'pending')|list|length }}</h3>
                    <small class="text-muted">En Attente</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-success">{{ emails.items|selectattr('status.value', 'equalto', 'sent')|list|length }}</h3>
                    <small class="text-muted">Envoyés</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-danger">{{ emails.items|selectattr('status.value', 'equalto', 'failed')|list|length }}</h3>
                    <small class="text-muted">Échecs</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-secondary">{{ emails.items|selectattr('status.value', 'equalto', 'cancelled')|list|length }}</h3>
                    <small class="text-muted">Annulés</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Email View Modal -->
<div class="modal fade" id="emailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Contenu Email</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="emailContent">
                <!-- Email content will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function viewEmail(emailId) {
    fetch(`/admin/email/queue/${emailId}/view`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('emailContent').innerHTML = html;
            new bootstrap.Modal(document.getElementById('emailModal')).show();
        })
        .catch(error => {
            alert('Erreur lors du chargement de l\'email');
        });
}

function retryEmail(emailId) {
    if (confirm('Réessayer l\'envoi de cet email ?')) {
        fetch(`/admin/email/queue/${emailId}/retry`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Email remis en file d\'attente !');
                location.reload();
            } else {
                alert('Erreur : ' + data.message);
            }
        })
        .catch(error => {
            alert('Erreur lors de la remise en file');
        });
    }
}

function cancelEmail(emailId) {
    if (confirm('Annuler cet email ?')) {
        fetch(`/admin/email/queue/${emailId}/cancel`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Email annulé !');
                location.reload();
            } else {
                alert('Erreur : ' + data.message);
            }
        })
        .catch(error => {
            alert('Erreur lors de l\'annulation');
        });
    }
}

function showError(errorMessage) {
    alert('Erreur: ' + errorMessage);
}

// Auto-refresh every 30 seconds for pending emails
setInterval(function() {
    if (window.location.pathname.includes('/email/queue')) {
        const pendingCount = document.querySelectorAll('.badge.bg-warning').length;
        if (pendingCount > 0) {
            location.reload();
        }
    }
}, 30000);
</script>
{% endblock %}
