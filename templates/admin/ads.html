{% extends "base.html" %}

{% block title %}Gestion des Publicités - Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Gestion des Publicités</h1>
                    <p class="text-muted">Gérez les publicités affichées sur le site</p>
                </div>
                <a href="{{ url_for('admin.create_ad') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Nouvelle Publicité
                </a>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">Statut</label>
                            <select name="status" id="status" class="form-select">
                                <option value="all" {% if status_filter == 'all' %}selected{% endif %}>Tous</option>
                                <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Actif</option>
                                <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>Inactif</option>
                                <option value="draft" {% if status_filter == 'draft' %}selected{% endif %}>Brouillon</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="search" class="form-label">Recherche</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Nom ou titre de la publicité..." value="{{ search }}">
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>Filtrer
                            </button>
                            <a href="{{ url_for('admin.manage_ads') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Reset
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Ads Table -->
            <div class="card">
                <div class="card-body">
                    {% if ads.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Nom</th>
                                    <th>Type</th>
                                    <th>Statut</th>
                                    <th>Impressions</th>
                                    <th>Période</th>
                                    <th>Créée le</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ad in ads.items %}
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ ad.name }}</strong>
                                            {% if ad.title %}
                                            <br><small class="text-muted">{{ ad.title }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if ad.ad_type.value == 'text' %}
                                        <span class="badge bg-info">Texte</span>
                                        {% elif ad.ad_type.value == 'image' %}
                                        <span class="badge bg-success">Image</span>
                                        {% elif ad.ad_type.value == 'html' %}
                                        <span class="badge bg-warning">HTML</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if ad.status.value == 'active' %}
                                        <span class="badge bg-success">Actif</span>
                                        {% elif ad.status.value == 'inactive' %}
                                        <span class="badge bg-secondary">Inactif</span>
                                        {% else %}
                                        <span class="badge bg-warning">Brouillon</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ ad.current_impressions }}
                                        {% if ad.max_impressions %}
                                        / {{ ad.max_impressions }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if ad.start_date %}
                                        Du {{ ad.start_date.strftime('%d/%m/%Y') }}
                                        {% endif %}
                                        {% if ad.end_date %}
                                        au {{ ad.end_date.strftime('%d/%m/%Y') }}
                                        {% endif %}
                                        {% if not ad.start_date and not ad.end_date %}
                                        <span class="text-muted">Permanente</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ ad.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('admin.edit_ad', ad_id=ad.id) }}" 
                                               class="btn btn-outline-primary" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" action="{{ url_for('admin.toggle_ad_status', ad_id=ad.id) }}" 
                                                  class="d-inline">
                                                <button type="submit" class="btn btn-outline-warning" 
                                                        title="{% if ad.status.value == 'active' %}Désactiver{% else %}Activer{% endif %}">
                                                    {% if ad.status.value == 'active' %}
                                                    <i class="fas fa-pause"></i>
                                                    {% else %}
                                                    <i class="fas fa-play"></i>
                                                    {% endif %}
                                                </button>
                                            </form>
                                            <form method="POST" action="{{ url_for('admin.delete_ad', ad_id=ad.id) }}" 
                                                  class="d-inline" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette publicité ?')">
                                                <button type="submit" class="btn btn-outline-danger" title="Supprimer">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if ads.pages > 1 %}
                    <nav aria-label="Navigation des pages" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if ads.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.manage_ads', page=ads.prev_num, status=status_filter, search=search) }}">
                                    Précédent
                                </a>
                            </li>
                            {% endif %}

                            {% for page_num in ads.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != ads.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.manage_ads', page=page_num, status=status_filter, search=search) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if ads.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.manage_ads', page=ads.next_num, status=status_filter, search=search) }}">
                                    Suivant
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-ad fa-3x text-muted mb-3"></i>
                        <h4>Aucune publicité trouvée</h4>
                        <p class="text-muted">Commencez par créer votre première publicité.</p>
                        <a href="{{ url_for('admin.create_ad') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Créer une Publicité
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
