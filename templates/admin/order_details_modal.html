<!-- Admin Order Details Modal Content -->
<div class="modal-header">
    <h5 class="modal-title">
        <i class="fas fa-receipt me-2"></i>Détails de la Commande #{{ order.id }}
    </h5>
    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
</div>

<div class="modal-body">
    <!-- Order Information -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h6 class="text-primary">Informations de la Commande</h6>
            <table class="table table-sm">
                <tr>
                    <td><strong>Numéro:</strong></td>
                    <td>{{ order.order_number or '#' + order.id|string }}</td>
                </tr>
                <tr>
                    <td><strong>Date:</strong></td>
                    <td>{{ order.created_at.strftime('%d/%m/%Y à %H:%M') }}</td>
                </tr>
                <tr>
                    <td><strong>Boutique:</strong></td>
                    <td>
                        {% if order.shop %}
                            <strong>{{ order.shop.name }}</strong>
                        {% else %}
                            <span class="text-muted">Boutique supprimée</span>
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <td><strong>Statut:</strong></td>
                    <td>
                        {% if order.status.value == 'pending' %}
                        <span class="badge bg-warning">En Attente</span>
                        {% elif order.status.value == 'processing' %}
                        <span class="badge bg-primary">En Cours</span>
                        {% elif order.status.value == 'shipped' %}
                        <span class="badge bg-secondary">Expédiée</span>
                        {% elif order.status.value == 'delivered' %}
                        <span class="badge bg-success">Livrée</span>
                        {% elif order.status.value == 'cancelled' %}
                        <span class="badge bg-danger">Annulée</span>
                        {% elif order.status.value == 'refunded' %}
                        <span class="badge bg-info">Remboursée</span>
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <td><strong>Paiement:</strong></td>
                    <td>
                        {% if order.payment_status == 'paid' %}
                        <span class="badge bg-success">Payée</span>
                        {% elif order.payment_status == 'pending' %}
                        <span class="badge bg-warning">En Attente</span>
                        {% else %}
                        <span class="badge bg-secondary">Non Payée</span>
                        {% endif %}
                    </td>
                </tr>
            </table>
        </div>
        <div class="col-md-6">
            <h6 class="text-primary">Informations Client</h6>
            <table class="table table-sm">
                <tr>
                    <td><strong>Nom:</strong></td>
                    <td>
                        {% if order.customer %}
                            <strong>{{ order.customer.get_full_name() }}</strong>
                        {% else %}
                            Commande manuelle
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <td><strong>Téléphone:</strong></td>
                    <td>
                        {% if order.customer and order.customer.phone %}
                            <span class="fw-bold text-success">{{ order.customer.phone }}</span>
                            <div class="mt-1">
                                <a href="#" onclick="contactWhatsApp('{{ order.customer.phone }}')" class="btn btn-sm btn-success me-1">
                                    <i class="fab fa-whatsapp"></i>
                                </a>
                                <a href="tel:{{ order.customer.phone }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-phone"></i>
                                </a>
                            </div>
                        {% else %}
                            <span class="text-muted">Non disponible</span>
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <td><strong>Email:</strong></td>
                    <td>
                        {% if order.customer %}
                            <small class="text-muted">{{ order.customer.email }}</small>
                        {% else %}
                            <small class="text-muted">N/A</small>
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <td><strong>Méthode de Paiement:</strong></td>
                    <td>{{ order.payment_method or 'Non spécifiée' }}</td>
                </tr>
            </table>
        </div>
    </div>

    <!-- Shipping Address -->
    {% if order.shipping_address %}
    <div class="mb-4">
        <h6 class="text-primary">Adresse de Livraison</h6>
        <div class="card bg-light">
            <div class="card-body">
                {% if order.shipping_address is string %}
                    <p class="mb-0">{{ order.shipping_address }}</p>
                {% else %}
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-1"><strong>{{ order.shipping_address.get('first_name', '') }} {{ order.shipping_address.get('last_name', '') }}</strong></p>
                            <p class="mb-1">{{ order.shipping_address.get('address_line_1', '') }}</p>
                            {% if order.shipping_address.get('address_line_2') %}
                            <p class="mb-1">{{ order.shipping_address.get('address_line_2') }}</p>
                            {% endif %}
                            <p class="mb-1">{{ order.shipping_address.get('city', '') }}, {{ order.shipping_address.get('state', '') }} {{ order.shipping_address.get('postal_code', '') }}</p>
                            <p class="mb-1">{{ order.shipping_address.get('country', '') }}</p>
                            {% if order.shipping_address.get('phone') %}
                            <p class="mb-0"><strong>Tél:</strong> {{ order.shipping_address.get('phone') }}</p>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Order Items -->
    <div class="mb-4">
        <h6 class="text-primary">Articles Commandés</h6>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Produit</th>
                        <th>Prix Unitaire</th>
                        <th>Quantité</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in order.items %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <img src="{{ item.product.get_main_image() }}" alt="{{ item.product.name }}"
                                     class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                <div>
                                    <div class="fw-bold">{{ item.product.name }}</div>
                                    <small class="text-muted">{{ item.product.sku or 'N/A' }}</small>
                                </div>
                            </div>
                        </td>
                        <td>{{ "%.0f"|format(item.price) }} FCFA</td>
                        <td>{{ item.quantity }}</td>
                        <td class="fw-bold">{{ "%.0f"|format(item.total) }} FCFA</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Order Totals -->
    <div class="mb-4">
        <h6 class="text-primary">Résumé des Coûts</h6>
        <div class="row">
            <div class="col-md-6 ms-auto">
                <table class="table table-sm">
                    <tr>
                        <td>Sous-total:</td>
                        <td class="text-end">{{ "%.0f"|format(order.subtotal) }} FCFA</td>
                    </tr>
                    {% if order.shipping_cost > 0 %}
                    <tr>
                        <td>Livraison:</td>
                        <td class="text-end">{{ "%.0f"|format(order.shipping_cost) }} FCFA</td>
                    </tr>
                    {% else %}
                    <tr>
                        <td>Livraison:</td>
                        <td class="text-end text-success">Gratuite</td>
                    </tr>
                    {% endif %}
                    {% if order.tax_amount > 0 %}
                    <tr>
                        <td>Taxes:</td>
                        <td class="text-end">{{ "%.0f"|format(order.tax_amount) }} FCFA</td>
                    </tr>
                    {% endif %}
                    <tr class="table-primary">
                        <td><strong>Total:</strong></td>
                        <td class="text-end"><strong>{{ "%.0f"|format(order.total) }} FCFA</strong></td>
                    </tr>
                    {% if order.commission_amount %}
                    <tr class="table-warning">
                        <td><strong>Commission Plateforme:</strong></td>
                        <td class="text-end"><strong>{{ "%.0f"|format(order.commission_amount) }} FCFA</strong></td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>
    </div>

    <!-- Admin Status Update Form -->
    <div class="mb-4">
        <h6 class="text-primary">Actions Administratives</h6>
        <form id="adminOrderForm" onsubmit="updateOrderStatusAdmin(event, {{ order.id }})">
            <div class="row">
                <div class="col-md-6">
                    <label for="orderStatus" class="form-label">Changer le Statut:</label>
                    <select class="form-select" id="orderStatus" name="status">
                        <option value="pending" {% if order.status.value == 'pending' %}selected{% endif %}>En Attente</option>
                        <option value="processing" {% if order.status.value == 'processing' %}selected{% endif %}>En Cours</option>
                        <option value="shipped" {% if order.status.value == 'shipped' %}selected{% endif %}>Expédiée</option>
                        <option value="delivered" {% if order.status.value == 'delivered' %}selected{% endif %}>Livrée</option>
                        <option value="cancelled" {% if order.status.value == 'cancelled' %}selected{% endif %}>Annulée</option>
                        <option value="refunded" {% if order.status.value == 'refunded' %}selected{% endif %}>Remboursée</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="adminNotes" class="form-label">Notes Administratives:</label>
                    <textarea class="form-control" id="adminNotes" name="admin_notes" rows="2" placeholder="Ajouter une note..."></textarea>
                </div>
            </div>
            <div class="mt-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>Mettre à Jour
                </button>
            </div>
        </form>
    </div>

    <!-- Notes -->
    {% if order.customer_notes or order.admin_notes %}
    <div class="mb-4">
        <h6 class="text-primary">Notes Existantes</h6>
        {% if order.customer_notes %}
        <div class="card bg-light mb-2">
            <div class="card-body">
                <h6 class="card-title">Notes du Client:</h6>
                <p class="card-text">{{ order.customer_notes }}</p>
            </div>
        </div>
        {% endif %}
        {% if order.admin_notes %}
        <div class="card bg-warning bg-opacity-10">
            <div class="card-body">
                <h6 class="card-title">Notes Administratives:</h6>
                <p class="card-text">{{ order.admin_notes|nl2br }}</p>
            </div>
        </div>
        {% endif %}
    </div>
    {% endif %}
</div>

<div class="modal-footer">
    <div class="d-flex gap-2 w-100">
        <!-- Contact Customer -->
        {% if order.customer and order.customer.phone %}
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-success btn-sm" onclick="contactWhatsApp('{{ order.customer.phone }}')">
                <i class="fab fa-whatsapp me-1"></i>WhatsApp
            </button>
            <a href="tel:{{ order.customer.phone }}" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-phone me-1"></i>Appeler
            </a>
        </div>
        {% endif %}

        <!-- Print -->
        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="printOrder()">
            <i class="fas fa-print me-1"></i>Imprimer
        </button>

        <div class="ms-auto">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
        </div>
    </div>
</div>

<script>
function updateOrderStatusAdmin(event, orderId) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    
    fetch(`/admin/orders/${orderId}/status`, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            // Close modal and refresh page
            bootstrap.Modal.getInstance(document.getElementById('orderDetailsModal')).hide();
            window.location.reload();
        } else {
            alert('Erreur lors de la mise à jour du statut');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Erreur lors de la mise à jour du statut');
    });
}

function contactWhatsApp(phone) {
    const cleanPhone = phone.replace(/[^0-9]/g, '');
    const message = encodeURIComponent('Bonjour, concernant votre commande sur Afroly.org');
    window.open(`https://wa.me/${cleanPhone}?text=${message}`, '_blank');
}

function printOrder() {
    window.print();
}
</script>
