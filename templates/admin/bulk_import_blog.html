{% extends "base.html" %}

{% block title %}Import en Masse - Blog - Admin - Afroly.org{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-6 fw-bold">
                <i class="fas fa-file-import me-3 text-info"></i>Import en Masse d'Articles
            </h1>
            <p class="lead text-muted">Importer plusieurs articles de blog depuis un fichier CSV ou Excel</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('admin.manage_blog') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Retour aux Articles
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Upload Form -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-upload me-2"></i>Télécharger le Fichier
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="mb-4">
                            <label class="form-label">Fichier CSV ou Excel <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" name="file" accept=".csv,.xlsx,.xls" required>
                            <small class="text-muted">Formats supportés: CSV, XLS, XLSX (max 10MB)</small>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Important:</strong> Assurez-vous que votre fichier contient les colonnes requises (voir l'exemple ci-dessous).
                        </div>

                        <div class="d-flex gap-2 justify-content-end">
                            <a href="{{ url_for('admin.manage_blog') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-upload me-2"></i>Importer les Articles
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sample Data -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-table me-2"></i>Exemple de Données
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>title <span class="text-danger">*</span></th>
                                    <th>content <span class="text-danger">*</span></th>
                                    <th>excerpt</th>
                                    <th>category</th>
                                    <th>tags</th>
                                    <th>is_published</th>
                                    <th>is_featured</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Guide du E-commerce en Afrique</td>
                                    <td>Le e-commerce en Afrique connaît une croissance exceptionnelle...</td>
                                    <td>Découvrez les opportunités du commerce électronique africain</td>
                                    <td>E-commerce</td>
                                    <td>afrique, ecommerce, business</td>
                                    <td>true</td>
                                    <td>false</td>
                                </tr>
                                <tr>
                                    <td>Marketing Digital pour PME</td>
                                    <td>Les petites et moyennes entreprises africaines peuvent...</td>
                                    <td>Stratégies de marketing digital adaptées aux PME africaines</td>
                                    <td>Marketing</td>
                                    <td>marketing, digital, pme</td>
                                    <td>false</td>
                                    <td>true</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mt-3">
                        <button class="btn btn-outline-primary" onclick="downloadSample()">
                            <i class="fas fa-download me-2"></i>Télécharger l'Exemple CSV
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Instructions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-list-ol me-2"></i>Instructions
                    </h6>
                </div>
                <div class="card-body">
                    <ol class="mb-0">
                        <li class="mb-2">Préparez votre fichier CSV ou Excel avec les colonnes requises</li>
                        <li class="mb-2">Assurez-vous que chaque ligne contient au minimum un <strong>titre</strong> et un <strong>contenu</strong></li>
                        <li class="mb-2">Utilisez "true" ou "false" pour les colonnes booléennes</li>
                        <li class="mb-2">Séparez les tags par des virgules</li>
                        <li>Téléchargez le fichier et vérifiez les résultats</li>
                    </ol>
                </div>
            </div>

            <!-- Column Descriptions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-columns me-2"></i>Description des Colonnes
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>title <span class="text-danger">*</span></strong>
                        <div class="text-muted small">Titre de l'article (requis)</div>
                    </div>
                    
                    <div class="mb-3">
                        <strong>content <span class="text-danger">*</span></strong>
                        <div class="text-muted small">Contenu complet de l'article (requis)</div>
                    </div>
                    
                    <div class="mb-3">
                        <strong>excerpt</strong>
                        <div class="text-muted small">Résumé court (optionnel)</div>
                    </div>
                    
                    <div class="mb-3">
                        <strong>category</strong>
                        <div class="text-muted small">Catégorie de l'article (optionnel)</div>
                    </div>
                    
                    <div class="mb-3">
                        <strong>tags</strong>
                        <div class="text-muted small">Tags séparés par des virgules (optionnel)</div>
                    </div>
                    
                    <div class="mb-3">
                        <strong>is_published</strong>
                        <div class="text-muted small">true/false - Publier immédiatement (optionnel)</div>
                    </div>
                    
                    <div class="mb-3">
                        <strong>is_featured</strong>
                        <div class="text-muted small">true/false - Article en vedette (optionnel)</div>
                    </div>
                </div>
            </div>

            <!-- Tips -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Conseils
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Testez avec un petit fichier d'abord
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Vérifiez l'encodage UTF-8 pour les caractères spéciaux
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Évitez les guillemets dans le contenu
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Les articles importés seront attribués à votre compte
                        </li>
                        <li>
                            <i class="fas fa-check text-success me-2"></i>
                            Vous pourrez modifier les articles après l'import
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function downloadSample() {
    // Create sample CSV data
    const csvData = [
        ['title', 'content', 'excerpt', 'category', 'tags', 'is_published', 'is_featured'],
        ['Guide du E-commerce en Afrique', 'Le e-commerce en Afrique connaît une croissance exceptionnelle. Avec plus de 1,3 milliard d\'habitants et une pénétration croissante d\'Internet, le continent offre des opportunités uniques pour les entrepreneurs. Dans cet article, nous explorons les meilleures stratégies pour réussir dans le commerce électronique africain.', 'Découvrez les opportunités du commerce électronique africain', 'E-commerce', 'afrique, ecommerce, business', 'true', 'false'],
        ['Marketing Digital pour PME', 'Les petites et moyennes entreprises africaines peuvent tirer parti du marketing digital pour atteindre de nouveaux clients. Les réseaux sociaux, le marketing de contenu et la publicité en ligne offrent des moyens abordables de promouvoir votre business.', 'Stratégies de marketing digital adaptées aux PME africaines', 'Marketing', 'marketing, digital, pme', 'false', 'true'],
        ['Paiements Mobiles en Afrique', 'Les solutions de paiement mobile révolutionnent le commerce en Afrique. De M-Pesa au Kenya à Orange Money en Afrique de l\'Ouest, ces plateformes facilitent les transactions et stimulent l\'inclusion financière.', 'Comment les paiements mobiles transforment le commerce africain', 'Technologie', 'paiement, mobile, fintech', 'true', 'false']
    ];
    
    // Convert to CSV string
    const csvContent = csvData.map(row => 
        row.map(field => `"${field.replace(/"/g, '""')}"`).join(',')
    ).join('\n');
    
    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'exemple_articles_blog.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// File upload validation
document.querySelector('input[type="file"]').addEventListener('change', function() {
    const file = this.files[0];
    if (file) {
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
            alert('Le fichier est trop volumineux. Taille maximum: 10MB');
            this.value = '';
            return;
        }
        
        const allowedTypes = ['.csv', '.xlsx', '.xls'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        if (!allowedTypes.includes(fileExtension)) {
            alert('Format de fichier non supporté. Utilisez CSV, XLS ou XLSX.');
            this.value = '';
            return;
        }
    }
});
</script>
{% endblock %}
