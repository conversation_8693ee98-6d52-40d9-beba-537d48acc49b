{% extends "base.html" %}

{% block title %}{% if newsletter %}Modifier{% else %}Créer{% endif %} Newsletter - Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-newspaper me-2"></i>
                        {% if newsletter %}Modifier Newsletter{% else %}Créer Newsletter{% endif %}
                    </h5>
                    <a href="{{ url_for('email.newsletters') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>Retour
                    </a>
                </div>
                <div class="card-body">
                    <form method="POST" id="newsletterForm">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="title" class="form-label">Titre de la Newsletter *</label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="{{ newsletter.title if newsletter else '' }}" required>
                                    <div class="form-text">Titre interne pour identifier cette newsletter</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="target_audience" class="form-label">Audience Cible *</label>
                                    <select class="form-select" id="target_audience" name="target_audience" required>
                                        <option value="all" {% if newsletter and newsletter.target_audience == 'all' %}selected{% endif %}>
                                            Tous les abonnés
                                        </option>
                                        <option value="vendors" {% if newsletter and newsletter.target_audience == 'vendors' %}selected{% endif %}>
                                            Vendeurs uniquement
                                        </option>
                                        <option value="shoppers" {% if newsletter and newsletter.target_audience == 'shoppers' %}selected{% endif %}>
                                            Acheteurs uniquement
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="subject" class="form-label">Sujet de l'Email *</label>
                            <input type="text" class="form-control" id="subject" name="subject" 
                                   value="{{ newsletter.subject if newsletter else '' }}" required>
                            <div class="form-text">
                                Sujet qui apparaîtra dans la boîte de réception des destinataires
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="html_content" class="form-label">Contenu de la Newsletter *</label>
                            <textarea class="form-control" id="html_content" name="html_content" 
                                      rows="20" required>{{ newsletter.html_content if newsletter else '' }}</textarea>
                            <div class="form-text">
                                Contenu HTML de la newsletter. Vous pouvez utiliser des variables comme {{ "{{subscriber_name}}" }}.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="text_content" class="form-label">Version Texte</label>
                            <textarea class="form-control" id="text_content" name="text_content" 
                                      rows="10">{{ newsletter.text_content if newsletter else '' }}</textarea>
                            <div class="form-text">
                                Version texte de la newsletter (optionnel, mais recommandé)
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="schedule_date" class="form-label">Programmer l'envoi (optionnel)</label>
                                    <input type="datetime-local" class="form-control" id="schedule_date" name="schedule_date" 
                                           value="{{ newsletter.scheduled_at.strftime('%Y-%m-%dT%H:%M') if newsletter and newsletter.scheduled_at else '' }}">
                                    <div class="form-text">
                                        Laisser vide pour envoyer immédiatement ou programmer pour plus tard
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Statistiques d'Audience</label>
                                    <div class="border rounded p-3 bg-light">
                                        <div id="audienceStats">
                                            <small class="text-muted">
                                                <i class="fas fa-spinner fa-spin me-1"></i>Chargement des statistiques...
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    {% if newsletter %}Mettre à jour{% else %}Créer{% endif %} Newsletter
                                </button>
                                <button type="button" class="btn btn-outline-info ms-2" onclick="previewNewsletter()">
                                    <i class="fas fa-eye me-2"></i>Aperçu
                                </button>
                                {% if not newsletter %}
                                <button type="button" class="btn btn-outline-success ms-2" onclick="sendNow()">
                                    <i class="fas fa-paper-plane me-2"></i>Créer et Envoyer
                                </button>
                                {% endif %}
                            </div>
                            <a href="{{ url_for('email.newsletters') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Newsletter Tips -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Conseils pour une Newsletter Efficace
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Contenu</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Utilisez un sujet accrocheur et clair</li>
                                <li><i class="fas fa-check text-success me-2"></i>Personnalisez avec {{ "{{subscriber_name}}" }}</li>
                                <li><i class="fas fa-check text-success me-2"></i>Incluez des images optimisées</li>
                                <li><i class="fas fa-check text-success me-2"></i>Ajoutez des appels à l'action clairs</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">Variables Disponibles</h6>
                            <div class="bg-light p-3 rounded">
                                <small class="text-muted">
                                    <code>{{ "{{subscriber_name}}" }}</code> - Nom de l'abonné<br>
                                    <code>{{ "{{platform_name}}" }}</code> - Nom de la plateforme<br>
                                    <code>{{ "{{platform_url}}" }}</code> - URL de la plateforme<br>
                                    <code>{{ "{{current_date}}" }}</code> - Date actuelle<br>
                                    <code>{{ "{{unsubscribe_url}}" }}</code> - Lien de désabonnement
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Aperçu Newsletter</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- Preview content will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Load audience statistics
function loadAudienceStats() {
    const audience = document.getElementById('target_audience').value;
    
    fetch(`/admin/email/audience-stats?audience=${audience}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('audienceStats').innerHTML = `
                <small class="text-muted">
                    <i class="fas fa-users me-1"></i>
                    <strong>${data.count}</strong> destinataires potentiels
                    ${data.details ? '<br>' + data.details : ''}
                </small>
            `;
        })
        .catch(error => {
            document.getElementById('audienceStats').innerHTML = `
                <small class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Erreur lors du chargement des statistiques
                </small>
            `;
        });
}

// Preview newsletter
function previewNewsletter() {
    const htmlContent = document.getElementById('html_content').value;
    const subject = document.getElementById('subject').value;
    
    if (!htmlContent.trim()) {
        alert('Veuillez saisir le contenu de la newsletter pour l\'aperçu');
        return;
    }
    
    // Simple preview with sample data
    let previewHtml = htmlContent;
    const sampleData = {
        'subscriber_name': 'Cher(e) abonné(e)',
        'platform_name': 'Afroly.org',
        'platform_url': 'https://afroly.org',
        'current_date': new Date().toLocaleDateString('fr-FR'),
        'unsubscribe_url': '#'
    };
    
    // Replace variables with sample data
    for (const [key, value] of Object.entries(sampleData)) {
        const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
        previewHtml = previewHtml.replace(regex, value);
    }
    
    document.getElementById('previewContent').innerHTML = `
        <div class="mb-3">
            <strong>Sujet :</strong> ${subject}
        </div>
        <div class="mb-3">
            <strong>Audience :</strong> ${document.getElementById('target_audience').selectedOptions[0].text}
        </div>
        <hr>
        <div style="border: 1px solid #ddd; padding: 20px; background: white;">
            ${previewHtml}
        </div>
    `;
    
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

// Send newsletter immediately
function sendNow() {
    if (confirm('Créer et envoyer cette newsletter immédiatement ?')) {
        // Remove schedule date to send immediately
        document.getElementById('schedule_date').value = '';
        
        // Add a hidden field to indicate immediate sending
        const form = document.getElementById('newsletterForm');
        const hiddenField = document.createElement('input');
        hiddenField.type = 'hidden';
        hiddenField.name = 'send_immediately';
        hiddenField.value = 'true';
        form.appendChild(hiddenField);
        
        form.submit();
    }
}

// Load audience stats on page load and when audience changes
document.addEventListener('DOMContentLoaded', loadAudienceStats);
document.getElementById('target_audience').addEventListener('change', loadAudienceStats);

// Auto-save functionality
let autoSaveTimer;
function autoSave() {
    clearTimeout(autoSaveTimer);
    autoSaveTimer = setTimeout(() => {
        console.log('Auto-saving newsletter...');
        // Here you could implement auto-save to localStorage or server
    }, 5000);
}

// Add auto-save listeners
document.getElementById('html_content').addEventListener('input', autoSave);
document.getElementById('text_content').addEventListener('input', autoSave);
document.getElementById('subject').addEventListener('input', autoSave);
document.getElementById('title').addEventListener('input', autoSave);
</script>
{% endblock %}
