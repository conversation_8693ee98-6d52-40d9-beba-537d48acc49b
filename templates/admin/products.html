{% extends "base.html" %}

{% block title %}Gestion des Produits - Admin - Afroly.org{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-6 fw-bold">
                <i class="fas fa-box me-3 text-info"></i>Gestion des Produits
            </h1>
            <p class="lead text-muted">Modérer et approuver les produits</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Retour au Dashboard
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">Recherche</label>
                            <input type="text" class="form-control" name="search" value="{{ search }}"
                                   placeholder="Nom du produit...">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Statut</label>
                            <select class="form-select" name="status">
                                <option value="all" {% if status_filter == 'all' %}selected{% endif %}>Tous</option>
                                <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>En Attente</option>
                                <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Actif</option>
                                <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>Rejeté</option>
                                <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>Inactif</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Filtrer
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Products Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-list me-2"></i>Liste des Produits
                        <span class="badge bg-info ms-2">{{ products.total }} total</span>
                        {% if status_filter == 'pending' %}
                        <span class="badge bg-warning ms-1">{{ products.total }} en attente</span>
                        {% endif %}
                    </h6>
                </div>
                <div class="card-body">
                    {% if products.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Boutique</th>
                                    <th>Prix</th>
                                    <th>Statut</th>
                                    <th>Création</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in products.items %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{{ product.get_main_image() }}" alt="{{ product.name }}"
                                                 class="rounded me-3" style="width: 60px; height: 60px; object-fit: cover;">
                                            <div>
                                                <h6 class="mb-0">{{ product.name }}</h6>
                                                <small class="text-muted">{{ product.description[:50] }}{% if product.description|length > 50 %}...{% endif %}</small>
                                                {% if product.category %}
                                                <br><span class="badge bg-light text-dark">{{ product.category.name }}</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <h6 class="mb-0">{{ product.shop.name }}</h6>
                                            <small class="text-muted">{{ product.shop.owner.get_full_name() }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            {% if product.sale_price and product.sale_price < product.price %}
                                            <span class="text-decoration-line-through text-muted">{{ "%.0f"|format(product.price) }}</span>
                                            <br><strong class="text-success">{{ "%.0f"|format(product.sale_price) }} FCFA</strong>
                                            {% else %}
                                            <strong>{{ "%.0f"|format(product.price) }} FCFA</strong>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if product.status.value == 'active' %}
                                        <span class="badge bg-success">Actif</span>
                                        {% elif product.status.value == 'pending' %}
                                        <span class="badge bg-warning">En Attente</span>
                                        {% elif product.status.value == 'rejected' %}
                                        <span class="badge bg-danger">Rejeté</span>
                                        {% elif product.status.value == 'inactive' %}
                                        <span class="badge bg-secondary">Inactif</span>
                                        {% endif %}

                                        {% if product.stock_quantity is not none %}
                                        <br><small class="text-muted">Stock: {{ product.stock_quantity }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ product.created_at.strftime('%d/%m/%Y') }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <!-- View Product -->
                                            <a href="{{ url_for('product.view', slug=product.slug) }}"
                                               class="btn btn-sm btn-outline-info" target="_blank"
                                               data-bs-toggle="tooltip" title="Voir le produit">
                                                <i class="fas fa-eye"></i>
                                            </a>

                                            <!-- Edit Product -->
                                            <a href="{{ url_for('admin.edit_product', product_id=product.id) }}"
                                               class="btn btn-sm btn-outline-primary"
                                               data-bs-toggle="tooltip" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>

                                            <!-- Status Actions -->
                                            {% if product.status.value == 'pending' %}
                                            <form method="POST" action="{{ url_for('admin.approve_product', product_id=product.id) }}"
                                                  style="display: inline;">
                                                <button type="submit" class="btn btn-sm btn-outline-success"
                                                        onclick="return confirm('Approuver ce produit ?')"
                                                        data-bs-toggle="tooltip" title="Approuver">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                            <form method="POST" action="{{ url_for('admin.reject_product', product_id=product.id) }}"
                                                  style="display: inline;">
                                                <button type="submit" class="btn btn-sm btn-outline-danger"
                                                        onclick="return confirm('Rejeter ce produit ?')"
                                                        data-bs-toggle="tooltip" title="Rejeter">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </form>
                                            {% elif product.status.value == 'active' %}
                                            <form method="POST" action="{{ url_for('admin.suspend_product', product_id=product.id) }}"
                                                  style="display: inline;">
                                                <button type="submit" class="btn btn-sm btn-outline-warning"
                                                        onclick="return confirm('Suspendre ce produit ?')"
                                                        data-bs-toggle="tooltip" title="Suspendre">
                                                    <i class="fas fa-pause"></i>
                                                </button>
                                            </form>
                                            {% elif product.status.value == 'inactive' %}
                                            <form method="POST" action="{{ url_for('admin.approve_product', product_id=product.id) }}"
                                                  style="display: inline;">
                                                <button type="submit" class="btn btn-sm btn-outline-success"
                                                        onclick="return confirm('Réactiver ce produit ?')"
                                                        data-bs-toggle="tooltip" title="Réactiver">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                            </form>
                                            {% elif product.status.value == 'rejected' %}
                                            <form method="POST" action="{{ url_for('admin.approve_product', product_id=product.id) }}"
                                                  style="display: inline;">
                                                <button type="submit" class="btn btn-sm btn-outline-success"
                                                        onclick="return confirm('Approuver ce produit ?')"
                                                        data-bs-toggle="tooltip" title="Approuver">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                            {% endif %}

                                            <!-- Delete Product -->
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-danger dropdown-toggle"
                                                        data-bs-toggle="dropdown" aria-expanded="false"
                                                        data-bs-toggle="tooltip" title="Supprimer">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <button class="dropdown-item text-danger"
                                                                onclick="deleteProduct({{ product.id }}, '{{ product.name }}')">
                                                            <i class="fas fa-trash me-2"></i>Supprimer Définitivement
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if products.pages > 1 %}
                    <nav aria-label="Navigation des produits" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if products.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.manage_products',
                                    search=search, status=status_filter, page=products.prev_num) }}">
                                    <i class="fas fa-chevron-left me-1"></i>Précédent
                                </a>
                            </li>
                            {% endif %}

                            {% for page_num in products.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != products.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.manage_products',
                                            search=search, status=status_filter, page=page_num) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if products.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.manage_products',
                                    search=search, status=status_filter, page=products.next_num) }}">
                                    Suivant<i class="fas fa-chevron-right ms-1"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-box fa-4x text-muted mb-4"></i>
                        <h4>Aucun produit trouvé</h4>
                        <p class="text-muted">Aucun produit ne correspond à vos critères de recherche.</p>
                        <a href="{{ url_for('admin.manage_products') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Effacer les Filtres
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteProductModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>Supprimer le Produit
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-warning me-2"></i>
                    <strong>Attention !</strong> Cette action est irréversible.
                </div>

                <p>Vous êtes sur le point de supprimer définitivement le produit :</p>
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 id="deleteProductName" class="mb-2"></h6>
                    </div>
                </div>

                <div class="mt-3">
                    <h6>Cette action supprimera :</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-danger me-2"></i>Le produit et toutes ses informations</li>
                        <li><i class="fas fa-check text-danger me-2"></i>Toutes les images du produit</li>
                        <li><i class="fas fa-check text-danger me-2"></i>Les avis et évaluations</li>
                        <li><i class="fas fa-check text-danger me-2"></i>Les articles dans les paniers</li>
                    </ul>
                </div>

                <div class="form-check mt-3">
                    <input class="form-check-input" type="checkbox" id="confirmDeleteProduct">
                    <label class="form-check-label" for="confirmDeleteProduct">
                        Je comprends que cette action est irréversible
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Annuler
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteProductBtn" disabled>
                    <i class="fas fa-trash me-2"></i>Supprimer Définitivement
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentProductId = null;

function deleteProduct(productId, productName) {
    currentProductId = productId;

    // Update modal content
    document.getElementById('deleteProductName').textContent = productName;

    // Reset confirmation checkbox
    const confirmCheckbox = document.getElementById('confirmDeleteProduct');
    const confirmBtn = document.getElementById('confirmDeleteProductBtn');
    confirmCheckbox.checked = false;
    confirmBtn.disabled = true;

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('deleteProductModal'));
    modal.show();
}

// Enable/disable confirm button based on checkbox
document.getElementById('confirmDeleteProduct').addEventListener('change', function() {
    document.getElementById('confirmDeleteProductBtn').disabled = !this.checked;
});

// Handle delete confirmation
document.getElementById('confirmDeleteProductBtn').addEventListener('click', function() {
    if (currentProductId && document.getElementById('confirmDeleteProduct').checked) {
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/products/${currentProductId}/delete`;
        document.body.appendChild(form);
        form.submit();
    }
});

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
