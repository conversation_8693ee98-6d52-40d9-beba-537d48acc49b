{% extends "base.html" %}

{% block title %}Détails Boutique - {{ shop.name }} - Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-store me-3 text-primary"></i>{{ shop.name }}
                    </h2>
                    <p class="text-muted mb-0">Détails de la boutique et activité</p>
                </div>
                <div>
                    <a href="{{ url_for('admin.manage_shops') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Retour aux Boutiques
                    </a>
                    <a href="{{ url_for('shop.view_shop', shop_slug=shop.slug) }}" target="_blank" class="btn btn-success">
                        <i class="fas fa-external-link-alt me-2"></i>Voir la Boutique
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Shop Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center border-primary">
                <div class="card-body">
                    <i class="fas fa-box fa-2x text-primary mb-2"></i>
                    <h6>Produits</h6>
                    <strong class="fs-5">{{ shop_stats.total_products }}</strong>
                    <small class="text-muted d-block">{{ shop_stats.active_products }} actifs</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-success">
                <div class="card-body">
                    <i class="fas fa-shopping-cart fa-2x text-success mb-2"></i>
                    <h6>Commandes</h6>
                    <strong class="fs-5">{{ shop_stats.total_orders }}</strong>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-warning">
                <div class="card-body">
                    <i class="fas fa-euro-sign fa-2x text-warning mb-2"></i>
                    <h6>Revenus</h6>
                    <strong class="fs-5">{{ "%.0f"|format(shop_stats.total_revenue) }} FCFA</strong>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-info">
                <div class="card-body">
                    <i class="fas fa-calendar fa-2x text-info mb-2"></i>
                    <h6>Créée le</h6>
                    <strong>{{ shop.created_at.strftime('%d/%m/%Y') }}</strong>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Shop Details -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Informations de la Boutique
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center">
                            {% if shop.logo %}
                            <img src="/static/uploads/shops/{{ shop.logo }}" alt="{{ shop.name }}"
                                 class="rounded mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                            {% else %}
                            <div class="bg-light rounded mb-3 d-flex align-items-center justify-content-center mx-auto"
                                 style="width: 120px; height: 120px;">
                                <i class="fas fa-store fa-3x text-muted"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-8">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Propriétaire:</strong></td>
                                    <td>
                                        <a href="{{ url_for('admin.view_user', user_id=shop.owner.id) }}" class="text-decoration-none">
                                            {{ shop.owner.get_full_name() }}
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>{{ shop.owner.email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Téléphone:</strong></td>
                                    <td>{{ shop.phone or shop.owner.phone or 'Non renseigné' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Pays:</strong></td>
                                    <td>{{ shop.country }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Statut:</strong></td>
                                    <td>
                                        {% if shop.status.value == 'active' %}
                                        <span class="badge bg-success">Actif</span>
                                        {% elif shop.status.value == 'pending' %}
                                        <span class="badge bg-warning">En Attente</span>
                                        {% elif shop.status.value == 'rejected' %}
                                        <span class="badge bg-danger">Rejeté</span>
                                        {% else %}
                                        <span class="badge bg-secondary">Suspendu</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>URL Courte:</strong></td>
                                    <td>
                                        {% if shop.short_url %}
                                        <a href="{{ url_for('shop.view_shop_short', shop_number=shop.short_url) }}" target="_blank">
                                            afroly.org/{{ shop.short_url }}
                                        </a>
                                        {% else %}
                                        Non assignée
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    {% if shop.description %}
                    <div class="mt-3">
                        <h6>Description:</h6>
                        <p class="text-muted">{{ shop.description }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Recent Products -->
            {% if shop_products %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-box me-2"></i>Produits Récents
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for product in shop_products %}
                        <div class="col-md-6 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <img src="{{ product.get_main_image() }}" alt="{{ product.name }}"
                                             class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">{{ product.name }}</h6>
                                            <small class="text-muted">{{ "%.0f"|format(product.price) }} FCFA</small>
                                            <div class="mt-1">
                                                {% if product.status.value == 'active' %}
                                                <span class="badge bg-success">Actif</span>
                                                {% elif product.status.value == 'pending' %}
                                                <span class="badge bg-warning">En Attente</span>
                                                {% elif product.status.value == 'rejected' %}
                                                <span class="badge bg-danger">Rejeté</span>
                                                {% else %}
                                                <span class="badge bg-secondary">Inactif</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="text-center">
                        <a href="{{ url_for('admin.manage_products', shop_id=shop.id) }}" class="btn btn-outline-primary">
                            Voir tous les produits
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Recent Orders -->
            {% if shop_orders %}
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>Commandes Récentes
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Commande</th>
                                    <th>Client</th>
                                    <th>Total</th>
                                    <th>Statut</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in shop_orders %}
                                <tr>
                                    <td>
                                        <a href="{{ url_for('admin.view_order', order_id=order.id) }}" class="text-decoration-none">
                                            #{{ order.id }}
                                        </a>
                                    </td>
                                    <td>
                                        {% if order.customer %}
                                        <a href="{{ url_for('admin.view_user', user_id=order.customer.id) }}" class="text-decoration-none">
                                            {{ order.customer.get_full_name() }}
                                        </a>
                                        {% else %}
                                        Commande manuelle
                                        {% endif %}
                                    </td>
                                    <td>{{ "%.0f"|format(order.total) }} FCFA</td>
                                    <td>
                                        {% if order.status.value == 'pending' %}
                                        <span class="badge bg-warning">En Attente</span>
                                        {% elif order.status.value == 'processing' %}
                                        <span class="badge bg-primary">En Cours</span>
                                        {% elif order.status.value == 'shipped' %}
                                        <span class="badge bg-secondary">Expédiée</span>
                                        {% elif order.status.value == 'delivered' %}
                                        <span class="badge bg-success">Livrée</span>
                                        {% elif order.status.value == 'cancelled' %}
                                        <span class="badge bg-danger">Annulée</span>
                                        {% elif order.status.value == 'refunded' %}
                                        <span class="badge bg-info">Remboursée</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ order.created_at.strftime('%d/%m/%Y') }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center">
                        <a href="{{ url_for('admin.manage_orders', shop_id=shop.id) }}" class="btn btn-outline-primary">
                            Voir toutes les commandes
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>Actions Rapides
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if shop.status.value == 'pending' %}
                        <form method="POST" action="{{ url_for('admin.approve_shop', shop_id=shop.id) }}" class="d-inline">
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-check me-2"></i>Approuver la Boutique
                            </button>
                        </form>
                        <form method="POST" action="{{ url_for('admin.reject_shop', shop_id=shop.id) }}" class="d-inline">
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="fas fa-times me-2"></i>Rejeter la Boutique
                            </button>
                        </form>
                        {% endif %}
                        
                        <a href="{{ url_for('shop.view_shop', shop_slug=shop.slug) }}" target="_blank" class="btn btn-primary">
                            <i class="fas fa-external-link-alt me-2"></i>Voir la Boutique
                        </a>
                        
                        {% if shop.phone or shop.owner.phone %}
                        <a href="https://wa.me/{{ (shop.phone or shop.owner.phone).replace('+', '').replace(' ', '') }}" 
                           target="_blank" class="btn btn-success">
                            <i class="fab fa-whatsapp me-2"></i>Contacter WhatsApp
                        </a>
                        {% endif %}
                        
                        <a href="mailto:{{ shop.owner.email }}" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-2"></i>Envoyer Email
                        </a>
                    </div>
                </div>
            </div>

            <!-- Shop Statistics -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Statistiques
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border rounded p-2">
                                <h6 class="text-primary">{{ shop_stats.total_products }}</h6>
                                <small class="text-muted">Produits Total</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-2">
                                <h6 class="text-success">{{ shop_stats.active_products }}</h6>
                                <small class="text-muted">Produits Actifs</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-2">
                                <h6 class="text-info">{{ shop_stats.total_orders }}</h6>
                                <small class="text-muted">Commandes</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-2">
                                <h6 class="text-warning">{{ "%.0f"|format(shop_stats.total_revenue) }}</h6>
                                <small class="text-muted">Revenus FCFA</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
