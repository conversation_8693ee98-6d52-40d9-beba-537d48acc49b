{% extends "base.html" %}

{% block title %}{% if template %}Modifier{% else %}Créer{% endif %} Template Email - Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        {% if template %}Modifier Template Email{% else %}Créer Template Email{% endif %}
                    </h5>
                    <a href="{{ url_for('email.templates') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>Retour
                    </a>
                </div>
                <div class="card-body">
                    <form method="POST" id="templateForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Nom du Template *</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="{{ template.name if template else '' }}" required>
                                    <div class="form-text">Nom descriptif pour identifier ce template</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email_type" class="form-label">Type d'Email *</label>
                                    <select class="form-select" id="email_type" name="email_type" required>
                                        <option value="">Sélectionner un type</option>
                                        {% for email_type in email_types %}
                                        <option value="{{ email_type.value }}" 
                                                {% if template and template.email_type == email_type %}selected{% endif %}>
                                            {{ email_type.value.replace('_', ' ').title() }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">Type d'email pour lequel ce template sera utilisé</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="subject" class="form-label">Sujet de l'Email *</label>
                            <input type="text" class="form-control" id="subject" name="subject" 
                                   value="{{ template.subject if template else '' }}" required>
                            <div class="form-text">
                                Vous pouvez utiliser des variables comme {{ "{{user_name}}" }}, {{ "{{shop_name}}" }}, etc.
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="html_content" class="form-label">Contenu HTML *</label>
                                    <textarea class="form-control" id="html_content" name="html_content" 
                                              rows="15" required>{{ template.html_content if template else '' }}</textarea>
                                    <div class="form-text">
                                        Contenu HTML de l'email. Utilisez des variables comme {{ "{{user_name}}" }} pour la personnalisation.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="text_content" class="form-label">Contenu Texte</label>
                                    <textarea class="form-control" id="text_content" name="text_content" 
                                              rows="8">{{ template.text_content if template else '' }}</textarea>
                                    <div class="form-text">
                                        Version texte de l'email (optionnel, mais recommandé pour la compatibilité)
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="variables" class="form-label">Variables Disponibles</label>
                                    <textarea class="form-control" id="variables" name="variables" 
                                              rows="4">{{ template.variables|tojson if template and template.variables else '{}' }}</textarea>
                                    <div class="form-text">
                                        Variables JSON disponibles pour ce template (format: {"variable": "description"})
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Variables Communes</label>
                                    <div class="border rounded p-3 bg-light">
                                        <small class="text-muted">
                                            <strong>Variables toujours disponibles :</strong><br>
                                            <code>{{ "{{user_name}}" }}</code> - Nom de l'utilisateur<br>
                                            <code>{{ "{{user_email}}" }}</code> - Email de l'utilisateur<br>
                                            <code>{{ "{{platform_name}}" }}</code> - Nom de la plateforme<br>
                                            <code>{{ "{{platform_url}}" }}</code> - URL de la plateforme<br>
                                            <code>{{ "{{current_date}}" }}</code> - Date actuelle<br>
                                            <code>{{ "{{unsubscribe_url}}" }}</code> - Lien de désabonnement
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {% if template %}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           {% if template.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        Template actif
                                    </label>
                                    <div class="form-text">
                                        Seuls les templates actifs peuvent être utilisés pour l'envoi d'emails
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    {% if template %}Mettre à jour{% else %}Créer{% endif %} Template
                                </button>
                                <button type="button" class="btn btn-outline-info ms-2" onclick="previewTemplate()">
                                    <i class="fas fa-eye me-2"></i>Aperçu
                                </button>
                            </div>
                            <a href="{{ url_for('email.templates') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Template Examples -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Exemples de Templates
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Template de Bienvenue</h6>
                            <div class="bg-light p-3 rounded">
                                <small>
                                    <strong>Sujet :</strong> Bienvenue sur {{ "{{platform_name}}" }} !<br>
                                    <strong>Variables :</strong> user_name, platform_name, platform_url
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">Template de Newsletter</h6>
                            <div class="bg-light p-3 rounded">
                                <small>
                                    <strong>Sujet :</strong> Newsletter {{ "{{platform_name}}" }} - {{ "{{newsletter_title}}" }}<br>
                                    <strong>Variables :</strong> subscriber_name, newsletter_content, unsubscribe_url
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6 class="text-primary">Template Boutique Approuvée</h6>
                            <div class="bg-light p-3 rounded">
                                <small>
                                    <strong>Sujet :</strong> Votre boutique {{ "{{shop_name}}" }} a été approuvée !<br>
                                    <strong>Variables :</strong> user_name, shop_name, shop_url
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">Template Commande</h6>
                            <div class="bg-light p-3 rounded">
                                <small>
                                    <strong>Sujet :</strong> Confirmation de commande #{{ "{{order_number}}" }}<br>
                                    <strong>Variables :</strong> user_name, order_number, order_total, order_items
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Aperçu du Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- Preview content will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function previewTemplate() {
    const htmlContent = document.getElementById('html_content').value;
    const subject = document.getElementById('subject').value;
    
    if (!htmlContent.trim()) {
        alert('Veuillez saisir le contenu HTML pour l\'aperçu');
        return;
    }
    
    // Simple preview with sample data
    let previewHtml = htmlContent;
    const sampleData = {
        'user_name': 'Jean Dupont',
        'user_email': '<EMAIL>',
        'platform_name': 'Afroly.org',
        'platform_url': 'https://afroly.org',
        'shop_name': 'Ma Boutique',
        'current_date': new Date().toLocaleDateString('fr-FR'),
        'order_number': '12345',
        'newsletter_title': 'Actualités du mois'
    };
    
    // Replace variables with sample data
    for (const [key, value] of Object.entries(sampleData)) {
        const regex = new RegExp(`{{ '{' }}{{ '{' }}\\s*${key}\\s*{{ '}' }}{{ '}' }}`, 'g');
        previewHtml = previewHtml.replace(regex, value);
    }
    
    document.getElementById('previewContent').innerHTML = `
        <div class="mb-3">
            <strong>Sujet :</strong> ${subject.replace(/{{ '{' }}{{ '{' }}\\s*user_name\\s*{{ '}' }}{{ '}' }}/g, 'Jean Dupont').replace(/{{ '{' }}{{ '{' }}\\s*platform_name\\s*{{ '}' }}{{ '}' }}/g, 'Afroly.org')}
        </div>
        <hr>
        <div style="border: 1px solid #ddd; padding: 20px; background: white;">
            ${previewHtml}
        </div>
    `;
    
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

// Auto-save draft functionality
let autoSaveTimer;
function autoSave() {
    clearTimeout(autoSaveTimer);
    autoSaveTimer = setTimeout(() => {
        // Here you could implement auto-save to localStorage or server
        console.log('Auto-saving template...');
    }, 5000);
}

// Add auto-save listeners
document.getElementById('html_content').addEventListener('input', autoSave);
document.getElementById('text_content').addEventListener('input', autoSave);
document.getElementById('subject').addEventListener('input', autoSave);
</script>
{% endblock %}
