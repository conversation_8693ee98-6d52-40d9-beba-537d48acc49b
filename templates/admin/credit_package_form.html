{% extends "admin/base.html" %}

{% block title %}{% if package %}Modifier{% else %}Créer{% endif %} un Package de Crédits - Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">{% if package %}Modifier{% else %}Créer{% endif %} un Package de Crédits</h1>
                    <p class="text-muted">{% if package %}Modifiez les détails du package{% else %}Créez un nouveau package de crédits{% endif %}</p>
                </div>
                <a href="{{ url_for('admin.manage_credit_packages') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour
                </a>
            </div>

            <!-- Form -->
            <div class="card">
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Nom du package <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="{{ package.name if package else '' }}" required>
                                    <div class="form-text">Ex: "Package Starter", "Package Pro"</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="display_order" class="form-label">Ordre d'affichage</label>
                                    <input type="number" class="form-control" id="display_order" name="display_order" 
                                           value="{{ package.display_order if package else 0 }}" min="0">
                                    <div class="form-text">Plus le nombre est élevé, plus le package sera affiché en premier</div>
                                </div>
                            </div>

                            <!-- Credits -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="credits" class="form-label">Nombre de crédits <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="credits" name="credits" 
                                           value="{{ package.credits if package else '' }}" required min="1" onchange="calculatePricePerCredit()">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="bonus_credits" class="form-label">Crédits bonus</label>
                                    <input type="number" class="form-control" id="bonus_credits" name="bonus_credits" 
                                           value="{{ package.bonus_credits if package else 0 }}" min="0" onchange="calculatePricePerCredit()">
                                    <div class="form-text">Crédits supplémentaires offerts gratuitement</div>
                                </div>
                            </div>

                            <!-- Pricing -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price_eur" class="form-label">Prix en EUR <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="price_eur" name="price_eur" 
                                               value="{{ package.price_eur if package else '' }}" required min="0.01" step="0.01" onchange="calculatePricePerCredit()">
                                        <span class="input-group-text">€</span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price_fcfa" class="form-label">Prix en FCFA <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="price_fcfa" name="price_fcfa" 
                                               value="{{ package.price_fcfa if package else '' }}" required min="1" step="1">
                                        <span class="input-group-text">FCFA</span>
                                    </div>
                                    <div class="form-text">
                                        <button type="button" class="btn btn-link btn-sm p-0" onclick="convertEurToFcfa()">
                                            Convertir depuis EUR (1€ = 655 FCFA)
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Price per credit display -->
                            <div class="col-12">
                                <div class="alert alert-info" id="price-per-credit" style="display: none;">
                                    <strong>Prix par crédit:</strong> <span id="price-per-credit-value"></span>
                                </div>
                            </div>

                            <!-- Description -->
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description (optionnel)</label>
                                    <textarea class="form-control" id="description" name="description" rows="3">{{ package.description if package else '' }}</textarea>
                                    <div class="form-text">Description courte du package</div>
                                </div>
                            </div>

                            <!-- Status -->
                            <div class="col-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           {% if not package or package.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        Package actif
                                    </label>
                                    <div class="form-text">Les packages inactifs ne sont pas visibles par les utilisateurs</div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ url_for('admin.manage_credit_packages') }}" class="btn btn-secondary">Annuler</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{% if package %}Mettre à jour{% else %}Créer{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Preview Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Aperçu du package</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header text-center">
                                    <h6 class="mb-0" id="preview-name">Nom du package</h6>
                                </div>
                                <div class="card-body text-center">
                                    <div class="display-6 fw-bold text-primary" id="preview-total-credits">0</div>
                                    <div class="text-muted">crédits</div>
                                    <div class="text-success small" id="preview-bonus" style="display: none;">
                                        <i class="fas fa-gift me-1"></i>+<span id="preview-bonus-value">0</span> bonus
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="fw-bold" id="preview-price-eur">0€</div>
                                            <small class="text-muted">EUR</small>
                                        </div>
                                        <div class="col-6">
                                            <div class="fw-bold" id="preview-price-fcfa">0 FCFA</div>
                                            <small class="text-muted">FCFA</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 d-flex align-items-center">
                            <div>
                                <h6>Comment ça marche:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>Les utilisateurs achètent des crédits</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Ils utilisent les crédits pour booster leurs produits</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Les produits boostés apparaissent en premier</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Plus de visibilité = plus de ventes</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function calculatePricePerCredit() {
    const credits = parseInt(document.getElementById('credits').value) || 0;
    const bonusCredits = parseInt(document.getElementById('bonus_credits').value) || 0;
    const priceEur = parseFloat(document.getElementById('price_eur').value) || 0;
    
    const totalCredits = credits + bonusCredits;
    const pricePerCredit = totalCredits > 0 ? (priceEur / totalCredits) : 0;
    
    const pricePerCreditDiv = document.getElementById('price-per-credit');
    const pricePerCreditValue = document.getElementById('price-per-credit-value');
    
    if (totalCredits > 0 && priceEur > 0) {
        pricePerCreditValue.textContent = pricePerCredit.toFixed(3) + '€ par crédit';
        pricePerCreditDiv.style.display = 'block';
    } else {
        pricePerCreditDiv.style.display = 'none';
    }
    
    updatePreview();
}

function convertEurToFcfa() {
    const priceEur = parseFloat(document.getElementById('price_eur').value) || 0;
    const priceFcfa = Math.round(priceEur * 655);
    document.getElementById('price_fcfa').value = priceFcfa;
    updatePreview();
}

function updatePreview() {
    const name = document.getElementById('name').value || 'Nom du package';
    const credits = parseInt(document.getElementById('credits').value) || 0;
    const bonusCredits = parseInt(document.getElementById('bonus_credits').value) || 0;
    const priceEur = parseFloat(document.getElementById('price_eur').value) || 0;
    const priceFcfa = parseFloat(document.getElementById('price_fcfa').value) || 0;
    
    document.getElementById('preview-name').textContent = name;
    document.getElementById('preview-total-credits').textContent = credits + bonusCredits;
    document.getElementById('preview-price-eur').textContent = priceEur + '€';
    document.getElementById('preview-price-fcfa').textContent = Math.round(priceFcfa) + ' FCFA';
    
    const bonusDiv = document.getElementById('preview-bonus');
    const bonusValue = document.getElementById('preview-bonus-value');
    if (bonusCredits > 0) {
        bonusValue.textContent = bonusCredits;
        bonusDiv.style.display = 'block';
    } else {
        bonusDiv.style.display = 'none';
    }
}

// Add event listeners
document.addEventListener('DOMContentLoaded', function() {
    ['name', 'credits', 'bonus_credits', 'price_eur', 'price_fcfa'].forEach(id => {
        document.getElementById(id).addEventListener('input', updatePreview);
    });
    
    updatePreview();
    calculatePricePerCredit();
});
</script>
{% endblock %}
