{% extends "base.html" %}

{% block title %}Détails Commande #{{ order.id }} - Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-receipt me-3 text-primary"></i>Commande #{{ order.id }}
                    </h2>
                    <p class="text-muted mb-0">Détails de la commande et gestion administrative</p>
                </div>
                <div>
                    <a href="{{ url_for('admin.manage_orders') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Retour aux Commandes
                    </a>
                    <button class="btn btn-outline-primary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>Imprimer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Status Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center border-primary">
                <div class="card-body">
                    <i class="fas fa-info-circle fa-2x text-primary mb-2"></i>
                    <h6>Statut</h6>
                    {% if order.status.value == 'pending' %}
                    <span class="badge bg-warning fs-6">En Attente</span>
                    {% elif order.status.value == 'processing' %}
                    <span class="badge bg-primary fs-6">En Cours</span>
                    {% elif order.status.value == 'shipped' %}
                    <span class="badge bg-secondary fs-6">Expédiée</span>
                    {% elif order.status.value == 'delivered' %}
                    <span class="badge bg-success fs-6">Livrée</span>
                    {% elif order.status.value == 'cancelled' %}
                    <span class="badge bg-danger fs-6">Annulée</span>
                    {% elif order.status.value == 'refunded' %}
                    <span class="badge bg-info fs-6">Remboursée</span>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-success">
                <div class="card-body">
                    <i class="fas fa-credit-card fa-2x text-success mb-2"></i>
                    <h6>Paiement</h6>
                    {% if order.payment_status == 'paid' %}
                    <span class="badge bg-success fs-6">Payée</span>
                    {% elif order.payment_status == 'pending' %}
                    <span class="badge bg-warning fs-6">En Attente</span>
                    {% else %}
                    <span class="badge bg-secondary fs-6">Non Payée</span>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-info">
                <div class="card-body">
                    <i class="fas fa-calendar fa-2x text-info mb-2"></i>
                    <h6>Date</h6>
                    <small>{{ order.created_at.strftime('%d/%m/%Y') }}</small><br>
                    <small class="text-muted">{{ order.created_at.strftime('%H:%M') }}</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-warning">
                <div class="card-body">
                    <i class="fas fa-euro-sign fa-2x text-warning mb-2"></i>
                    <h6>Total</h6>
                    <strong class="fs-5">{{ "%.0f"|format(order.total) }} FCFA</strong>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Order Details -->
        <div class="col-lg-8">
            <!-- Customer Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>Informations Client
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Nom:</strong></td>
                                    <td>
                                        {% if order.customer %}
                                            <a href="{{ url_for('admin.view_user', user_id=order.customer.id) }}" class="text-decoration-none">
                                                {{ order.customer.get_full_name() }}
                                            </a>
                                        {% else %}
                                            Commande manuelle
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>
                                        {% if order.customer %}
                                            {{ order.customer.email }}
                                        {% else %}
                                            N/A
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Téléphone:</strong></td>
                                    <td>
                                        {% if order.customer and order.customer.phone %}
                                            <span class="fw-bold text-success">{{ order.customer.phone }}</span>
                                            <div class="mt-1">
                                                <a href="#" onclick="contactWhatsApp('{{ order.customer.phone }}')" class="btn btn-sm btn-success me-1">
                                                    <i class="fab fa-whatsapp"></i> WhatsApp
                                                </a>
                                                <a href="tel:{{ order.customer.phone }}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-phone"></i> Appeler
                                                </a>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">Non disponible</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Boutique:</strong></td>
                                    <td>
                                        {% if order.shop %}
                                            <a href="{{ url_for('admin.view_shop', shop_id=order.shop.id) }}" class="text-decoration-none">
                                                {{ order.shop.name }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">Boutique supprimée</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Méthode de Paiement:</strong></td>
                                    <td>{{ order.payment_method or 'Non spécifiée' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Numéro de Commande:</strong></td>
                                    <td>{{ order.order_number or '#' + order.id|string }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Shipping Address -->
            {% if order.shipping_address %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>Adresse de Livraison
                    </h5>
                </div>
                <div class="card-body">
                    {% if order.shipping_address is string %}
                        <p class="mb-0">{{ order.shipping_address }}</p>
                    {% else %}
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>{{ order.shipping_address.get('first_name', '') }} {{ order.shipping_address.get('last_name', '') }}</strong></p>
                                <p class="mb-1">{{ order.shipping_address.get('address_line_1', '') }}</p>
                                {% if order.shipping_address.get('address_line_2') %}
                                <p class="mb-1">{{ order.shipping_address.get('address_line_2') }}</p>
                                {% endif %}
                                <p class="mb-1">{{ order.shipping_address.get('city', '') }}, {{ order.shipping_address.get('state', '') }} {{ order.shipping_address.get('postal_code', '') }}</p>
                                <p class="mb-1">{{ order.shipping_address.get('country', '') }}</p>
                                {% if order.shipping_address.get('phone') %}
                                <p class="mb-0"><strong>Tél:</strong> {{ order.shipping_address.get('phone') }}</p>
                                {% endif %}
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Order Items -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>Articles Commandés
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Prix Unitaire</th>
                                    <th>Quantité</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in order.items %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{{ item.product.get_main_image() }}" alt="{{ item.product.name }}"
                                                 class="rounded me-3" style="width: 60px; height: 60px; object-fit: cover;">
                                            <div>
                                                <div class="fw-bold">{{ item.product.name }}</div>
                                                <small class="text-muted">SKU: {{ item.product.sku or 'N/A' }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ "%.0f"|format(item.price) }} FCFA</td>
                                    <td>{{ item.quantity }}</td>
                                    <td class="fw-bold">{{ "%.0f"|format(item.total) }} FCFA</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Order Totals -->
                    <div class="row">
                        <div class="col-md-6 ms-auto">
                            <table class="table">
                                <tr>
                                    <td>Sous-total:</td>
                                    <td class="text-end">{{ "%.0f"|format(order.subtotal) }} FCFA</td>
                                </tr>
                                {% if order.shipping_cost > 0 %}
                                <tr>
                                    <td>Livraison:</td>
                                    <td class="text-end">{{ "%.0f"|format(order.shipping_cost) }} FCFA</td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td>Livraison:</td>
                                    <td class="text-end text-success">Gratuite</td>
                                </tr>
                                {% endif %}
                                {% if order.tax_amount > 0 %}
                                <tr>
                                    <td>Taxes:</td>
                                    <td class="text-end">{{ "%.0f"|format(order.tax_amount) }} FCFA</td>
                                </tr>
                                {% endif %}
                                <tr class="table-primary">
                                    <td><strong>Total:</strong></td>
                                    <td class="text-end"><strong>{{ "%.0f"|format(order.total) }} FCFA</strong></td>
                                </tr>
                                {% if order.commission_amount %}
                                <tr class="table-warning">
                                    <td><strong>Commission Plateforme:</strong></td>
                                    <td class="text-end"><strong>{{ "%.0f"|format(order.commission_amount) }} FCFA</strong></td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Admin Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>Actions Administratives
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('admin.update_order_status', order_id=order.id) }}">
                        <div class="mb-3">
                            <label for="status" class="form-label">Statut de la Commande:</label>
                            <select class="form-select" id="status" name="status">
                                <option value="pending" {% if order.status.value == 'pending' %}selected{% endif %}>En Attente</option>
                                <option value="processing" {% if order.status.value == 'processing' %}selected{% endif %}>En Cours</option>
                                <option value="shipped" {% if order.status.value == 'shipped' %}selected{% endif %}>Expédiée</option>
                                <option value="delivered" {% if order.status.value == 'delivered' %}selected{% endif %}>Livrée</option>
                                <option value="cancelled" {% if order.status.value == 'cancelled' %}selected{% endif %}>Annulée</option>
                                <option value="refunded" {% if order.status.value == 'refunded' %}selected{% endif %}>Remboursée</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="admin_notes" class="form-label">Notes Administratives:</label>
                            <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3" placeholder="Ajouter une note..."></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-save me-2"></i>Mettre à Jour
                        </button>
                    </form>
                </div>
            </div>

            <!-- Notes -->
            {% if order.customer_notes or order.admin_notes %}
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-sticky-note me-2"></i>Notes
                    </h5>
                </div>
                <div class="card-body">
                    {% if order.customer_notes %}
                    <div class="mb-3">
                        <h6 class="text-primary">Notes du Client:</h6>
                        <p class="text-muted">{{ order.customer_notes }}</p>
                    </div>
                    {% endif %}
                    {% if order.admin_notes %}
                    <div>
                        <h6 class="text-warning">Notes Administratives:</h6>
                        <p class="text-muted">{{ order.admin_notes|nl2br }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function contactWhatsApp(phone) {
    const cleanPhone = phone.replace(/[^0-9]/g, '');
    const message = encodeURIComponent('Bonjour, concernant votre commande sur Afroly.org');
    window.open(`https://wa.me/${cleanPhone}?text=${message}`, '_blank');
}
</script>
{% endblock %}
