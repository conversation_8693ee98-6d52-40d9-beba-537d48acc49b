{% extends "base.html" %}

{% block title %}Gestion des Catégories - Admin - Afroly.org{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-6 fw-bold">
                <i class="fas fa-tags me-3 text-warning"></i>Gestion des Catégories
            </h1>
            <p class="lead text-muted">Organiser les catégories de produits</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('admin.create_category') }}" class="btn btn-success me-2">
                <i class="fas fa-plus me-2"></i>Nouvelle Catégorie
            </a>
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Dashboard
            </a>
        </div>
    </div>

    <!-- Categories List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-list me-2"></i>Liste des Catégories
                        <span class="badge bg-warning ms-2">{{ categories|length }} total</span>
                    </h6>
                </div>
                <div class="card-body">
                    {% if categories %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Nom</th>
                                    <th>Description</th>
                                    <th>Parent</th>
                                    <th>Produits</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for category in categories %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-tag fa-lg text-warning me-3"></i>
                                            <div>
                                                <h6 class="mb-0">{{ category.name }}</h6>
                                                <small class="text-muted">{{ category.slug }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if category.description %}
                                        <small>{{ category.description[:100] }}{% if category.description|length > 100 %}...{% endif %}</small>
                                        {% else %}
                                        <small class="text-muted">Aucune description</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if category.parent %}
                                        <span class="badge bg-light text-dark">{{ category.parent.name }}</span>
                                        {% else %}
                                        <span class="badge bg-primary">Catégorie Principale</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ category.products|length }} produits</span>
                                        {% if category.children %}
                                        <br><small class="text-muted">{{ category.children|length }} sous-catégories</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('main.category_products', category_slug=category.slug) }}" 
                                               class="btn btn-sm btn-outline-info" target="_blank">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-primary" 
                                                    onclick="editCategory({{ category.id }}, '{{ category.name }}', '{{ category.description or '' }}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            {% if category.products|length == 0 and category.children|length == 0 %}
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteCategory({{ category.id }}, '{{ category.name }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                
                                <!-- Show subcategories -->
                                {% for subcategory in category.children %}
                                <tr class="table-light">
                                    <td class="ps-5">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-arrow-right text-muted me-2"></i>
                                            <i class="fas fa-tag text-secondary me-3"></i>
                                            <div>
                                                <h6 class="mb-0">{{ subcategory.name }}</h6>
                                                <small class="text-muted">{{ subcategory.slug }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if subcategory.description %}
                                        <small>{{ subcategory.description[:80] }}{% if subcategory.description|length > 80 %}...{% endif %}</small>
                                        {% else %}
                                        <small class="text-muted">Aucune description</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">{{ category.name }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ subcategory.products|length }} produits</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('main.category_products', category_slug=subcategory.slug) }}" 
                                               class="btn btn-sm btn-outline-info" target="_blank">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-primary" 
                                                    onclick="editCategory({{ subcategory.id }}, '{{ subcategory.name }}', '{{ subcategory.description or '' }}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            {% if subcategory.products|length == 0 %}
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteCategory({{ subcategory.id }}, '{{ subcategory.name }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-tags fa-4x text-muted mb-4"></i>
                        <h4>Aucune catégorie</h4>
                        <p class="text-muted">Commencez par créer votre première catégorie.</p>
                        <a href="{{ url_for('admin.create_category') }}" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Créer une Catégorie
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Modifier la Catégorie</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editCategoryForm" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Nom</label>
                        <input type="text" class="form-control" name="name" id="editCategoryName" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="description" id="editCategoryDescription" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Sauvegarder</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function editCategory(id, name, description) {
    document.getElementById('editCategoryName').value = name;
    document.getElementById('editCategoryDescription').value = description;
    document.getElementById('editCategoryForm').action = `/admin/categories/${id}/edit`;
    
    const modal = new bootstrap.Modal(document.getElementById('editCategoryModal'));
    modal.show();
}

function deleteCategory(id, name) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer la catégorie "${name}" ?`)) {
        fetch(`/admin/categories/${id}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur lors de la suppression');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur lors de la suppression');
        });
    }
}
</script>
{% endblock %}
