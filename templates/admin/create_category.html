{% extends "base.html" %}

{% block title %}Créer une Catégorie - Admin - Afroly.org{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-6 fw-bold">
                <i class="fas fa-plus me-3 text-success"></i>Créer une Nouvelle Catégorie
            </h1>
            <p class="lead text-muted">Ajouter une nouvelle catégorie de produits</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('admin.manage_categories') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Retour aux Catégories
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-tag me-2"></i>Informations de la Catégorie
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-4">
                            <label class="form-label">Nom de la Catégorie <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="name" required 
                                   placeholder="Ex: Vêtements, Électronique, Maison...">
                            <small class="text-muted">Le nom sera automatiquement formaté pour l'URL</small>
                        </div>

                        <div class="mb-4">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" name="description" rows="4" 
                                      placeholder="Description de la catégorie (optionnel)"></textarea>
                            <small class="text-muted">Une description courte pour aider les utilisateurs</small>
                        </div>

                        <div class="mb-4">
                            <label class="form-label">Catégorie Parent</label>
                            <select class="form-select" name="parent_id">
                                <option value="">Catégorie Principale (pas de parent)</option>
                                {% for parent in parent_categories %}
                                <option value="{{ parent.id }}">{{ parent.name }}</option>
                                {% endfor %}
                            </select>
                            <small class="text-muted">Sélectionnez une catégorie parent pour créer une sous-catégorie</small>
                        </div>

                        <!-- Preview Section -->
                        <div class="mb-4">
                            <label class="form-label">Aperçu</label>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-tag fa-lg text-warning me-3"></i>
                                        <div>
                                            <h6 class="mb-0" id="preview-name">Nom de la catégorie</h6>
                                            <small class="text-muted" id="preview-slug">nom-de-la-categorie</small>
                                            <div id="preview-description" class="mt-1" style="display: none;">
                                                <small class="text-muted"></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2 justify-content-end">
                            <a href="{{ url_for('admin.manage_categories') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Créer la Catégorie
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Help Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Conseils pour les Catégories
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Catégories Principales Suggérées :</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-tshirt me-2 text-primary"></i>Mode & Vêtements</li>
                                <li><i class="fas fa-laptop me-2 text-info"></i>Électronique & High-Tech</li>
                                <li><i class="fas fa-home me-2 text-success"></i>Maison & Jardin</li>
                                <li><i class="fas fa-heart me-2 text-danger"></i>Santé & Beauté</li>
                                <li><i class="fas fa-gamepad me-2 text-warning"></i>Sports & Loisirs</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Bonnes Pratiques :</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check me-2 text-success"></i>Utilisez des noms clairs et descriptifs</li>
                                <li><i class="fas fa-check me-2 text-success"></i>Évitez les noms trop longs</li>
                                <li><i class="fas fa-check me-2 text-success"></i>Organisez en hiérarchie logique</li>
                                <li><i class="fas fa-check me-2 text-success"></i>Pensez à l'expérience utilisateur</li>
                                <li><i class="fas fa-check me-2 text-success"></i>Ajoutez des descriptions utiles</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Live preview functionality
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.querySelector('input[name="name"]');
    const descriptionInput = document.querySelector('textarea[name="description"]');
    const previewName = document.getElementById('preview-name');
    const previewSlug = document.getElementById('preview-slug');
    const previewDescription = document.getElementById('preview-description');

    function updatePreview() {
        const name = nameInput.value.trim();
        const description = descriptionInput.value.trim();

        if (name) {
            previewName.textContent = name;
            // Generate slug preview
            const slug = name.toLowerCase()
                .replace(/[àáâãäå]/g, 'a')
                .replace(/[èéêë]/g, 'e')
                .replace(/[ìíîï]/g, 'i')
                .replace(/[òóôõö]/g, 'o')
                .replace(/[ùúûü]/g, 'u')
                .replace(/[ç]/g, 'c')
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .replace(/^-|-$/g, '');
            previewSlug.textContent = slug || 'nom-de-la-categorie';
        } else {
            previewName.textContent = 'Nom de la catégorie';
            previewSlug.textContent = 'nom-de-la-categorie';
        }

        if (description) {
            previewDescription.style.display = 'block';
            previewDescription.querySelector('small').textContent = description;
        } else {
            previewDescription.style.display = 'none';
        }
    }

    nameInput.addEventListener('input', updatePreview);
    descriptionInput.addEventListener('input', updatePreview);
});
</script>
{% endblock %}
