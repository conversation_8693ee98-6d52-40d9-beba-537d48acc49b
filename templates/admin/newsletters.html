{% extends "base.html" %}

{% block title %}Newsletters - Admin{% endblock %}

{% block content %}
<!-- Email-specific Flash Messages -->
{% include 'components/email_flash_messages.html' %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-newspaper me-2"></i>Gestion des Newsletters
                    </h5>
                    <div>
                        <a href="{{ url_for('email.subscribers') }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-users me-1"></i>Abonnés
                        </a>
                        <a href="{{ url_for('email.create_newsletter') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>Nouvelle Newsletter
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if newsletters %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Titre</th>
                                    <th>Sujet</th>
                                    <th>Audience</th>
                                    <th>Statut</th>
                                    <th>Statistiques</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for newsletter in newsletters %}
                                <tr>
                                    <td>
                                        <strong>{{ newsletter.title }}</strong>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ newsletter.subject[:40] }}{% if newsletter.subject|length > 40 %}...{% endif %}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if newsletter.target_audience == 'all' %}primary{% elif newsletter.target_audience == 'vendors' %}success{% else %}info{% endif %}">
                                            {% if newsletter.target_audience == 'all' %}Tous
                                            {% elif newsletter.target_audience == 'vendors' %}Vendeurs
                                            {% elif newsletter.target_audience == 'shoppers' %}Acheteurs
                                            {% else %}{{ newsletter.target_audience.title() }}
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        {% if newsletter.status == 'draft' %}
                                        <span class="badge bg-secondary">Brouillon</span>
                                        {% elif newsletter.status == 'scheduled' %}
                                        <span class="badge bg-warning">Programmée</span>
                                        {% elif newsletter.status == 'sending' %}
                                        <span class="badge bg-info">En cours</span>
                                        {% elif newsletter.status == 'sent' %}
                                        <span class="badge bg-success">Envoyée</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if newsletter.status == 'sent' %}
                                        <small class="text-muted">
                                            {{ newsletter.sent_count }}/{{ newsletter.total_recipients }} envoyés
                                            {% if newsletter.failed_count > 0 %}
                                            <br><span class="text-danger">{{ newsletter.failed_count }} échecs</span>
                                            {% endif %}
                                        </small>
                                        {% else %}
                                        <small class="text-muted">-</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {% if newsletter.sent_at %}
                                            Envoyée: {{ newsletter.sent_at.strftime('%d/%m/%Y %H:%M') }}
                                            {% elif newsletter.scheduled_at %}
                                            Programmée: {{ newsletter.scheduled_at.strftime('%d/%m/%Y %H:%M') }}
                                            {% else %}
                                            Créée: {{ newsletter.created_at.strftime('%d/%m/%Y %H:%M') }}
                                            {% endif %}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            {% if newsletter.status == 'draft' %}
                                            <a href="{{ url_for('email.edit_newsletter', newsletter_id=newsletter.id) }}" 
                                               class="btn btn-outline-primary" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-outline-success" 
                                                    onclick="sendNewsletter({{ newsletter.id }})" title="Envoyer">
                                                <i class="fas fa-paper-plane"></i>
                                            </button>
                                            {% elif newsletter.status == 'scheduled' %}
                                            <button class="btn btn-outline-warning" 
                                                    onclick="cancelNewsletter({{ newsletter.id }})" title="Annuler">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            {% endif %}
                                            <button class="btn btn-outline-info" 
                                                    onclick="previewNewsletter({{ newsletter.id }})" title="Aperçu">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            {% if newsletter.status == 'sent' %}
                                            <button class="btn btn-outline-secondary" 
                                                    onclick="viewStats({{ newsletter.id }})" title="Statistiques">
                                                <i class="fas fa-chart-bar"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune newsletter</h5>
                        <p class="text-muted">Créez votre première newsletter pour communiquer avec vos utilisateurs.</p>
                        <a href="{{ url_for('email.create_newsletter') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Créer une Newsletter
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Newsletter Statistics -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-primary">{{ newsletters|length }}</h3>
                    <small class="text-muted">Newsletters Totales</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-success">{{ newsletters|selectattr('status', 'equalto', 'sent')|list|length }}</h3>
                    <small class="text-muted">Newsletters Envoyées</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-warning">{{ newsletters|selectattr('status', 'equalto', 'draft')|list|length }}</h3>
                    <small class="text-muted">Brouillons</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-info">{{ newsletters|map(attribute='total_recipients')|sum }}</h3>
                    <small class="text-muted">Emails Envoyés</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Tips -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Conseils pour vos Newsletters
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Bonnes Pratiques</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Utilisez un sujet accrocheur</li>
                                <li><i class="fas fa-check text-success me-2"></i>Personnalisez avec le nom du destinataire</li>
                                <li><i class="fas fa-check text-success me-2"></i>Incluez un appel à l'action clair</li>
                                <li><i class="fas fa-check text-success me-2"></i>Testez avant l'envoi massif</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">Audiences Disponibles</h6>
                            <ul class="list-unstyled">
                                <li><span class="badge bg-primary me-2">Tous</span>Tous les abonnés</li>
                                <li><span class="badge bg-success me-2">Vendeurs</span>Utilisateurs vendeurs uniquement</li>
                                <li><span class="badge bg-info me-2">Acheteurs</span>Utilisateurs acheteurs uniquement</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Aperçu Newsletter</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- Preview content will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function sendNewsletter(newsletterId) {
    if (confirm('Êtes-vous sûr de vouloir envoyer cette newsletter ? Cette action est irréversible.')) {
        fetch(`/admin/email/newsletters/${newsletterId}/send`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Newsletter envoyée avec succès !');
                location.reload();
            } else {
                alert('Erreur : ' + data.message);
            }
        })
        .catch(error => {
            alert('Erreur lors de l\'envoi');
        });
    }
}

function cancelNewsletter(newsletterId) {
    if (confirm('Annuler l\'envoi programmé de cette newsletter ?')) {
        fetch(`/admin/email/newsletters/${newsletterId}/cancel`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Newsletter annulée avec succès !');
                location.reload();
            } else {
                alert('Erreur : ' + data.message);
            }
        })
        .catch(error => {
            alert('Erreur lors de l\'annulation');
        });
    }
}

function previewNewsletter(newsletterId) {
    fetch(`/admin/email/newsletters/${newsletterId}/preview`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('previewContent').innerHTML = html;
            new bootstrap.Modal(document.getElementById('previewModal')).show();
        })
        .catch(error => {
            alert('Erreur lors du chargement de l\'aperçu');
        });
}

function viewStats(newsletterId) {
    window.open(`/admin/email/newsletters/${newsletterId}/stats`, '_blank');
}
</script>
{% endblock %}
