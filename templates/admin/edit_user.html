{% extends "base.html" %}

{% block title %}Modifier Utilisateur - Admin - Afroly.org{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-6 fw-bold">
                <i class="fas fa-user-edit me-3 text-primary"></i>Modifier l'Utilisateur
            </h1>
            <p class="lead text-muted">Modifier les informations de {{ user.get_full_name() }}</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('admin.manage_users') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Retour aux Utilisateurs
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-user me-2"></i>Informations de l'Utilisateur
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <!-- User Basic Info (Read-only) -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label">Prénom</label>
                                <input type="text" class="form-control" value="{{ user.first_name }}" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Nom</label>
                                <input type="text" class="form-control" value="{{ user.last_name }}" readonly>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" value="{{ user.email }}" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Téléphone</label>
                                <input type="text" class="form-control" value="{{ user.phone or 'Non fourni' }}" readonly>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label">Date d'inscription</label>
                                <input type="text" class="form-control" value="{{ user.created_at.strftime('%d/%m/%Y %H:%M') }}" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Dernière connexion</label>
                                <input type="text" class="form-control" value="{{ user.last_login.strftime('%d/%m/%Y %H:%M') if user.last_login else 'Jamais' }}" readonly>
                            </div>
                        </div>

                        <hr>

                        <!-- Editable Fields -->
                        <h6 class="mb-3">Paramètres Modifiables</h6>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Rôle</label>
                                <select class="form-select" name="role" required>
                                    <option value="admin" {% if user.role.value == 'admin' %}selected{% endif %}>Administrateur</option>
                                    <option value="vendor" {% if user.role.value == 'vendor' %}selected{% endif %}>Vendeur</option>
                                    <option value="shopper" {% if user.role.value == 'shopper' %}selected{% endif %}>Acheteur</option>
                                </select>
                                <small class="text-muted">Définit les permissions de l'utilisateur</small>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Niveau</label>
                                <select class="form-select" name="tier" required>
                                    <option value="free" {% if user.tier.value == 'free' %}selected{% endif %}>Gratuit</option>
                                    <option value="premium" {% if user.tier.value == 'premium' %}selected{% endif %}>Premium</option>
                                    <option value="gold" {% if user.tier.value == 'gold' %}selected{% endif %}>Gold</option>
                                </select>
                                <small class="text-muted">Détermine les limites et fonctionnalités</small>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="is_active" id="is_active" 
                                           {% if user.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        Compte Actif
                                    </label>
                                </div>
                                <small class="text-muted">L'utilisateur peut se connecter et utiliser la plateforme</small>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="email_verified" id="email_verified" 
                                           {% if user.email_verified %}checked{% endif %}>
                                    <label class="form-check-label" for="email_verified">
                                        Email Vérifié
                                    </label>
                                </div>
                                <small class="text-muted">L'adresse email a été confirmée</small>
                            </div>
                        </div>

                        <!-- User Statistics -->
                        <hr>
                        <h6 class="mb-3">Statistiques</h6>

                        {% if user.role.value == 'vendor' %}
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h5 class="text-primary">{{ user.shops|length }}</h5>
                                        <small>Boutiques</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h5 class="text-success">
                                            {% set total_products = 0 %}
                                            {% for shop in user.shops %}
                                                {% set total_products = total_products + shop.products|length %}
                                            {% endfor %}
                                            {{ total_products }}
                                        </h5>
                                        <small>Produits</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h5 class="text-info">
                                            {% set total_orders = 0 %}
                                            {% for shop in user.shops %}
                                                {% set total_orders = total_orders + shop.orders|length %}
                                            {% endfor %}
                                            {{ total_orders }}
                                        </h5>
                                        <small>Commandes</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h5 class="text-info">
                                            {{ user.credit_balance }}
                                        </h5>
                                        <small>Crédits</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2 justify-content-end">
                            <a href="{{ url_for('admin.manage_users') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Sauvegarder les Modifications
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- User's Shops (if vendor) -->
            {% if user.role.value == 'vendor' and user.shops %}
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-store me-2"></i>Boutiques de l'Utilisateur
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for shop in user.shops %}
                        <div class="col-md-6 mb-3">
                            <div class="card border">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        {% if shop.logo %}
                                        <img src="/static/uploads/shops/{{ shop.logo }}" alt="{{ shop.name }}"
                                             class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                        {% else %}
                                        <i class="fas fa-store fa-2x text-success me-3"></i>
                                        {% endif %}
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">{{ shop.name }}</h6>
                                            <small class="text-muted">{{ shop.products|length }} produits</small>
                                            <br><span class="badge bg-{{ 'success' if shop.status.value == 'active' else 'warning' if shop.status.value == 'pending' else 'danger' }}">
                                                {{ shop.status.value.title() }}
                                            </span>
                                        </div>
                                        <div>
                                            <a href="{{ url_for('shop.view', slug=shop.slug) }}" 
                                               class="btn btn-sm btn-outline-primary" target="_blank">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
