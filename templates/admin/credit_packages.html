{% extends "base.html" %}

{% block title %}Gestion des Packages de Crédits - Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Gestion des Packages de Crédits</h1>
                    <p class="text-muted">Configurez les packages de crédits pour le boost de produits</p>
                </div>
                <a href="{{ url_for('admin.create_credit_package') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Nouveau Package
                </a>
            </div>

            <!-- Packages Grid -->
            {% if packages %}
            <div class="row">
                {% for package in packages %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 {% if not package.is_active %}opacity-75{% endif %}">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">{{ package.name }}</h5>
                            {% if package.is_active %}
                            <span class="badge bg-success">Actif</span>
                            {% else %}
                            <span class="badge bg-secondary">Inactif</span>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <div class="display-6 fw-bold text-primary">{{ package.get_total_credits() }}</div>
                                <div class="text-muted">crédits</div>
                                {% if package.bonus_credits > 0 %}
                                <small class="text-success">
                                    <i class="fas fa-gift me-1"></i>+{{ package.bonus_credits }} bonus
                                </small>
                                {% endif %}
                            </div>
                            
                            <div class="row text-center mb-3">
                                <div class="col-6">
                                    <div class="fw-bold">{{ package.price_eur }}€</div>
                                    <small class="text-muted">EUR</small>
                                </div>
                                <div class="col-6">
                                    <div class="fw-bold">{{ "%.0f"|format(package.price_fcfa) }} FCFA</div>
                                    <small class="text-muted">FCFA</small>
                                </div>
                            </div>
                            
                            <div class="text-center mb-3">
                                <small class="text-muted">
                                    {{ "%.3f"|format(package.get_price_per_credit_eur()) }}€ par crédit
                                </small>
                            </div>
                            
                            {% if package.description %}
                            <p class="card-text small text-muted">{{ package.description }}</p>
                            {% endif %}
                            
                            <div class="small text-muted">
                                <div>Ordre: {{ package.display_order }}</div>
                                <div>Créé: {{ package.created_at.strftime('%d/%m/%Y') }}</div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100">
                                <a href="{{ url_for('admin.edit_credit_package', package_id=package.id) }}" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-edit me-1"></i>Modifier
                                </a>
                                <form method="POST" action="{{ url_for('admin.delete_credit_package', package_id=package.id) }}" 
                                      class="d-inline" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer ce package ?')">
                                    <button type="submit" class="btn btn-outline-danger btn-sm">
                                        <i class="fas fa-trash me-1"></i>Supprimer
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Summary Stats -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">{{ packages|length }}</h5>
                            <p class="card-text text-muted">Packages Total</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">{{ packages|selectattr('is_active')|list|length }}</h5>
                            <p class="card-text text-muted">Packages Actifs</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">{{ packages|map(attribute='price_eur')|min if packages else 0 }}€</h5>
                            <p class="card-text text-muted">Prix Min</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">{{ packages|map(attribute='price_eur')|max if packages else 0 }}€</h5>
                            <p class="card-text text-muted">Prix Max</p>
                        </div>
                    </div>
                </div>
            </div>

            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-coins fa-3x text-muted mb-3"></i>
                <h4>Aucun package de crédits</h4>
                <p class="text-muted">Créez votre premier package de crédits pour permettre aux utilisateurs d'acheter des crédits.</p>
                <a href="{{ url_for('admin.create_credit_package') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Créer un Package
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
