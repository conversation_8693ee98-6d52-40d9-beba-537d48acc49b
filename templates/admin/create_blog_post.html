{% extends "base.html" %}

{% block title %}Créer un Article - Admin - Afroly.org{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-6 fw-bold">
                <i class="fas fa-plus me-3 text-success"></i>Créer un Article
            </h1>
            <p class="lead text-muted">Créer un nouvel article de blog</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#aiGenerateModal">
                    <i class="fas fa-robot me-2"></i>Générer avec IA
                </button>
                <a href="{{ url_for('admin.bulk_import_blog') }}" class="btn btn-outline-info">
                    <i class="fas fa-file-import me-2"></i>Import CSV/Excel
                </a>
            </div>
            <a href="{{ url_for('admin.manage_blog') }}" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-arrow-left me-2"></i>Retour aux Articles
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Contenu de l'Article
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" id="blogForm">
                        <!-- AI Generation Fields (hidden by default) -->
                        <div id="aiFields" style="display: none;">
                            <div class="alert alert-info">
                                <i class="fas fa-robot me-2"></i>
                                <strong>Mode IA Activé:</strong> Le contenu sera généré automatiquement basé sur votre prompt.
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Prompt IA <span class="text-danger">*</span></label>
                                <textarea class="form-control" name="ai_prompt" rows="3"
                                          placeholder="Décrivez le sujet de l'article que vous voulez générer..."></textarea>
                                <small class="text-muted">Exemple: "Écris un article sur les meilleures stratégies de marketing digital pour les PME africaines"</small>
                            </div>
                            <input type="hidden" name="generate_ai" value="1">
                        </div>

                        <!-- Title -->
                        <div class="mb-3">
                            <label class="form-label">Titre <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="title" id="titleField"
                                   placeholder="Entrez le titre de l'article...">
                            <small class="text-muted" id="titleHelp">Laissez vide pour génération automatique avec l'IA</small>
                        </div>

                        <!-- Excerpt -->
                        <div class="mb-3">
                            <label class="form-label">Extrait</label>
                            <textarea class="form-control" name="excerpt" rows="3"
                                      placeholder="Résumé court de l'article (optionnel)..."></textarea>
                            <small class="text-muted">Résumé qui apparaîtra dans les listes d'articles</small>
                        </div>

                        <!-- Content -->
                        <div class="mb-3">
                            <label class="form-label">Contenu <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="content" id="contentField" rows="15"
                                      placeholder="Écrivez le contenu de votre article ici..."></textarea>
                            <small class="text-muted" id="contentHelp">Utilisez du HTML ou Markdown pour le formatage</small>
                        </div>

                        <!-- Category and Tags -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Catégorie</label>
                                <select class="form-select" name="category">
                                    <option value="">Sélectionner une catégorie</option>
                                    <option value="E-commerce">E-commerce</option>
                                    <option value="Marketplace">Marketplace</option>
                                    <option value="Business">Business</option>
                                    <option value="Technologie">Technologie</option>
                                    <option value="Marketing">Marketing</option>
                                    <option value="Conseils">Conseils</option>
                                    <option value="Actualités">Actualités</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Tags</label>
                                <input type="text" class="form-control" name="tags"
                                       placeholder="tag1, tag2, tag3...">
                                <small class="text-muted">Séparez les tags par des virgules</small>
                            </div>
                        </div>

                        <!-- Publishing Options -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_published" id="is_published">
                                    <label class="form-check-label" for="is_published">
                                        <strong>Publier immédiatement</strong>
                                    </label>
                                    <div class="form-text">Si décoché, l'article sera sauvé comme brouillon</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_featured" id="is_featured">
                                    <label class="form-check-label" for="is_featured">
                                        <strong>Article en vedette</strong>
                                    </label>
                                    <div class="form-text">L'article apparaîtra en première position</div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2 justify-content-end">
                            <a href="{{ url_for('admin.manage_blog') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>Créer l'Article
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Publishing Info -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Informations de Publication
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Auteur:</strong> {{ current_user.get_full_name() }}
                    </div>
                    <div class="mb-3">
                        <strong>Date de création:</strong> Maintenant
                    </div>
                    <div class="mb-3">
                        <strong>Statut:</strong>
                        <span id="status-indicator" class="badge bg-secondary">Brouillon</span>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Conseil:</strong> Vous pouvez sauvegarder comme brouillon et publier plus tard.
                    </div>
                </div>
            </div>

            <!-- SEO Tips -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-search me-2"></i>Conseils SEO
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Utilisez un titre accrocheur et descriptif
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Rédigez un extrait engageant
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Ajoutez des tags pertinents
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Structurez votre contenu avec des sous-titres
                        </li>
                        <li>
                            <i class="fas fa-check text-success me-2"></i>
                            Visez 300+ mots pour un bon référencement
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Content Guidelines -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-book me-2"></i>Guide de Contenu
                    </h6>
                </div>
                <div class="card-body">
                    <h6>Formatage HTML de base:</h6>
                    <code>&lt;h2&gt;Sous-titre&lt;/h2&gt;</code><br>
                    <code>&lt;p&gt;Paragraphe&lt;/p&gt;</code><br>
                    <code>&lt;strong&gt;Gras&lt;/strong&gt;</code><br>
                    <code>&lt;em&gt;Italique&lt;/em&gt;</code><br>
                    <code>&lt;a href="url"&gt;Lien&lt;/a&gt;</code><br>

                    <hr>

                    <h6>Bonnes pratiques:</h6>
                    <ul class="small">
                        <li>Écrivez pour votre audience africaine</li>
                        <li>Utilisez un langage simple et clair</li>
                        <li>Incluez des exemples concrets</li>
                        <li>Relisez avant de publier</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- AI Generation Modal -->
<div class="modal fade" id="aiGenerateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-robot me-2"></i>Génération d'Article avec IA
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Comment ça marche:</strong> Décrivez le sujet de votre article et l'IA générera automatiquement le titre, le contenu et l'extrait.
                </div>

                <div class="mb-3">
                    <label class="form-label">Sujet de l'article</label>
                    <textarea class="form-control" id="aiPromptModal" rows="4"
                              placeholder="Exemple: Écris un article sur les meilleures stratégies de marketing digital pour les PME africaines. Inclus des conseils pratiques et des exemples concrets."></textarea>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Ton de l'article</label>
                        <select class="form-select" id="aiTone">
                            <option value="professionnel">Professionnel</option>
                            <option value="conversationnel">Conversationnel</option>
                            <option value="éducatif">Éducatif</option>
                            <option value="inspirant">Inspirant</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Longueur approximative</label>
                        <select class="form-select" id="aiLength">
                            <option value="court">Court (300-500 mots)</option>
                            <option value="moyen" selected>Moyen (500-800 mots)</option>
                            <option value="long">Long (800-1200 mots)</option>
                        </select>
                    </div>
                </div>

                <div class="mt-3">
                    <h6>Exemples de prompts efficaces:</h6>
                    <div class="list-group">
                        <button type="button" class="list-group-item list-group-item-action" onclick="setPrompt('Guide complet pour créer une boutique en ligne réussie en Afrique')">
                            Guide complet pour créer une boutique en ligne réussie en Afrique
                        </button>
                        <button type="button" class="list-group-item list-group-item-action" onclick="setPrompt('Les défis et opportunités du e-commerce en Afrique francophone')">
                            Les défis et opportunités du e-commerce en Afrique francophone
                        </button>
                        <button type="button" class="list-group-item list-group-item-action" onclick="setPrompt('Comment utiliser les réseaux sociaux pour promouvoir son business en Afrique')">
                            Comment utiliser les réseaux sociaux pour promouvoir son business en Afrique
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="enableAIMode()">
                    <i class="fas fa-magic me-2"></i>Activer le Mode IA
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// AI Mode functionality
function setPrompt(prompt) {
    document.getElementById('aiPromptModal').value = prompt;
}

function enableAIMode() {
    const prompt = document.getElementById('aiPromptModal').value.trim();
    const tone = document.getElementById('aiTone').value;
    const length = document.getElementById('aiLength').value;

    if (!prompt) {
        alert('Veuillez entrer un sujet pour l\'article.');
        return;
    }

    // Build enhanced prompt
    const enhancedPrompt = `${prompt}. Ton: ${tone}, Longueur: ${length}. Écris en français pour une audience africaine.`;

    // Show AI fields and populate
    document.getElementById('aiFields').style.display = 'block';
    document.querySelector('textarea[name="ai_prompt"]').value = enhancedPrompt;

    // Update form labels and help text
    document.getElementById('titleField').required = false;
    document.getElementById('titleHelp').style.display = 'block';
    document.getElementById('contentField').required = false;
    document.getElementById('contentHelp').textContent = 'Le contenu sera généré automatiquement par l\'IA';

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('aiGenerateModal'));
    modal.hide();

    // Show success message
    const alert = document.createElement('div');
    alert.className = 'alert alert-success alert-dismissible fade show';
    alert.innerHTML = `
        <i class="fas fa-robot me-2"></i>
        <strong>Mode IA activé!</strong> Soumettez le formulaire pour générer l'article.
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.card-body').insertBefore(alert, document.querySelector('#blogForm'));
}

// Update status indicator based on publish checkbox
document.getElementById('is_published').addEventListener('change', function() {
    const statusIndicator = document.getElementById('status-indicator');
    if (this.checked) {
        statusIndicator.textContent = 'Publié';
        statusIndicator.className = 'badge bg-success';
    } else {
        statusIndicator.textContent = 'Brouillon';
        statusIndicator.className = 'badge bg-secondary';
    }
});

// Auto-save functionality (optional)
let autoSaveTimer;
const titleInput = document.querySelector('input[name="title"]');
const contentTextarea = document.querySelector('textarea[name="content"]');

function autoSave() {
    // This could be implemented to save drafts automatically
    console.log('Auto-save triggered');
}

// Set up auto-save on content change
[titleInput, contentTextarea].forEach(element => {
    element.addEventListener('input', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(autoSave, 5000); // Auto-save after 5 seconds of inactivity
    });
});

// Character count for content
contentTextarea.addEventListener('input', function() {
    const wordCount = this.value.split(/\s+/).filter(word => word.length > 0).length;
    const readingTime = Math.ceil(wordCount / 200);

    // You could display this information somewhere
    console.log(`Mots: ${wordCount}, Temps de lecture: ${readingTime} min`);
});
</script>
{% endblock %}
