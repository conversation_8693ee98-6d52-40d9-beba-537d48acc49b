{% extends "base.html" %}

{% block title %}Statistiques Détaillées - Admin - Afroly.org{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-6 fw-bold">
                <i class="fas fa-chart-bar me-3 text-info"></i>Statistiques Détaillées
            </h1>
            <p class="lead text-muted">Analyse complète des performances de la plateforme</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group" role="group">
                <a href="{{ url_for('admin.statistics', days=7) }}"
                   class="btn btn-outline-primary {% if days == 7 %}active{% endif %}">7j</a>
                <a href="{{ url_for('admin.statistics', days=30) }}"
                   class="btn btn-outline-primary {% if days == 30 %}active{% endif %}">30j</a>
                <a href="{{ url_for('admin.statistics', days=90) }}"
                   class="btn btn-outline-primary {% if days == 90 %}active{% endif %}">90j</a>
            </div>
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-arrow-left me-2"></i>Dashboard
            </a>
        </div>
    </div>

    <!-- Platform Overview -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-globe me-2"></i>Vue d'Ensemble de la Plateforme
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <div class="border rounded p-3">
                                <h4 class="text-primary mb-1">{{ platform_stats.total_users }}</h4>
                                <small class="text-muted">Utilisateurs Totaux</small>
                                {% if growth_stats.users_growth != 0 %}
                                <br><small class="text-{{ 'success' if growth_stats.users_growth > 0 else 'danger' }}">
                                    <i class="fas fa-arrow-{{ 'up' if growth_stats.users_growth > 0 else 'down' }} me-1"></i>
                                    {{ growth_stats.users_growth }}%
                                </small>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <div class="border rounded p-3">
                                <h4 class="text-success mb-1">{{ platform_stats.total_vendors }}</h4>
                                <small class="text-muted">Vendeurs</small>
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <div class="border rounded p-3">
                                <h4 class="text-info mb-1">{{ platform_stats.total_shops }}</h4>
                                <small class="text-muted">Boutiques</small>
                                {% if growth_stats.shops_growth != 0 %}
                                <br><small class="text-{{ 'success' if growth_stats.shops_growth > 0 else 'danger' }}">
                                    <i class="fas fa-arrow-{{ 'up' if growth_stats.shops_growth > 0 else 'down' }} me-1"></i>
                                    {{ growth_stats.shops_growth }}%
                                </small>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <div class="border rounded p-3">
                                <h4 class="text-warning mb-1">{{ platform_stats.total_products }}</h4>
                                <small class="text-muted">Produits</small>
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <div class="border rounded p-3">
                                <h4 class="text-purple mb-1">{{ platform_stats.total_orders }}</h4>
                                <small class="text-muted">Commandes</small>
                                {% if growth_stats.orders_growth != 0 %}
                                <br><small class="text-{{ 'success' if growth_stats.orders_growth > 0 else 'danger' }}">
                                    <i class="fas fa-arrow-{{ 'up' if growth_stats.orders_growth > 0 else 'down' }} me-1"></i>
                                    {{ growth_stats.orders_growth }}%
                                </small>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <div class="border rounded p-3">
                                <h4 class="text-success mb-1">{{ "%.0f"|format(platform_stats.total_revenue) }}</h4>
                                <small class="text-muted">FCFA Revenus</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Commissions Totales</h6>
                            <h3 class="mb-0">{{ "%.0f"|format(platform_stats.total_commission) }} FCFA</h3>
                            <small>{{ "%.1f"|format((platform_stats.total_commission / platform_stats.total_revenue * 100) if platform_stats.total_revenue > 0 else 0) }}% des revenus</small>
                        </div>
                        <div>
                            <i class="fas fa-eye fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Boutiques Actives</h6>
                            <h3 class="mb-0">{{ platform_stats.active_shops }}</h3>
                            <small>{{ platform_stats.total_shops }} au total</small>
                        </div>
                        <div>
                            <i class="fas fa-store fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-dark h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Nouveaux Utilisateurs</h6>
                            <h3 class="mb-0">{{ platform_stats.total_vendors }}</h3>
                            <small>Vendeurs actifs</small>
                        </div>
                        <div>
                            <i class="fas fa-user-plus fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Commandes Totales</h6>
                            <h3 class="mb-0">{{ platform_stats.total_orders }}</h3>
                            <small>Revenus: {{ "%.0f"|format(platform_stats.total_revenue) }} FCFA</small>
                        </div>
                        <div>
                            <i class="fas fa-shopping-cart fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Évolution des Visiteurs (30 derniers jours)</h5>
                </div>
                <div class="card-body">
                    <canvas id="trafficChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Répartition des Activités</h5>
                </div>
                <div class="card-body">
                    <canvas id="activityChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Shops -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Top 10 Boutiques (30 derniers jours)</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Boutique</th>
                                    <th>Propriétaire</th>
                                    <th>Pays</th>
                                    <th>Commandes</th>
                                    <th>Revenus (FCFA)</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for shop_data in top_shops %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if shop_data.shop.logo %}
                                            <img src="/static/uploads/shops/{{ shop_data.shop.logo }}"
                                                 class="rounded-circle me-2" style="width: 32px; height: 32px; object-fit: cover;">
                                            {% else %}
                                            <div class="bg-primary rounded-circle me-2 d-flex align-items-center justify-content-center"
                                                 style="width: 32px; height: 32px;">
                                                <i class="fas fa-store text-white"></i>
                                            </div>
                                            {% endif %}
                                            <div>
                                                <strong>{{ shop_data.shop.name }}</strong>
                                                {% if shop_data.shop.owner.tier.value == 'premium' %}
                                                <span class="badge bg-info ms-1">Premium</span>
                                                {% elif shop_data.shop.owner.tier.value == 'gold' %}
                                                <span class="badge bg-warning ms-1">Gold</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ shop_data.shop.owner.first_name }} {{ shop_data.shop.owner.last_name }}</td>
                                    <td>{{ shop_data.shop.country }}</td>
                                    <td>{{ shop_data.order_count }}</td>
                                    <td>{{ "%.0f"|format(shop_data.total_revenue) }} FCFA</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Growth Statistics -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Croissance (30 derniers jours)</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4 mb-3">
                            <h4 class="text-primary">{{ growth_stats.users_growth }}%</h4>
                            <small class="text-muted">Utilisateurs</small>
                        </div>
                        <div class="col-4 mb-3">
                            <h4 class="text-success">{{ growth_stats.shops_growth }}%</h4>
                            <small class="text-muted">Boutiques</small>
                        </div>
                        <div class="col-4 mb-3">
                            <h4 class="text-warning">{{ growth_stats.orders_growth }}%</h4>
                            <small class="text-muted">Commandes</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Résumé Financier</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h4 class="text-success">{{ "%.0f"|format(platform_stats.total_revenue) }}</h4>
                            <small class="text-muted">Revenus Totaux (FCFA)</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-info">{{ "%.0f"|format(platform_stats.total_commission) }}</h4>
                            <small class="text-muted">Commissions (FCFA)</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}
