{% extends "base.html" %}

{% block title %}Modifier Produit - Admin - Afroly.org{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-6 fw-bold">
                <i class="fas fa-edit me-3 text-primary"></i>Modifier le Produit
            </h1>
            <p class="lead text-muted">Modifier "{{ product.name }}" de {{ product.shop.name }}</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('admin.manage_products') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Retour aux Produits
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-box me-2"></i>Informations du Produit
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <!-- Basic Information -->
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label class="form-label">Nom du Produit <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="name" value="{{ product.name }}" required>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">SKU</label>
                                <input type="text" class="form-control" name="sku" value="{{ product.sku or '' }}">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Description Courte</label>
                            <textarea class="form-control" name="short_description" rows="2">{{ product.short_description or '' }}</textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Description Complète <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="description" rows="6" required>{{ product.description }}</textarea>
                        </div>

                        <!-- Pricing -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label class="form-label">Prix (FCFA) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" name="price" value="{{ product.price }}" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Prix de Vente (FCFA)</label>
                                <input type="number" class="form-control" name="sale_price" value="{{ product.sale_price or '' }}" step="0.01" min="0">
                                <small class="text-muted">Laissez vide si pas de promotion</small>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Poids (kg)</label>
                                <input type="number" class="form-control" name="weight" value="{{ product.weight or '' }}" step="0.01" min="0">
                            </div>
                        </div>

                        <!-- Category and Stock -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Catégorie <span class="text-danger">*</span></label>
                                <select class="form-select" name="category_id" required>
                                    <option value="">Sélectionner une catégorie</option>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}" {% if category.id == product.category_id %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Stock <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" name="stock_quantity" value="{{ product.stock_quantity }}" min="0" required>
                            </div>
                        </div>

                        <!-- Product Type and Status -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="digital" id="digital" {% if product.digital %}checked{% endif %}>
                                    <label class="form-check-label" for="digital">
                                        Produit Numérique
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Statut <span class="text-danger">*</span></label>
                                <select class="form-select" name="status" required>
                                    <option value="pending" {% if product.status.value == 'pending' %}selected{% endif %}>En Attente</option>
                                    <option value="active" {% if product.status.value == 'active' %}selected{% endif %}>Actif</option>
                                    <option value="inactive" {% if product.status.value == 'inactive' %}selected{% endif %}>Inactif</option>
                                    <option value="rejected" {% if product.status.value == 'rejected' %}selected{% endif %}>Rejeté</option>
                                </select>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2 justify-content-end">
                            <a href="{{ url_for('admin.manage_products') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Sauvegarder les Modifications
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Product Info Sidebar -->
        <div class="col-lg-4">
            <!-- Current Images -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-images me-2"></i>Images Actuelles
                    </h6>
                </div>
                <div class="card-body">
                    {% if product.images %}
                    <div class="row">
                        {% for image in product.images %}
                        <div class="col-6 mb-3">
                            <img src="{{ image }}" alt="Product Image" class="img-fluid rounded">
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted text-center">Aucune image</p>
                    {% endif %}
                </div>
            </div>

            <!-- Product Stats -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Statistiques
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h5 class="text-primary">{{ product.reviews|length }}</h5>
                            <small class="text-muted">Avis</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h5 class="text-success">
                                {% if product.reviews %}
                                {{ "%.1f"|format(product.reviews|map(attribute='rating')|sum / product.reviews|length) }}
                                {% else %}
                                0.0
                                {% endif %}
                            </h5>
                            <small class="text-muted">Note Moyenne</small>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="mb-2">
                        <strong>Boutique:</strong> {{ product.shop.name }}
                    </div>
                    <div class="mb-2">
                        <strong>Propriétaire:</strong> {{ product.shop.owner.get_full_name() }}
                    </div>
                    <div class="mb-2">
                        <strong>Créé le:</strong> {{ product.created_at.strftime('%d/%m/%Y') }}
                    </div>
                    <div class="mb-2">
                        <strong>Modifié le:</strong> {{ product.updated_at.strftime('%d/%m/%Y') if product.updated_at else 'Jamais' }}
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Actions Rapides
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('product.view', slug=product.slug) }}" 
                           class="btn btn-outline-info" target="_blank">
                            <i class="fas fa-eye me-2"></i>Voir le Produit
                        </a>
                        
                        {% if product.status.value == 'pending' %}
                        <form method="POST" action="{{ url_for('admin.approve_product', product_id=product.id) }}">
                            <button type="submit" class="btn btn-success w-100"
                                    onclick="return confirm('Approuver ce produit ?')">
                                <i class="fas fa-check me-2"></i>Approuver
                            </button>
                        </form>
                        {% elif product.status.value == 'active' %}
                        <form method="POST" action="{{ url_for('admin.suspend_product', product_id=product.id) }}">
                            <button type="submit" class="btn btn-warning w-100"
                                    onclick="return confirm('Suspendre ce produit ?')">
                                <i class="fas fa-pause me-2"></i>Suspendre
                            </button>
                        </form>
                        {% endif %}
                        
                        <button class="btn btn-outline-danger" 
                                onclick="deleteProduct({{ product.id }}, '{{ product.name }}')">
                            <i class="fas fa-trash me-2"></i>Supprimer
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteProductModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>Supprimer le Produit
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer ce produit ?</p>
                <p class="text-danger"><strong>Cette action est irréversible.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Supprimer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentProductId = null;

function deleteProduct(productId, productName) {
    currentProductId = productId;
    const modal = new bootstrap.Modal(document.getElementById('deleteProductModal'));
    modal.show();
}

document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
    if (currentProductId) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/products/${currentProductId}/delete`;
        document.body.appendChild(form);
        form.submit();
    }
});
</script>
{% endblock %}
