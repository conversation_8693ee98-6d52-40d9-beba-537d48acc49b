{% extends "base.html" %}

{% block title %}Gestion des Crédits - Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-coins me-2"></i>Gestion des Crédits
                </h1>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card bg-warning text-dark">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Transactions en Attente</h5>
                                    <h2 class="mb-0">{{ pending_count }}</h2>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Crédits en Attente</h5>
                                    <h2 class="mb-0">{{ total_pending_amount }}</h2>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-coins fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">Statut</label>
                            <select name="status" id="status" class="form-select">
                                <option value="all" {% if status_filter == 'all' %}selected{% endif %}>Tous</option>
                                <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>En Attente</option>
                                <option value="approved" {% if status_filter == 'approved' %}selected{% endif %}>Approuvées</option>
                                <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>Rejetées</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="search" class="form-label">Rechercher</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Email, nom, description..." value="{{ search }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>Filtrer
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Transactions Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Transactions de Crédits</h5>
                </div>
                <div class="card-body p-0">
                    {% if transactions.items %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Utilisateur</th>
                                        <th>Montant</th>
                                        <th>Type</th>
                                        <th>Description</th>
                                        <th>Statut</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for transaction in transactions.items %}
                                    <tr>
                                        <td><strong>#{{ transaction.id }}</strong></td>
                                        <td>
                                            <div>
                                                <strong>{{ transaction.user.get_full_name() }}</strong><br>
                                                <small class="text-muted">{{ transaction.user.email }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-{% if transaction.amount > 0 %}success{% else %}danger{% endif %} fs-6">
                                                {% if transaction.amount > 0 %}+{% endif %}{{ transaction.amount }} crédits
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ transaction.transaction_type.value }}</span>
                                        </td>
                                        <td>
                                            <small>{{ transaction.description }}</small>
                                            {% if transaction.payment_method %}
                                                <br><small class="text-muted">{{ transaction.payment_method }}</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if transaction.payment_status == 'pending' %}
                                                <span class="badge bg-warning">En Attente</span>
                                            {% elif transaction.payment_status == 'completed' %}
                                                <span class="badge bg-success">Approuvée</span>
                                            {% elif transaction.payment_status == 'rejected' %}
                                                <span class="badge bg-danger">Rejetée</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ transaction.payment_status }}</span>
                                            {% endif %}
                                            
                                            {% if transaction.approved_by %}
                                                <br><small class="text-muted">
                                                    Par: {{ transaction.approver.get_full_name() if transaction.approver else 'Admin' }}
                                                </small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <small>{{ transaction.created_at.strftime('%d/%m/%Y %H:%M') }}</small>
                                            {% if transaction.approved_at %}
                                                <br><small class="text-muted">
                                                    Traité: {{ transaction.approved_at.strftime('%d/%m/%Y %H:%M') }}
                                                </small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if transaction.payment_status == 'pending' and not transaction.is_approved %}
                                                <div class="btn-group-vertical btn-group-sm">
                                                    <button type="button" class="btn btn-success btn-sm" 
                                                            onclick="approveTransaction({{ transaction.id }})">
                                                        <i class="fas fa-check"></i> Approuver
                                                    </button>
                                                    <button type="button" class="btn btn-danger btn-sm" 
                                                            onclick="rejectTransaction({{ transaction.id }})">
                                                        <i class="fas fa-times"></i> Rejeter
                                                    </button>
                                                </div>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% if transaction.admin_notes %}
                                    <tr class="table-light">
                                        <td colspan="8">
                                            <small><strong>Notes admin:</strong> {{ transaction.admin_notes }}</small>
                                        </td>
                                    </tr>
                                    {% endif %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-coins fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucune transaction trouvée</h5>
                            <p class="text-muted">Aucune transaction de crédit ne correspond à vos critères.</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Pagination -->
            {% if transactions.pages > 1 %}
            <nav aria-label="Pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if transactions.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.manage_credits', page=transactions.prev_num, status=status_filter, search=search) }}">Précédent</a>
                        </li>
                    {% endif %}
                    
                    {% for page_num in transactions.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != transactions.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.manage_credits', page=page_num, status=status_filter, search=search) }}">{{ page_num }}</a>
                                </li>
                            {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                            {% endif %}
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if transactions.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.manage_credits', page=transactions.next_num, status=status_filter, search=search) }}">Suivant</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approvalModalTitle">Approuver la Transaction</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="approvalForm" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="admin_notes" class="form-label">Notes administratives (optionnel)</label>
                        <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3" 
                                  placeholder="Ajoutez des notes sur cette approbation..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-success" id="approvalSubmitBtn">
                        <i class="fas fa-check"></i> Approuver
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div class="modal fade" id="rejectionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Rejeter la Transaction</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="rejectionForm" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="reject_admin_notes" class="form-label">Raison du rejet <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="reject_admin_notes" name="admin_notes" rows="3" 
                                  placeholder="Expliquez pourquoi cette transaction est rejetée..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times"></i> Rejeter
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function approveTransaction(transactionId) {
    document.getElementById('approvalForm').action = `/admin/credits/${transactionId}/approve`;
    document.getElementById('admin_notes').value = '';
    new bootstrap.Modal(document.getElementById('approvalModal')).show();
}

function rejectTransaction(transactionId) {
    document.getElementById('rejectionForm').action = `/admin/credits/${transactionId}/reject`;
    document.getElementById('reject_admin_notes').value = '';
    new bootstrap.Modal(document.getElementById('rejectionModal')).show();
}

// Auto-hide flash messages
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert:not(.alert-dismissible)');
    alerts.forEach(function(alert) {
        if (alert.classList.contains('alert-success') || alert.classList.contains('alert-info')) {
            setTimeout(function() {
                alert.style.transition = 'opacity 0.5s ease-out';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            }, 4000); // Hide after 4 seconds
        }
    });

    // Auto-hide dismissible alerts after longer time
    const dismissibleAlerts = document.querySelectorAll('.alert-dismissible');
    dismissibleAlerts.forEach(function(alert) {
        if (alert.classList.contains('alert-success') || alert.classList.contains('alert-info')) {
            setTimeout(function() {
                const closeBtn = alert.querySelector('.btn-close');
                if (closeBtn && alert.style.display !== 'none') {
                    closeBtn.click();
                }
            }, 6000); // Hide after 6 seconds
        }
    });
});
</script>
{% endblock %}
