{% extends "base.html" %}

{% block title %}Changer le Mo<PERSON> de Passe - AfroMall{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-lock me-2"></i>Changer le Mot de Passe</h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="current_password" class="form-label">Mot de Passe Actuel</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required 
                                   placeholder="Entrez votre mot de passe actuel">
                        </div>
                        
                        <div class="mb-3">
                            <label for="new_password" class="form-label">Nouveau Mot de Passe</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required 
                                   placeholder="Entrez votre nouveau mot de passe">
                            <div class="form-text">
                                Le mot de passe doit contenir au moins 8 caractères avec des lettres et des chiffres.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirmer le Nouveau Mot de Passe</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required 
                                   placeholder="Confirmez votre nouveau mot de passe">
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Changer le Mot de Passe
                            </button>
                            <a href="{{ url_for('auth.profile') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (newPassword !== confirmPassword) {
        this.setCustomValidity('Les mots de passe ne correspondent pas');
    } else {
        this.setCustomValidity('');
    }
});
</script>
{% endblock %}
