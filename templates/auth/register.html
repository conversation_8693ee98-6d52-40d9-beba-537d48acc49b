{% extends "base.html" %}

{% block title %}Inscription - AfroMall{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-lg border-0">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <h2 class="fw-bold text-primary">Rejoindre AfroMall</h2>
                        <p class="text-muted">Créez votre compte et commencez à acheter ou vendre</p>
                    </div>

                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">Prénom</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required
                                       placeholder="Entrez votre prénom" value="{{ request.form.get('first_name', '') }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Nom</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required
                                       placeholder="Entrez votre nom" value="{{ request.form.get('last_name', '') }}">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Adresse Email</label>
                            <input type="email" class="form-control" id="email" name="email" required
                                   placeholder="Entrez votre email" value="{{ request.form.get('email', '') }}">
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">Numéro de Téléphone (Optionnel)</label>
                            <input type="tel" class="form-control" id="phone" name="phone"
                                   placeholder="Entrez votre numéro de téléphone" value="{{ request.form.get('phone', '') }}">
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Mot de Passe</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" required
                                       placeholder="Créez un mot de passe fort">
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                Le mot de passe doit contenir au moins 8 caractères avec des lettres et des chiffres.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirmer le Mot de Passe</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required
                                   placeholder="Confirmez votre mot de passe">
                        </div>

                        <div class="mb-4">
                            <label class="form-label">Type de Compte</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-2 account-type-card" data-type="shopper">
                                        <div class="card-body text-center">
                                            <i class="fas fa-shopping-bag fa-2x text-primary mb-2"></i>
                                            <h6>Acheteur</h6>
                                            <p class="small text-muted">Parcourir et acheter des produits</p>
                                            <input type="radio" name="user_type" value="shopper" checked class="d-none">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-2 account-type-card" data-type="vendor">
                                        <div class="card-body text-center">
                                            <i class="fas fa-store fa-2x text-success mb-2"></i>
                                            <h6>Vendeur</h6>
                                            <p class="small text-muted">Vendre vos produits</p>
                                            <input type="radio" name="user_type" value="vendor" class="d-none">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" required>
                            <label class="form-check-label" for="terms">
                                J'accepte les <a href="{{ url_for('main.terms') }}" target="_blank">Conditions d'Utilisation</a>
                                et la <a href="{{ url_for('main.privacy') }}" target="_blank">Politique de Confidentialité</a>
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-user-plus me-2"></i>Créer un Compte
                        </button>
                    </form>

                    <hr class="my-4">

                    <div class="text-center">
                        <p class="mb-0">Vous avez déjà un compte ?
                            <a href="{{ url_for('auth.login') }}" class="text-decoration-none fw-bold">Connectez-vous ici</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
.account-type-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border-color: #dee2e6 !important;
}

.account-type-card:hover {
    border-color: #007bff !important;
    transform: translateY(-2px);
}

.account-type-card.selected {
    border-color: #007bff !important;
    background-color: #f8f9ff;
}
</style>
{% endblock %}

{% block extra_scripts %}
<script>
// Password toggle
document.getElementById('togglePassword').addEventListener('click', function() {
    const passwordInput = document.getElementById('password');
    const icon = this.querySelector('i');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});

// Account type selection
document.querySelectorAll('.account-type-card').forEach(card => {
    card.addEventListener('click', function() {
        // Remove selected class from all cards
        document.querySelectorAll('.account-type-card').forEach(c => c.classList.remove('selected'));

        // Add selected class to clicked card
        this.classList.add('selected');

        // Check the radio button
        this.querySelector('input[type="radio"]').checked = true;
    });
});

// Set initial selection
document.querySelector('.account-type-card[data-type="shopper"]').classList.add('selected');

// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;

    if (password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});
</script>
{% endblock %}
