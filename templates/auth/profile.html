{% extends "base.html" %}

{% block title %}Mon Profil - AfroMall{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-user-circle fa-5x text-primary"></i>
                    </div>
                    <h5>{{ current_user.get_full_name() }}</h5>
                    <p class="text-muted">{{ current_user.email }}</p>
                    <span class="badge bg-primary">{{ current_user.role.value.title() }}</span>
                    {% if current_user.role.value == 'vendor' %}
                        <span class="badge bg-success">{{ current_user.tier.value.title() }}</span>
                    {% endif %}
                </div>
            </div>

            <div class="list-group mt-3">
                <a href="{{ url_for('auth.profile') }}" class="list-group-item list-group-item-action active">
                    <i class="fas fa-user me-2"></i>Informations Personnelles
                </a>
                <a href="{{ url_for('auth.edit_profile') }}" class="list-group-item list-group-item-action">
                    <i class="fas fa-edit me-2"></i>Modifier le Profil
                </a>
                <a href="{{ url_for('auth.change_password') }}" class="list-group-item list-group-item-action">
                    <i class="fas fa-lock me-2"></i>Changer le Mot de Passe
                </a>
                {% if current_user.role.value == 'vendor' %}
                <a href="{{ url_for('shop.vendor_dashboard') }}" class="list-group-item list-group-item-action">
                    <i class="fas fa-store me-2"></i>Tableau de Bord Vendeur
                </a>
                <a href="{{ url_for('shop.payment_methods') }}" class="list-group-item list-group-item-action">
                    <i class="fas fa-credit-card me-2"></i>Méthodes de Paiement
                </a>
                {% endif %}
                {% if current_user.role.value == 'shopper' %}
                <a href="{{ url_for('cart.my_carts') }}" class="list-group-item list-group-item-action">
                    <i class="fas fa-shopping-cart me-2"></i>Mes Paniers
                </a>
                {% endif %}
            </div>
        </div>

        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-user me-2"></i>Informations Personnelles</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Prénom</label>
                                <p class="form-control-plaintext">{{ current_user.first_name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Nom</label>
                                <p class="form-control-plaintext">{{ current_user.last_name }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Email</label>
                                <p class="form-control-plaintext">{{ current_user.email }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Téléphone</label>
                                <p class="form-control-plaintext">{{ current_user.phone or 'Non renseigné' }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Type de Compte</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-primary">{{ current_user.role.value.title() }}</span>
                                </p>
                            </div>
                        </div>
                        {% if current_user.role.value == 'vendor' %}
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Niveau</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-success">{{ current_user.tier.value.title() }}</span>
                                </p>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Membre depuis</label>
                                <p class="form-control-plaintext">{{ current_user.created_at.strftime('%d/%m/%Y') }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Statut</label>
                                <p class="form-control-plaintext">
                                    {% if current_user.is_active %}
                                        <span class="badge bg-success">Actif</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactif</span>
                                    {% endif %}
                                    {% if current_user.email_verified %}
                                        <span class="badge bg-info">Email Vérifié</span>
                                    {% else %}
                                        <span class="badge bg-warning">Email Non Vérifié</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <a href="{{ url_for('auth.edit_profile') }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>Modifier le Profil
                        </a>
                        <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-lock me-2"></i>Changer le Mot de Passe
                        </a>
                    </div>
                </div>
            </div>

            {% if current_user.role.value == 'vendor' %}
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-store me-2"></i>Statistiques Vendeur</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-primary">{{ current_user.shops|length }}</h4>
                                <small class="text-muted">Boutiques</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-success">0</h4>
                                <small class="text-muted">Produits Actifs</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-info">0</h4>
                                <small class="text-muted">Commandes</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-warning">0 €</h4>
                                <small class="text-muted">Revenus</small>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-3">
                        <a href="{{ url_for('shop.vendor_dashboard') }}" class="btn btn-primary">
                            <i class="fas fa-chart-bar me-2"></i>Voir le Tableau de Bord
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
