{% extends "base.html" %}

{% block title %}Afroly.org - Votre Destination Shopping Africaine de Premier Plan{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">Bienvenue sur Afroly.org</h1>
                <p class="lead mb-4">Découvrez des produits extraordinaires de vendeurs africains. Achetez local, soutenez les entreprises africaines, et trouvez des articles uniques que vous ne trouverez nulle part ailleurs.</p>
                <div class="d-flex gap-3" style="position: relative; z-index: 20;">
                    <a href="{{ url_for('shop.browse') }}" class="btn btn-light btn-lg" style="position: relative; z-index: 21;">Parcourir les Boutiques</a>
                    <a href="{{ url_for('auth.register') }}" class="btn btn-outline-light btn-lg" style="position: relative; z-index: 21;">Devenir Vendeur</a>
                </div>
            </div>
            <div class="col-lg-6">
                <img src="{{ url_for('static', filename='images/young-black-african-businesswoman-local-market-browsing-online-using-smartphone-checking-reading-news-online_505521-693 (1).avif') }}"
                     alt="Jeune Femme d'Affaires Africaine Utilisant son Smartphone pour les Affaires en Ligne"
                     class="img-fluid rounded shadow-lg"
                     loading="lazy"
                     width="600"
                     height="400">
            </div>
        </div>
    </div>
</section>

<!-- Payment Methods & Delivery Section -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-3 col-md-12 text-center mb-3 mb-lg-0">
                <h6 class="text-primary mb-2">
                    <i class="fas fa-shield-alt me-2"></i>Paiements Sécurisés
                </h6>
                <small class="text-muted">Méthodes de paiement de confiance</small>
            </div>
            <div class="col-lg-9 col-md-12">
                <div class="d-flex flex-wrap justify-content-center justify-content-lg-end align-items-center gap-2">
                    <!-- Mobile Money -->
                    <div class="payment-method-badge">
                        <i class="fas fa-mobile-alt text-success"></i>
                        <span class="ms-1">Orange Money</span>
                    </div>
                    <div class="payment-method-badge">
                        <i class="fas fa-mobile-alt text-warning"></i>
                        <span class="ms-1">MTN MoMo</span>
                    </div>
                    <div class="payment-method-badge">
                        <i class="fas fa-mobile-alt text-info"></i>
                        <span class="ms-1">Wave</span>
                    </div>
                    <div class="payment-method-badge">
                        <i class="fas fa-mobile-alt text-primary"></i>
                        <span class="ms-1">Airtel Money</span>
                    </div>
                    <!-- International -->
                    <div class="payment-method-badge">
                        <i class="fab fa-paypal text-primary"></i>
                        <span class="ms-1">PayPal</span>
                    </div>
                    <div class="payment-method-badge">
                        <i class="fas fa-university text-secondary"></i>
                        <span class="ms-1">Virement</span>
                    </div>
                    <div class="payment-method-badge">
                        <i class="fab fa-stripe text-primary"></i>
                        <span class="ms-1">Stripe</span>
                    </div>
                    <div class="payment-method-badge">
                        <i class="fas fa-money-bill-wave text-success"></i>
                        <span class="ms-1">Cash</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Launch Your Store Section -->
<section class="py-5 bg-gradient-success launch-store-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card border-0 shadow-lg">
                    <div class="card-body p-5 text-center">
                        <h2 class="display-5 fw-bold text-primary mb-4">
                            🚀 Lance ta boutique en ligne et vends partout !
                        </h2>
                        <div class="row justify-content-center mb-4">
                            <div class="col-lg-8">
                                <p class="lead text-dark mb-4 fs-4">
                                    Avec notre plateforme, tu peux créer ta boutique <strong class="text-primary">100% gratuite</strong> et commencer à vendre <strong class="text-success">en quelques minutes</strong>.
                                </p>
                            </div>
                        </div>

                        <div class="row justify-content-center mb-4">
                            <div class="col-lg-10">
                                <div class="d-flex flex-column align-items-center justify-content-center">
                                    <div class="text-center">
                                        <div class="mb-3">
                                            <h5 class="text-primary mb-2">📱 Vends facilement sur :</h5>
                                        </div>
                                        <div class="d-flex flex-wrap justify-content-center align-items-center gap-4 mb-3">
                                            <div class="social-platform-item">
                                                <div class="social-logo-container facebook">
                                                    <i class="fab fa-facebook-f"></i>
                                                </div>
                                                <small class="d-block mt-1 fw-bold">Facebook</small>
                                            </div>
                                            <div class="social-platform-item">
                                                <div class="social-logo-container instagram">
                                                    <i class="fab fa-instagram"></i>
                                                </div>
                                                <small class="d-block mt-1 fw-bold">Instagram</small>
                                            </div>
                                            <div class="social-platform-item">
                                                <div class="social-logo-container whatsapp">
                                                    <i class="fab fa-whatsapp"></i>
                                                </div>
                                                <small class="d-block mt-1 fw-bold">WhatsApp</small>
                                            </div>
                                            <div class="social-platform-item">
                                                <div class="social-logo-container tiktok">
                                                    <i class="fab fa-tiktok"></i>
                                                </div>
                                                <small class="d-block mt-1 fw-bold">TikTok</small>
                                            </div>
                                            <div class="social-platform-item">
                                                <div class="social-logo-container telegram">
                                                    <i class="fab fa-telegram-plane"></i>
                                                </div>
                                                <small class="d-block mt-1 fw-bold">Telegram</small>
                                            </div>
                                        </div>
                                        <p class="text-muted mb-0"><em>et même par message privé</em></p>
                                    </div>
                                </div>
                                <div class="text-center mt-3">
                                    <p class="fs-5 text-dark mb-0">
                                        <strong>Plus besoin de</strong> dépenser des millions pour lancer ton business.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="row text-start mb-4">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-3">
                                    <span class="badge bg-success rounded-pill me-3 fs-6">✅</span>
                                    <span class="fs-5">Crée ta boutique en quelques clics</span>
                                </div>
                                <div class="d-flex align-items-center mb-3">
                                    <span class="badge bg-success rounded-pill me-3 fs-6">✅</span>
                                    <span class="fs-5">Ajoute tes produits, ton prix, tes images</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-3">
                                    <span class="badge bg-success rounded-pill me-3 fs-6">✅</span>
                                    <span class="fs-5">Partage ton lien sur tous tes réseaux</span>
                                </div>
                                <div class="d-flex align-items-center mb-3">
                                    <span class="badge bg-success rounded-pill me-3 fs-6">✅</span>
                                    <span class="fs-5">Reçois des commandes directement</span>
                                </div>
                            </div>
                        </div>

                        <div class="permanent-highlight-box border-0 mb-4 p-4 rounded-3">
                            <h5 class="mb-2">💼 Que tu vendes des vêtements, des cosmétiques, des ebooks, des services…</h5>
                            <p class="mb-2 fs-5"><strong>TA boutique est prête.</strong></p>
                            <p class="mb-0 fs-5">Et devine quoi ? <strong class="text-success">Pas de commission sur tes ventes.</strong></p>
                        </div>

                        <div class="d-grid gap-3 d-md-flex justify-content-md-center">
                            <a href="{{ url_for('auth.register') }}" class="btn btn-primary btn-lg px-5 py-3">
                                <i class="fas fa-rocket me-2"></i>Créer Ma Boutique Maintenant
                            </a>
                            <a href="{{ url_for('shop.browse') }}" class="btn btn-outline-primary btn-lg px-5 py-3">
                                <i class="fas fa-eye me-2"></i>Voir des Exemples
                            </a>
                        </div>

                        <p class="mt-4 text-muted">
                            <strong>👉 Prêt(e) à vendre comme un pro ?</strong> Rejoins des milliers d'entrepreneurs qui ont déjà lancé leur business !
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Products Section -->
{% if featured_products %}
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">Produits en Vedette</h2>
        <div class="row">
            {% for product in featured_products %}
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card h-100 product-card">
                    <div class="position-relative">
                        <img src="{{ product.get_main_image() }}" class="card-img-top" alt="{{ product.name }}" style="height: 200px; object-fit: cover;">
                        {% if product.is_on_sale() %}
                        <span class="badge bg-danger position-absolute top-0 end-0 m-2">
                            -{{ product.get_discount_percentage() }}%
                        </span>
                        {% endif %}
                    </div>
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title">{{ product.name }}</h6>
                        <p class="card-text text-muted small">{{ product.short_description[:100] }}...</p>
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    {% if product.is_on_sale() %}
                                    <span class="text-decoration-line-through text-muted">{{ "%.0f"|format(product.price) }} {{ product.shop.get_currency_symbol() }}</span>
                                    <span class="fw-bold text-primary">{{ "%.0f"|format(product.sale_price) }} {{ product.shop.get_currency_symbol() }}</span>
                                    {% else %}
                                    <span class="fw-bold text-primary">{{ "%.0f"|format(product.price) }} {{ product.shop.get_currency_symbol() }}</span>
                                    {% endif %}
                                </div>
                                <div class="rating">
                                    {% for i in range(5) %}
                                        {% if i < product.get_average_rating() %}
                                        <i class="fas fa-star text-warning"></i>
                                        {% else %}
                                        <i class="far fa-star text-warning"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                            <a href="{{ url_for('product.view', slug=product.slug) }}" class="btn btn-primary btn-sm mt-2 w-100">Voir Produit</a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Featured Shops Section -->
{% if featured_shops %}
<section class="py-5">
    <div class="container">
        <h2 class="text-center mb-5">Boutiques en Vedette</h2>
        <div class="row">
            {% for shop in featured_shops %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 shop-card">
                    {% if shop.banner %}
                    <img src="/static/uploads/shops/{{ shop.banner }}" class="card-img-top" alt="{{ shop.name }}" style="height: 150px; object-fit: cover;">
                    {% endif %}
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            {% if shop.logo %}
                            <img src="/static/uploads/shops/{{ shop.logo }}" alt="{{ shop.name }}" class="rounded-circle me-3" style="width: 50px; height: 50px; object-fit: cover;">
                            {% else %}
                            <div class="bg-primary rounded-circle me-3 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                <i class="fas fa-store text-white"></i>
                            </div>
                            {% endif %}
                            <div>
                                <h5 class="card-title mb-0">{{ shop.name }}</h5>
                                <div class="rating">
                                    {% for i in range(5) %}
                                        {% if i < shop.get_average_rating() %}
                                        <i class="fas fa-star text-warning"></i>
                                        {% else %}
                                        <i class="far fa-star text-warning"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        <p class="card-text">{{ shop.description[:100] }}...</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">{{ shop.get_active_products_count() }} produits</small>
                            <div class="d-flex gap-2">
                                <a href="{{ url_for('shop.view', slug=shop.slug) }}" class="btn btn-outline-primary btn-sm">Visiter Boutique</a>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" title="Partager">
                                        <i class="fas fa-share-alt"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="shareOnFacebook('{{ url_for('shop.view', slug=shop.slug, _external=True) }}', '{{ shop.name }}')">
                                                <i class="fab fa-facebook text-primary me-2"></i>Facebook
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="shareOnWhatsApp('{{ url_for('shop.view', slug=shop.slug, _external=True) }}', '{{ shop.name }}')">
                                                <i class="fab fa-whatsapp text-success me-2"></i>WhatsApp
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="shareOnTwitter('{{ url_for('shop.view', slug=shop.slug, _external=True) }}', '{{ shop.name }}')">
                                                <i class="fab fa-twitter text-info me-2"></i>Twitter
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="shareOnTelegram('{{ url_for('shop.view', slug=shop.slug, _external=True) }}', '{{ shop.name }}')">
                                                <i class="fab fa-telegram text-primary me-2"></i>Telegram
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="copyToClipboard('{{ url_for('shop.view', slug=shop.slug, _external=True) }}')">
                                                <i class="fas fa-copy text-secondary me-2"></i>Copier le lien
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        <div class="text-center mt-4">
            <a href="{{ url_for('shop.browse') }}" class="btn btn-outline-primary">Parcourir Toutes les Boutiques</a>
        </div>
    </div>
</section>
{% endif %}

<!-- Popular Products Section -->
{% if popular_products %}
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">Produits Populaires</h2>
        <div class="row">
            {% for product in popular_products %}
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card h-100 product-card">
                    <div class="position-relative">
                        <img src="{{ product.get_main_image() }}" class="card-img-top" alt="{{ product.name }}" style="height: 200px; object-fit: cover;">
                        {% if product.is_on_sale() %}
                        <span class="badge bg-danger position-absolute top-0 end-0 m-2">
                            -{{ product.get_discount_percentage() }}%
                        </span>
                        {% endif %}
                    </div>
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title">{{ product.name }}</h6>
                        <p class="card-text text-muted small">{{ product.short_description[:100] }}...</p>
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    {% if product.is_on_sale() %}
                                    <span class="text-decoration-line-through text-muted">{{ "%.0f"|format(product.price) }} {{ product.shop.get_currency_symbol() }}</span>
                                    <span class="fw-bold text-primary">{{ "%.0f"|format(product.sale_price) }} {{ product.shop.get_currency_symbol() }}</span>
                                    {% else %}
                                    <span class="fw-bold text-primary">{{ "%.0f"|format(product.price) }} {{ product.shop.get_currency_symbol() }}</span>
                                    {% endif %}
                                </div>
                                <div class="rating">
                                    {% for i in range(5) %}
                                        {% if i < product.get_average_rating() %}
                                        <i class="fas fa-star text-warning"></i>
                                        {% else %}
                                        <i class="far fa-star text-warning"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                            <a href="{{ url_for('product.view', slug=product.slug) }}" class="btn btn-primary btn-sm mt-2 w-100">Voir Produit</a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Call to Action Section -->
<section class="py-5 bg-primary text-white">
    <div class="container text-center">
        <h2 class="mb-4">Prêt à Commencer à Vendre ?</h2>
        <p class="lead mb-4">Rejoignez des milliers de vendeurs africains qui vendent déjà sur Afroly.org. C'est gratuit pour commencer !</p>
        <div class="d-flex flex-column justify-content-center align-items-center">
            <a href="{{ url_for('auth.register') }}" class="btn btn-light btn-lg mb-4">Commencer à Vendre Aujourd'hui</a>

            <!-- Share Buttons -->
            <div class="share-section text-center">
                <div class="mb-3">
                    <small class="text-white-50">Partager Afroly.org :</small>
                </div>
                <div class="d-flex justify-content-center align-items-center gap-2 flex-wrap">
                    <button class="btn btn-outline-light btn-sm rounded-circle" onclick="shareOnFacebook('{{ request.url_root }}', 'Afroly.org - Votre Destination Shopping Africaine')" title="Partager sur Facebook">
                        <i class="fab fa-facebook"></i>
                    </button>
                    <button class="btn btn-outline-light btn-sm rounded-circle" onclick="shareOnWhatsApp('{{ request.url_root }}', 'Découvrez Afroly.org - Votre Destination Shopping Africaine')" title="Partager sur WhatsApp">
                        <i class="fab fa-whatsapp"></i>
                    </button>
                    <button class="btn btn-outline-light btn-sm rounded-circle" onclick="shareOnTwitter('{{ request.url_root }}', 'Découvrez Afroly.org - Votre Destination Shopping Africaine')" title="Partager sur Twitter">
                        <i class="fab fa-twitter"></i>
                    </button>
                    <button class="btn btn-outline-light btn-sm rounded-circle" onclick="shareOnTelegram('{{ request.url_root }}', 'Découvrez Afroly.org - Votre Destination Shopping Africaine')" title="Partager sur Telegram">
                        <i class="fab fa-telegram"></i>
                    </button>
                    <button class="btn btn-outline-light btn-sm rounded-circle" onclick="copyToClipboard('{{ request.url_root }}')" title="Copier le lien">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_head %}
<style>
.hero-section {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    position: relative;
    z-index: 1;
}

.hero-section .btn {
    position: relative;
    z-index: 100 !important;
    pointer-events: auto !important;
    display: inline-block !important;
    text-decoration: none !important;
}

.hero-section .btn:hover {
    z-index: 101 !important;
    pointer-events: auto !important;
}

.category-card:hover {
    transform: translateY(-5px);
    transition: transform 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.product-card:hover {
    transform: translateY(-3px);
    transition: transform 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.shop-card:hover {
    transform: translateY(-3px);
    transition: transform 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.rating i {
    font-size: 0.8rem;
}

.bg-gradient-success {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
}

.launch-store-section .card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
}

.launch-store-section .badge {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.launch-store-section .btn {
    border-radius: 50px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.launch-store-section .alert {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border-radius: 15px;
}

.launch-store-section .badge {
    border-radius: 25px;
    font-weight: 500;
    font-size: 0.9rem;
}

.launch-store-section .badge.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
    color: white !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.launch-store-section .badge.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Permanent Highlight Box */
.permanent-highlight-box {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border: 2px solid #17a2b8;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.2);
    animation: none !important;
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    position: relative;
    z-index: 10;
}

.permanent-highlight-box h5 {
    color: #0c5460;
    font-weight: 600;
}

.permanent-highlight-box p {
    color: #0c5460;
    margin-bottom: 0.5rem;
}

.permanent-highlight-box .text-success {
    color: #155724 !important;
    font-weight: 700;
}

.social-platform-item {
    text-align: center;
    transition: transform 0.3s ease;
}

.social-platform-item:hover {
    transform: translateY(-5px);
}

.social-logo-container {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.social-logo-container:hover {
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    transform: scale(1.05);
}

.social-logo-container i {
    font-size: 24px;
    color: white;
}

.social-logo-container.facebook {
    background: linear-gradient(135deg, #1877f2 0%, #166fe5 100%);
}

.social-logo-container.instagram {
    background: linear-gradient(135deg, #e4405f 0%, #833ab4 50%, #fccc63 100%);
}

.social-logo-container.whatsapp {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
}

.social-logo-container.tiktok {
    background: linear-gradient(135deg, #000000 0%, #ff0050 50%, #00f2ea 100%);
}

.social-logo-container.telegram {
    background: linear-gradient(135deg, #0088cc 0%, #005580 100%);
}

/* Payment Methods Badges */
.payment-method-badge {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    padding: 6px 10px;
    font-size: 0.8rem;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    white-space: nowrap;
}

.payment-method-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .payment-method-badge {
        font-size: 0.75rem;
        padding: 6px 10px;
    }

    /* Hero section mobile optimization */
    .hero-section {
        padding: 2rem 0 !important;
    }

    .hero-section h1 {
        font-size: 1.8rem !important;
    }

    .hero-section .lead {
        font-size: 1rem !important;
    }

    .hero-section .btn {
        font-size: 0.9rem;
        padding: 0.6rem 1.2rem;
        margin-bottom: 0.5rem;
    }

    /* Launch store section mobile */
    .launch-store-section {
        padding: 2rem 0 !important;
    }

    .launch-store-section .display-5 {
        font-size: 1.5rem !important;
    }

    .launch-store-section .fs-4 {
        font-size: 1.1rem !important;
    }

    .launch-store-section .fs-5 {
        font-size: 1rem !important;
    }

    /* Social platforms mobile */
    .social-platform-item {
        margin-bottom: 1rem;
    }

    .social-logo-container {
        width: 50px;
        height: 50px;
    }

    .social-logo-container i {
        font-size: 20px;
    }

    /* Payment methods mobile */
    .payment-method-badge {
        margin-bottom: 0.5rem;
    }
}

/* Share Buttons Styling */
.share-section .btn.rounded-circle {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.share-section .btn.rounded-circle:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.6);
    transform: translateY(-2px);
}

.share-section .btn.rounded-circle i {
    font-size: 16px;
}

/* 3G/4G Network Optimizations */
@media (max-width: 768px) and (max-resolution: 150dpi) {
    /* Reduce image quality for slower connections */
    img {
        image-rendering: optimizeSpeed;
    }

    /* Simplify animations for performance */
    .social-platform-item:hover {
        transform: none;
    }

    .payment-method-badge:hover {
        transform: none;
    }

    .share-section .btn.rounded-circle:hover {
        transform: none;
    }
}
</style>

<script>
// Share functions
function shareOnFacebook(url, title) {
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank', 'width=600,height=400');
}

function shareOnTwitter(url, title) {
    window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`, '_blank', 'width=600,height=400');
}

function shareOnWhatsApp(url, title) {
    window.open(`https://wa.me/?text=${encodeURIComponent(title + ' ' + url)}`, '_blank');
}

function shareOnTelegram(url, title) {
    window.open(`https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`, '_blank');
}

function copyToClipboard(url) {
    navigator.clipboard.writeText(url).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'alert alert-success alert-dismissible fade show position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
        toast.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            Lien copié dans le presse-papiers!
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(toast);

        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    });
}

// Debug function to ensure buttons are clickable
document.addEventListener('DOMContentLoaded', function() {
    // Add click event listeners to hero buttons for debugging
    const heroButtons = document.querySelectorAll('.hero-section .btn');
    heroButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            console.log('Button clicked:', this.textContent.trim(), 'URL:', this.href);
            // Allow normal navigation to proceed
        });
    });
});
</script>
{% endblock %}
