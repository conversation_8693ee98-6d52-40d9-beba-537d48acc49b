{% extends "base.html" %}

{% block title %}Panier - {{ shop.name }} - Afroly.org{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('shop.view', slug=shop.slug) }}">{{ shop.name }}</a></li>
            <li class="breadcrumb-item active">Panier</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-12">
            <!-- Shop Header -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex align-items-center">
                        {% if shop.logo %}
                        <img src="/static/uploads/shops/{{ shop.logo }}" alt="{{ shop.name }}"
                             class="rounded-circle me-3" style="width: 50px; height: 50px; object-fit: cover;">
                        {% else %}
                        <div class="bg-white rounded-circle me-3 d-flex align-items-center justify-content-center"
                             style="width: 50px; height: 50px;">
                            <i class="fas fa-store text-primary"></i>
                        </div>
                        {% endif %}
                        <div>
                            <h4 class="mb-0">Panier - {{ shop.name }}</h4>
                            <small class="opacity-75">{{ shop.country }}</small>
                        </div>
                    </div>
                </div>
            </div>

            {% if cart_items %}
            <!-- Cart Items -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>Articles dans votre Panier
                    </h5>
                </div>
                <div class="card-body">
                    {% for item in cart_items %}
                    <div class="row align-items-center mb-4 {% if not loop.last %}border-bottom pb-4{% endif %}">
                        <!-- Product Image -->
                        <div class="col-md-2">
                            <img src="{{ item.product.get_main_image() }}" alt="{{ item.product.name }}"
                                 class="img-fluid rounded" style="max-height: 100px; object-fit: cover;">
                        </div>

                        <!-- Product Details -->
                        <div class="col-md-4">
                            <h6 class="mb-1">
                                <a href="{{ url_for('product.view', slug=item.product.slug) }}"
                                   class="text-decoration-none">{{ item.product.name }}</a>
                            </h6>
                            <p class="text-muted mb-1">{{ item.product.description[:100] }}{% if item.product.description|length > 100 %}...{% endif %}</p>
                            <small class="text-success">
                                <i class="fas fa-check-circle me-1"></i>En stock ({{ item.product.stock_quantity }} disponibles)
                            </small>
                        </div>

                        <!-- Price -->
                        <div class="col-md-2 text-center">
                            <div class="fw-bold">{{ "%.0f"|format(item.product.get_display_price()) }} {{ shop.get_currency_symbol() }}</div>
                            <small class="text-muted">Prix unitaire</small>
                        </div>

                        <!-- Quantity Controls -->
                        <div class="col-md-2">
                            <form method="POST" action="{{ url_for('cart.update_cart') }}" class="d-inline">
                                <input type="hidden" name="product_id" value="{{ item.product.id }}">
                                <input type="hidden" name="shop_id" value="{{ shop.id }}">
                                <div class="input-group">
                                    <button class="btn btn-outline-secondary btn-sm" type="button"
                                            onclick="decreaseQuantity({{ item.product.id }})">-</button>
                                    <input type="number" class="form-control form-control-sm text-center"
                                           name="quantity" id="quantity_{{ item.product.id }}"
                                           value="{{ item.quantity }}" min="1" max="{{ item.product.stock_quantity }}"
                                           onchange="this.form.submit()">
                                    <button class="btn btn-outline-secondary btn-sm" type="button"
                                            onclick="increaseQuantity({{ item.product.id }}, {{ item.product.stock_quantity }})">+</button>
                                </div>
                            </form>
                        </div>

                        <!-- Total & Actions -->
                        <div class="col-md-2 text-end">
                            <div class="fw-bold text-primary mb-2">
                                {{ "%.0f"|format(item.get_total_price()) }} {{ shop.get_currency_symbol() }}
                            </div>
                            <form method="POST" action="{{ url_for('cart.remove_from_cart') }}" class="d-inline">
                                <input type="hidden" name="product_id" value="{{ item.product.id }}">
                                <input type="hidden" name="shop_id" value="{{ shop.id }}">
                                <button type="submit" class="btn btn-outline-danger btn-sm"
                                        onclick="return confirm('Êtes-vous sûr de vouloir supprimer cet article ?')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Cart Summary -->
            <div class="row">
                <div class="col-lg-8">
                    <!-- Continue Shopping -->
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-arrow-left me-2"></i>Continuer vos Achats
                            </h6>
                            <p class="card-text">Découvrez plus de produits dans cette boutique.</p>
                            <a href="{{ url_for('shop.view', slug=shop.slug) }}" class="btn btn-outline-primary">
                                <i class="fas fa-store me-2"></i>Retour à la Boutique
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Order Summary -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-calculator me-2"></i>Résumé de la Commande
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Sous-total ({{ cart_items|length }} articles):</span>
                                <span>{{ "%.0f"|format(subtotal) }} {{ shop.get_currency_symbol() }}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Livraison:</span>
                                {% if shipping_cost > 0 %}
                                <span>{{ "%.0f"|format(shipping_cost) }} {{ shop.get_currency_symbol() }}</span>
                                {% else %}
                                <span class="text-success">Gratuite</span>
                                {% endif %}
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-3">
                                <strong>Total:</strong>
                                <strong class="text-primary">{{ "%.0f"|format(total) }} {{ shop.get_currency_symbol() }}</strong>
                            </div>

                            <div class="d-grid">
                                <a href="{{ url_for('cart.checkout', shop_id=shop.id) }}" class="btn btn-primary btn-lg">
                                    <i class="fas fa-credit-card me-2"></i>Finaliser la Commande
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Info -->
                    {% if shop.get_whatsapp_number() or shop.contact_email %}
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-phone me-2"></i>Contact Vendeur
                            </h6>
                        </div>
                        <div class="card-body">
                            {% if shop.get_whatsapp_number() %}
                            <div class="mb-2">
                                <a href="{{ shop.get_whatsapp_link() }}" target="_blank" class="btn btn-success btn-sm w-100">
                                    <i class="fab fa-whatsapp me-2"></i>WhatsApp: {{ shop.get_whatsapp_number() }}
                                </a>
                            </div>
                            {% endif %}
                            {% if shop.contact_email %}
                            <div class="mb-0">
                                <small class="text-muted">
                                    <i class="fas fa-envelope me-1"></i>{{ shop.contact_email }}
                                </small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            {% else %}
            <!-- Empty Cart -->
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-shopping-cart fa-4x text-muted mb-4"></i>
                    <h3>Votre panier est vide</h3>
                    <p class="text-muted mb-4">Ajoutez des produits de cette boutique pour les voir ici.</p>
                    <div class="d-flex gap-3 justify-content-center">
                        <a href="{{ url_for('shop.view', slug=shop.slug) }}" class="btn btn-primary">
                            <i class="fas fa-store me-2"></i>Parcourir la Boutique
                        </a>
                        <a href="{{ url_for('cart.my_carts') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-shopping-cart me-2"></i>Mes Autres Paniers
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function increaseQuantity(productId, maxQuantity) {
    const input = document.getElementById('quantity_' + productId);
    const currentValue = parseInt(input.value);
    if (currentValue < maxQuantity) {
        input.value = currentValue + 1;
        input.form.submit();
    }
}

function decreaseQuantity(productId) {
    const input = document.getElementById('quantity_' + productId);
    const currentValue = parseInt(input.value);
    if (currentValue > 1) {
        input.value = currentValue - 1;
        input.form.submit();
    }
}

// Auto-submit form when quantity changes
document.addEventListener('DOMContentLoaded', function() {
    const quantityInputs = document.querySelectorAll('input[name="quantity"]');
    quantityInputs.forEach(input => {
        input.addEventListener('change', function() {
            if (this.value < 1) {
                this.value = 1;
            }
            if (this.value > parseInt(this.getAttribute('max'))) {
                this.value = this.getAttribute('max');
            }
        });
    });
});
</script>

<style>
.input-group .form-control {
    border-left: 0;
    border-right: 0;
}

.input-group .btn {
    border-radius: 0;
}

.input-group .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.input-group .btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}
</style>
{% endblock %}
