{% extends "base.html" %}

{% block title %}Finaliser la Commande - {{ shop.name }} - Afroly.org{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('shop.view', slug=shop.slug) }}">{{ shop.name }}</a></li>
            <li class="breadcrumb-item active">Finaliser la Commande</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Order Summary -->
        <div class="col-lg-4 order-lg-2 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>R<PERSON>umé de la Commande
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Shop Info -->
                    <div class="shop-info mb-3 pb-3 border-bottom">
                        <div class="d-flex align-items-center">
                            {% if shop.logo %}
                            <img src="/static/uploads/shops/{{ shop.logo }}" alt="{{ shop.name }}"
                                 class="rounded-circle me-2" style="width: 40px; height: 40px; object-fit: cover;">
                            {% endif %}
                            <div>
                                <h6 class="mb-0">{{ shop.name }}</h6>
                                <small class="text-muted">{{ shop.country }}</small>
                            </div>
                        </div>
                    </div>

                    <!-- Cart Items -->
                    <div class="cart-items mb-3">
                        {% for item in cart.items %}
                        <div class="d-flex align-items-center mb-3 {% if not loop.last %}pb-3 border-bottom{% endif %}">
                            <img src="{{ item.product.get_main_image() }}" alt="{{ item.product.name }}"
                                 class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ item.product.name }}</h6>
                                <small class="text-muted">Quantité: {{ item.quantity }}</small>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold">{{ "%.0f"|format(item.get_total_price()) }} {{ shop.get_currency_symbol() }}</div>
                                {% if item.quantity > 1 %}
                                <small class="text-muted">{{ "%.0f"|format(item.product.get_display_price()) }} {{ shop.get_currency_symbol() }} chacun</small>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Order Totals -->
                    <div class="order-totals">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Sous-total:</span>
                            <span>{{ "%.0f"|format(subtotal) }} {{ shop.get_currency_symbol() }}</span>
                        </div>
                        {% if shipping_cost > 0 %}
                        <div class="d-flex justify-content-between mb-2">
                            <span>Livraison:</span>
                            <span>{{ "%.0f"|format(shipping_cost) }} {{ shop.get_currency_symbol() }}</span>
                        </div>
                        {% else %}
                        <div class="d-flex justify-content-between mb-2">
                            <span>Livraison:</span>
                            <span class="text-success">Gratuite</span>
                        </div>
                        {% endif %}
                        {% if tax_amount > 0 %}
                        <div class="d-flex justify-content-between mb-2">
                            <span>Taxes:</span>
                            <span>{{ "%.0f"|format(tax_amount) }} {{ shop.get_currency_symbol() }}</span>
                        </div>
                        {% endif %}
                        <hr>
                        <div class="d-flex justify-content-between">
                            <strong>Total:</strong>
                            <strong class="text-primary">{{ "%.0f"|format(total) }} {{ shop.get_currency_symbol() }}</strong>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Methods -->
            {% set payment_methods = shop.get_payment_methods() %}
            {% if payment_methods %}
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>Méthodes de Paiement Acceptées
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for method in payment_methods %}
                        {% set method_config = None %}
                        {% for category, methods in config.PAYMENT_METHODS.items() %}
                            {% if method.get('type') in methods and not method_config %}
                                {% set method_config = methods[method.get('type')] %}
                            {% endif %}
                        {% endfor %}
                        {% if method_config %}
                        <div class="col-6 mb-2">
                            <div class="text-center">
                                <i class="{{ method_config.icon }} fa-2x mb-1" style="color: {{ method_config.color }}"></i>
                                <div><small>{{ method_config.name }}</small></div>
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Vendor Contact - Permanent -->
            <div class="card mt-4 border-success sticky-contact" style="position: sticky; top: 20px;">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-phone me-2"></i>Contact du Vendeur
                    </h6>
                </div>
                <div class="card-body">
                    <div class="vendor-contact-info">
                        <div class="mb-2">
                            <i class="fas fa-envelope text-primary me-2"></i>
                            <strong>Email:</strong>
                            <a href="mailto:{{ shop.contact_email }}" class="text-decoration-none">{{ shop.contact_email }}</a>
                        </div>
                        {% if shop.contact_phone %}
                        <div class="mb-2">
                            <i class="fas fa-phone text-primary me-2"></i>
                            <strong>Téléphone:</strong>
                            <a href="tel:{{ shop.contact_phone }}" class="text-decoration-none">{{ shop.contact_phone }}</a>
                        </div>
                        {% endif %}
                        {% if shop.get_whatsapp_number() %}
                        <div class="mb-3">
                            <i class="fab fa-whatsapp text-success me-2"></i>
                            <strong>WhatsApp:</strong>
                            <a href="{{ shop.get_whatsapp_link('Bonjour, j\'ai une question concernant ma commande sur Afroly.org') }}"
                               target="_blank" class="text-success text-decoration-none fw-bold">
                                {{ shop.get_whatsapp_number() }}
                            </a>
                        </div>
                        <div class="d-grid">
                            <a href="{{ shop.get_whatsapp_link('Bonjour, j\'ai une question concernant ma commande sur Afroly.org') }}"
                               target="_blank" class="btn btn-success btn-sm">
                                <i class="fab fa-whatsapp me-2"></i>Contacter sur WhatsApp
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="card-footer bg-light text-center">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>Contact direct et sécurisé
                    </small>
                </div>
            </div>
        </div>

        <!-- Checkout Form -->
        <div class="col-lg-8 order-lg-1">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>Informations de Livraison
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('cart.place_order', shop_id=shop.id) }}" id="checkoutForm">
                        <div class="row">
                            <!-- Personal Information -->
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">Prénom *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name"
                                       value="{{ current_user.first_name or '' }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Nom *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name"
                                       value="{{ current_user.last_name or '' }}" required>
                            </div>
                        </div>

                        <!-- Address Information -->
                        <div class="mb-3">
                            <label for="address_line_1" class="form-label">Adresse *</label>
                            <input type="text" class="form-control" id="address_line_1" name="address_line_1"
                                   placeholder="Numéro et nom de rue" required>
                        </div>
                        <div class="mb-3">
                            <label for="address_line_2" class="form-label">Complément d'adresse</label>
                            <input type="text" class="form-control" id="address_line_2" name="address_line_2"
                                   placeholder="Appartement, étage, etc. (optionnel)">
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="city" class="form-label">Ville *</label>
                                <input type="text" class="form-control" id="city" name="city" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="state" class="form-label">État/Province *</label>
                                <input type="text" class="form-control" id="state" name="state" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="postal_code" class="form-label">Code Postal</label>
                                <input type="text" class="form-control" id="postal_code" name="postal_code">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="country" class="form-label">Pays *</label>
                                <input type="text" class="form-control" id="country" name="country"
                                       value="{{ shop.country }}" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">Téléphone *</label>
                            <input type="tel" class="form-control" id="phone" name="phone"
                                   value="{{ current_user.phone or '' }}" required>
                        </div>

                        <!-- Order Notes -->
                        <div class="mb-4">
                            <label for="notes" class="form-label">Notes de Commande</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"
                                      placeholder="Instructions spéciales pour la livraison, préférences, etc."></textarea>
                        </div>

                        <!-- Payment Instructions -->
                        <div class="card border-info mb-4">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Instructions de Paiement</h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-2">Après avoir finalisé votre commande, le vendeur vous contactera pour organiser le paiement et la livraison.</p>
                                <p class="mb-0"><strong>💡 Conseil:</strong> Contactez le vendeur sur WhatsApp pour un suivi rapide de votre commande.</p>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary btn-lg flex-grow-1">
                                <i class="fas fa-check me-2"></i>Finaliser la Commande
                            </button>
                            <a href="{{ url_for('shop.view', slug=shop.slug) }}" class="btn btn-outline-secondary btn-lg">
                                <i class="fas fa-arrow-left me-2"></i>Retour
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.getElementById('checkoutForm');

    form.addEventListener('submit', function(e) {
        const requiredFields = form.querySelectorAll('input[required], textarea[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('Veuillez remplir tous les champs obligatoires.');
        }
    });

    // Remove validation styling on input
    const inputs = form.querySelectorAll('input, textarea');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            this.classList.remove('is-invalid');
        });
    });
});
</script>
{% endblock %}
