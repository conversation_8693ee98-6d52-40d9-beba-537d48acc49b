{% extends "base.html" %}

{% block title %}Commande Confirmée - {{ order.order_number }} - Afroly.org{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Success Message -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-4">
                <div class="mb-4">
                    <i class="fas fa-check-circle fa-5x text-success"></i>
                </div>
                <h1 class="display-5 fw-bold text-success">Commande Confirmée !</h1>
                <p class="lead">Merci pour votre commande. Votre commande a été reçue et est en cours de traitement.</p>
            </div>
        </div>
    </div>

    <!-- Order Details -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-receipt me-2"></i>Détails de la Commande
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Order Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-primary">Numéro de Commande</h6>
                            <p class="fw-bold">{{ order.order_number }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">Date de Commande</h6>
                            <p>{{ order.created_at.strftime('%d/%m/%Y à %H:%M') }}</p>
                        </div>
                    </div>

                    <!-- Shop Info -->
                    <div class="shop-info mb-4 pb-4 border-bottom">
                        <h6 class="text-primary mb-3">Boutique</h6>
                        <div class="d-flex align-items-center mb-3">
                            {% if order.shop.logo %}
                            <img src="/static/uploads/shops/{{ order.shop.logo }}" alt="{{ order.shop.name }}"
                                 class="rounded-circle me-3" style="width: 50px; height: 50px; object-fit: cover;">
                            {% endif %}
                            <div>
                                <h6 class="mb-1">{{ order.shop.name }}</h6>
                                <p class="text-muted mb-1">{{ order.shop.contact_email }}</p>
                                {% if order.shop.contact_phone %}
                                <p class="text-muted mb-0">{{ order.shop.contact_phone }}</p>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Payment Methods Accepted -->
                        {% set payment_methods = order.shop.get_payment_methods() %}
                        {% if payment_methods %}
                        <div class="payment-methods-accepted">
                            <h6 class="text-secondary mb-2">
                                <i class="fas fa-credit-card me-2"></i>Méthodes de Paiement Acceptées
                            </h6>
                            <div class="row">
                                {% for method in payment_methods[:6] %}
                                {% set method_config = None %}
                                {% for category, methods in config.PAYMENT_METHODS.items() %}
                                    {% if method.get('type') in methods and not method_config %}
                                        {% set method_config = methods[method.get('type')] %}
                                    {% endif %}
                                {% endfor %}

                                {% if method_config %}
                                <div class="col-4 col-md-3 mb-2">
                                    <div class="payment-method-badge">
                                        <span class="badge bg-light text-dark border p-2 w-100 d-flex align-items-center justify-content-center" style="min-height: 35px;">
                                            <i class="{{ method_config.icon }} me-1" style="color: {{ method_config.color }}; font-size: 0.9rem;"></i>
                                            <small>{{ method_config.name }}</small>
                                        </span>
                                    </div>
                                </div>
                                {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}

                        <!-- WhatsApp Contact Highlight -->
                        {% if order.shop.get_whatsapp_number() %}
                        <div class="whatsapp-contact-highlight mt-3">
                            <div class="alert alert-success d-flex align-items-center">
                                <i class="fab fa-whatsapp fa-2x me-3"></i>
                                <div>
                                    <h6 class="mb-1">Contact Direct WhatsApp</h6>
                                    <small>Contactez {{ order.shop.name }} directement sur WhatsApp pour le suivi de votre commande</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Order Items -->
                    <div class="order-items mb-4 pb-4 border-bottom">
                        <h6 class="text-primary mb-3">Articles Commandés</h6>
                        {% for item in order.items %}
                        <div class="d-flex align-items-center mb-3 {% if not loop.last %}pb-3 border-bottom{% endif %}">
                            <img src="{{ item.product.get_main_image() }}" alt="{{ item.product.name }}"
                                 class="rounded me-3" style="width: 60px; height: 60px; object-fit: cover;">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ item.product.name }}</h6>
                                <p class="text-muted mb-0">Quantité: {{ item.quantity }}</p>
                                <small class="text-muted">{{ "%.0f"|format(item.price) }} {{ order.shop.get_currency_symbol() }} × {{ item.quantity }}</small>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold">{{ "%.0f"|format(item.total) }} {{ order.shop.get_currency_symbol() }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Order Totals -->
                    <div class="order-totals mb-4 pb-4 border-bottom">
                        <h6 class="text-primary mb-3">Résumé des Coûts</h6>
                        <div class="row">
                            <div class="col-md-6 ms-auto">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Sous-total:</span>
                                    <span>{{ "%.0f"|format(order.subtotal) }} {{ order.shop.get_currency_symbol() }}</span>
                                </div>
                                {% if order.shipping_cost > 0 %}
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Livraison:</span>
                                    <span>{{ "%.0f"|format(order.shipping_cost) }} {{ order.shop.get_currency_symbol() }}</span>
                                </div>
                                {% else %}
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Livraison:</span>
                                    <span class="text-success">Gratuite</span>
                                </div>
                                {% endif %}
                                {% if order.tax_amount > 0 %}
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Taxes:</span>
                                    <span>{{ "%.0f"|format(order.tax_amount) }} {{ order.shop.get_currency_symbol() }}</span>
                                </div>
                                {% endif %}
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <strong>Total:</strong>
                                    <strong class="text-primary">{{ "%.0f"|format(order.total) }} {{ order.shop.get_currency_symbol() }}</strong>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Shipping Address -->
                    <div class="shipping-address mb-4">
                        <h6 class="text-primary mb-3">Adresse de Livraison</h6>
                        <div class="bg-light p-3 rounded">
                            {% set addr = order.shipping_address %}
                            <p class="mb-1"><strong>{{ addr.first_name }} {{ addr.last_name }}</strong></p>
                            <p class="mb-1">{{ addr.address_line_1 }}</p>
                            {% if addr.address_line_2 %}
                            <p class="mb-1">{{ addr.address_line_2 }}</p>
                            {% endif %}
                            <p class="mb-1">{{ addr.city }}, {{ addr.state }}</p>
                            {% if addr.postal_code %}
                            <p class="mb-1">{{ addr.postal_code }}</p>
                            {% endif %}
                            <p class="mb-1">{{ addr.country }}</p>
                            <p class="mb-0"><strong>Tél:</strong> {{ addr.phone }}</p>
                        </div>
                    </div>

                    <!-- Order Status -->
                    <div class="order-status">
                        <h6 class="text-primary mb-3">Statut de la Commande</h6>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-warning fs-6 me-3">
                                <i class="fas fa-clock me-1"></i>En Attente
                            </span>
                            <span class="text-muted">Votre commande est en attente de confirmation par le vendeur.</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Next Steps -->
    <div class="row justify-content-center mt-4">
        <div class="col-lg-8">
            <div class="card bg-light">
                <div class="card-body">
                    <h6 class="text-primary mb-3">Prochaines Étapes</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-start mb-3">
                                <i class="fas fa-envelope text-primary me-3 mt-1"></i>
                                <div>
                                    <h6 class="mb-1">Confirmation par Email</h6>
                                    <small class="text-muted">Vous recevrez un email de confirmation avec tous les détails.</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-start mb-3">
                                <i class="fas fa-user-check text-primary me-3 mt-1"></i>
                                <div>
                                    <h6 class="mb-1">Confirmation du Vendeur</h6>
                                    <small class="text-muted">Le vendeur confirmera votre commande sous 24h.</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-start mb-3">
                                <i class="fas fa-shipping-fast text-primary me-3 mt-1"></i>
                                <div>
                                    <h6 class="mb-1">Préparation & Expédition</h6>
                                    <small class="text-muted">Votre commande sera préparée et expédiée.</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-start mb-3">
                                {% if order.shop.get_whatsapp_number() %}
                                <i class="fab fa-whatsapp text-success me-3 mt-1"></i>
                                <div>
                                    <h6 class="mb-1 text-success">Contact WhatsApp Recommandé</h6>
                                    <small class="text-muted">Contactez le vendeur sur WhatsApp pour un suivi rapide et personnalisé.</small>
                                </div>
                                {% else %}
                                <i class="fas fa-phone text-primary me-3 mt-1"></i>
                                <div>
                                    <h6 class="mb-1">Contact Direct</h6>
                                    <small class="text-muted">Contactez le vendeur directement pour toute question.</small>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- WhatsApp Encouragement -->
    {% if order.shop.get_whatsapp_number() %}
    <div class="row justify-content-center mt-4">
        <div class="col-lg-8">
            <div class="card border-success">
                <div class="card-body text-center bg-light">
                    <div class="mb-3">
                        <i class="fab fa-whatsapp fa-3x text-success"></i>
                    </div>
                    <h5 class="text-success mb-3">Restez en Contact avec {{ order.shop.name }}</h5>
                    <p class="mb-3">
                        Pour un suivi optimal de votre commande <strong>{{ order.order_number }}</strong>,
                        nous vous encourageons à contacter directement le vendeur sur WhatsApp.
                        Vous obtiendrez des réponses rapides et un service personnalisé !
                    </p>
                    <a href="{{ order.shop.get_whatsapp_link('Bonjour, je viens de passer la commande ' + order.order_number + ' sur Afroly.org. Pouvez-vous me confirmer la réception ?') }}"
                       target="_blank" class="btn btn-success btn-lg">
                        <i class="fab fa-whatsapp me-2"></i>Contacter Maintenant sur WhatsApp
                    </a>
                    <div class="mt-2">
                        <small class="text-muted">{{ order.shop.get_whatsapp_number() }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Action Buttons -->
    <div class="row justify-content-center mt-4">
        <div class="col-lg-8">
            <div class="d-flex gap-2 justify-content-center flex-wrap">
                <a href="{{ url_for('main.index') }}" class="btn btn-primary">
                    <i class="fas fa-home me-2"></i>Retour à l'Accueil
                </a>
                <a href="{{ url_for('shop.view', slug=order.shop.slug) }}" class="btn btn-outline-primary">
                    <i class="fas fa-store me-2"></i>Retour à la Boutique
                </a>
                {% if order.shop.get_whatsapp_number() %}
                <a href="{{ order.shop.get_whatsapp_link('Bonjour, concernant ma commande ' + order.order_number + ' sur Afroly.org') }}"
                   target="_blank" class="btn btn-success">
                    <i class="fab fa-whatsapp me-2"></i>WhatsApp
                </a>
                {% endif %}
                <button onclick="window.print()" class="btn btn-outline-secondary">
                    <i class="fas fa-print me-2"></i>Imprimer
                </button>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .navbar, .breadcrumb {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .container {
        max-width: 100% !important;
    }
}
</style>
{% endblock %}
