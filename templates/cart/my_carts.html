{% extends "base.html" %}

{% block title %}Mes Paniers - AfroMall{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <h1 class="display-5 fw-bold mb-4">
                <i class="fas fa-shopping-cart me-3"></i>Mes Paniers
            </h1>
            <p class="lead text-muted">Gérez vos paniers par boutique</p>
        </div>
    </div>

    {% if carts %}
    <div class="row">
        {% for cart in carts %}
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="card h-100">
                <!-- Shop Header -->
                <div class="card-header bg-primary text-white">
                    <div class="d-flex align-items-center">
                        {% if cart.shop.logo %}
                        <img src="/static/uploads/shops/{{ cart.shop.logo }}" alt="{{ cart.shop.name }}"
                             class="rounded-circle me-3" style="width: 40px; height: 40px; object-fit: cover;">
                        {% else %}
                        <div class="bg-white rounded-circle me-3 d-flex align-items-center justify-content-center"
                             style="width: 40px; height: 40px;">
                            <i class="fas fa-store text-primary"></i>
                        </div>
                        {% endif %}
                        <div>
                            <h6 class="mb-0">{{ cart.shop.name }}</h6>
                            <small class="opacity-75">{{ cart.get_total_items() }} articles</small>
                        </div>
                    </div>
                </div>

                <!-- Cart Items -->
                <div class="card-body">
                    {% for item in cart.items[:3] %}
                    <div class="d-flex align-items-center mb-3 {% if not loop.last %}border-bottom pb-3{% endif %}">
                        <img src="{{ item.product.get_main_image() }}" alt="{{ item.product.name }}"
                             class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ item.product.name }}</h6>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">Qté: {{ item.quantity }}</small>
                                <span class="fw-bold text-primary">{{ "%.0f"|format(item.get_total_price()) }} {{ cart.shop.get_currency_symbol() }}</span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}

                    {% if cart.items|length > 3 %}
                    <div class="text-center">
                        <small class="text-muted">... et {{ cart.items|length - 3 }} autres articles</small>
                    </div>
                    {% endif %}
                </div>

                <!-- Cart Footer -->
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="fw-bold">Total:</span>
                        <span class="h5 text-primary mb-0">{{ "%.0f"|format(cart.get_total_price()) }} {{ cart.shop.get_currency_symbol() }}</span>
                    </div>

                    <div class="d-grid gap-2">
                        <a href="{{ url_for('cart.view_cart', shop_id=cart.shop_id) }}" class="btn btn-primary">
                            <i class="fas fa-eye me-2"></i>Voir le Panier
                        </a>
                        <a href="{{ url_for('cart.checkout', shop_id=cart.shop_id) }}" class="btn btn-success">
                            <i class="fas fa-credit-card me-2"></i>Commander
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Summary -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h4 class="text-primary">{{ carts|length }}</h4>
                            <small class="text-muted">Boutiques</small>
                        </div>
                        <div class="col-md-3">
                            {% set total_items = [] %}
                            {% for cart in carts %}
                                {% set _ = total_items.append(cart.get_total_items()) %}
                            {% endfor %}
                            <h4 class="text-info">{{ total_items|sum }}</h4>
                            <small class="text-muted">Articles Total</small>
                        </div>
                        <div class="col-md-3">
                            {% set total_prices = [] %}
                            {% for cart in carts %}
                                {% set _ = total_prices.append(cart.get_total_price()) %}
                            {% endfor %}
                            <h4 class="text-success">{{ "%.0f"|format(total_prices|sum) }} FCFA</h4>
                            <small class="text-muted">Valeur Total</small>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('shop.browse') }}" class="btn btn-outline-primary">
                                <i class="fas fa-plus me-2"></i>Continuer Shopping
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% else %}
    <!-- Empty Carts -->
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-shopping-cart fa-4x text-muted mb-4"></i>
                <h3>Vos paniers sont vides</h3>
                <p class="text-muted mb-4">Commencez à ajouter des produits à vos paniers pour les voir ici.</p>
                <div class="d-flex gap-3 justify-content-center">
                    <a href="{{ url_for('main.index') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-home me-2"></i>Retour à l'Accueil
                    </a>
                    <a href="{{ url_for('shop.browse') }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-store me-2"></i>Parcourir Boutiques
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Shopping Tips -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-lightbulb me-2"></i>Conseils Shopping
                    </h5>
                    <div class="row">
                        <div class="col-md-4">
                            <h6><i class="fas fa-truck me-2"></i>Livraison</h6>
                            <p class="small">Chaque boutique gère sa propre livraison. Vérifiez les conditions avant de commander.</p>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-shield-alt me-2"></i>Sécurité</h6>
                            <p class="small">Vos paiements sont sécurisés et vos données personnelles protégées.</p>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-star me-2"></i>Avis</h6>
                            <p class="small">Laissez des avis après vos achats pour aider la communauté.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}
</style>
{% endblock %}
