{% extends "base.html" %}

{% block title %}Catégories - Afroly.org{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold">Toutes les Catégories</h1>
        <p class="lead">Explorez nos différentes catégories de produits</p>
    </div>

    <div class="row">
        {% for category in categories %}
        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
            <a href="{{ url_for('main.category_products', slug=category.slug) }}" class="text-decoration-none">
                <div class="card h-100 text-center category-card">
                    <div class="card-body">
                        <i class="{{ category.icon or 'fas fa-tag' }} fa-4x text-primary mb-3"></i>
                        <h5 class="card-title">{{ category.name }}</h5>
                        <p class="card-text text-muted">{{ category.description or 'Explorer ' + category.name }}</p>

                        <!-- Show subcategories if any -->
                        {% if category.children %}
                        <div class="mt-3">
                            <small class="text-muted">Sous-catégories :</small>
                            <div class="mt-1">
                                {% for child in category.children[:3] %}
                                <span class="badge bg-light text-dark me-1">{{ child.name }}</span>
                                {% endfor %}
                                {% if category.children|length > 3 %}
                                <span class="badge bg-secondary">+{{ category.children|length - 3 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    <div class="card-footer bg-transparent">
                        <small class="text-muted">
                            <i class="fas fa-box me-1"></i>
                            Voir les produits
                        </small>
                    </div>
                </div>
            </a>
        </div>
        {% endfor %}
    </div>

    {% if not categories %}
    <div class="text-center py-5">
        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
        <h4>Aucune catégorie disponible</h4>
        <p class="text-muted">Les catégories seront bientôt disponibles.</p>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_head %}
<style>
.category-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.category-card:hover {
    transform: translateY(-5px);
    border-color: var(--bs-primary);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.category-card .card-body {
    padding: 2rem 1rem;
}
</style>
{% endblock %}
