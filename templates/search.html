{% extends "base.html" %}

{% block title %}Recherche - AfroMall{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Search Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h2>Résultats de Recherche</h2>
            {% if query %}
                <p class="text-muted">Recherche pour : "<strong>{{ query }}</strong>"</p>
            {% endif %}
        </div>
    </div>
    
    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <input type="hidden" name="q" value="{{ query }}">
                        
                        <div class="col-md-3">
                            <label for="category" class="form-label">Catégorie</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">Toutes les catégories</option>
                                {% for category in categories %}
                                    <option value="{{ category.id }}" {% if category_id == category.id %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="min_price" class="form-label">Prix Min</label>
                            <input type="number" class="form-control" id="min_price" name="min_price" 
                                   value="{{ min_price or '' }}" placeholder="0" step="0.01">
                        </div>
                        
                        <div class="col-md-2">
                            <label for="max_price" class="form-label">Prix Max</label>
                            <input type="number" class="form-control" id="max_price" name="max_price" 
                                   value="{{ max_price or '' }}" placeholder="1000" step="0.01">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="sort" class="form-label">Trier par</label>
                            <select class="form-select" id="sort" name="sort">
                                <option value="relevance" {% if sort_by == 'relevance' %}selected{% endif %}>Pertinence</option>
                                <option value="newest" {% if sort_by == 'newest' %}selected{% endif %}>Plus récent</option>
                                <option value="price_low" {% if sort_by == 'price_low' %}selected{% endif %}>Prix croissant</option>
                                <option value="price_high" {% if sort_by == 'price_high' %}selected{% endif %}>Prix décroissant</option>
                                <option value="rating" {% if sort_by == 'rating' %}selected{% endif %}>Mieux notés</option>
                            </select>
                        </div>
                        
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-filter me-2"></i>Filtrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Results -->
    <div class="row">
        <!-- Products -->
        <div class="col-md-9">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h4>Produits ({{ products.total }} résultats)</h4>
            </div>
            
            {% if products.items %}
                <div class="row">
                    {% for product in products.items %}
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 product-card">
                            <div class="position-relative">
                                <img src="{{ product.get_main_image() }}" class="card-img-top" alt="{{ product.name }}" 
                                     style="height: 200px; object-fit: cover;">
                                {% if product.is_on_sale() %}
                                <span class="badge bg-danger position-absolute top-0 end-0 m-2">
                                    -{{ product.get_discount_percentage() }}%
                                </span>
                                {% endif %}
                            </div>
                            <div class="card-body d-flex flex-column">
                                <h6 class="card-title">{{ product.name }}</h6>
                                <p class="card-text text-muted small">{{ product.short_description[:100] }}...</p>
                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            {% if product.is_on_sale() %}
                                            <span class="text-decoration-line-through text-muted">{{ "%.2f"|format(product.price) }} €</span>
                                            <span class="fw-bold text-primary">{{ "%.2f"|format(product.sale_price) }} €</span>
                                            {% else %}
                                            <span class="fw-bold text-primary">{{ "%.2f"|format(product.price) }} €</span>
                                            {% endif %}
                                        </div>
                                        <div class="rating">
                                            {% for i in range(5) %}
                                                {% if i < product.get_average_rating() %}
                                                <i class="fas fa-star text-warning"></i>
                                                {% else %}
                                                <i class="far fa-star text-warning"></i>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                    <a href="{{ url_for('product.view', slug=product.slug) }}" class="btn btn-primary btn-sm w-100">
                                        Voir Produit
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Pagination -->
                {% if products.pages > 1 %}
                <nav aria-label="Navigation des pages">
                    <ul class="pagination justify-content-center">
                        {% if products.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.search', q=query, category=category_id, min_price=min_price, max_price=max_price, sort=sort_by, page=products.prev_num) }}">
                                Précédent
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in products.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != products.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('main.search', q=query, category=category_id, min_price=min_price, max_price=max_price, sort=sort_by, page=page_num) }}">
                                        {{ page_num }}
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if products.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.search', q=query, category=category_id, min_price=min_price, max_price=max_price, sort=sort_by, page=products.next_num) }}">
                                Suivant
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4>Aucun produit trouvé</h4>
                    <p class="text-muted">Essayez de modifier vos critères de recherche.</p>
                </div>
            {% endif %}
        </div>
        
        <!-- Shops Sidebar -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5>Boutiques Correspondantes</h5>
                </div>
                <div class="card-body">
                    {% if shops %}
                        {% for shop in shops %}
                        <div class="d-flex align-items-center mb-3">
                            {% if shop.logo %}
                            <img src="/static/uploads/shops/{{ shop.logo }}" alt="{{ shop.name }}" 
                                 class="rounded-circle me-3" style="width: 40px; height: 40px; object-fit: cover;">
                            {% else %}
                            <div class="bg-primary rounded-circle me-3 d-flex align-items-center justify-content-center" 
                                 style="width: 40px; height: 40px;">
                                <i class="fas fa-store text-white"></i>
                            </div>
                            {% endif %}
                            <div class="flex-grow-1">
                                <h6 class="mb-0">
                                    <a href="{{ url_for('shop.view', slug=shop.slug) }}" class="text-decoration-none">
                                        {{ shop.name }}
                                    </a>
                                </h6>
                                <small class="text-muted">{{ shop.get_active_products_count() }} produits</small>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">Aucune boutique trouvée.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
