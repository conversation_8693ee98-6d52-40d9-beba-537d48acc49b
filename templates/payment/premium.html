{% extends "base.html" %}

{% block title %}Paiement Premium - Afroly.org{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- NOUVEAU Alert - Persistent -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-warning alert-dismissible fade show" role="alert" style="position: sticky; top: 70px; z-index: 1020;">
                <div class="d-flex align-items-center">
                    <i class="fas fa-gift fa-2x me-3"></i>
                    <div>
                        <h5 class="alert-heading mb-1">
                            <strong>NOUVEAU :</strong> Crédits de publicité GRATUITS inclus dans chaque plan !
                        </h5>
                        <p class="mb-0">
                            Gratuit: 100 crédits • Premium: 500 crédits • Gold: 2000 crédits
                        </p>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Plan Summary -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white text-center">
                    <h3 class="mb-0"><i class="fas fa-crown me-2"></i>Plan Premium</h3>
                </div>
                <div class="card-body text-center">
                    <h4 class="text-primary mb-3">Choisissez votre période d'abonnement</h4>

                    <!-- Billing Toggle -->
                    <div class="d-flex justify-content-center align-items-center mb-4">
                        <span class="me-3 fw-semibold">6 mois</span>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="billingToggle" onchange="toggleBilling()">
                            <label class="form-check-label" for="billingToggle"></label>
                        </div>
                        <span class="ms-3 fw-semibold">12 mois <span class="badge bg-success ms-1">-20%</span></span>
                    </div>

                    <!-- Pricing Display -->
                    <div class="pricing-summary mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="price-card p-3 border rounded" id="sixMonthCard">
                                    <h5>6 Mois</h5>
                                    <div class="price-display">
                                        <span class="h3 text-primary">€24</span>
                                        <span class="text-muted">(15,720 FCFA)</span>
                                    </div>
                                    <p class="text-muted">€4/mois (2,620 FCFA/mois)</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="price-card p-3 border rounded position-relative" id="twelveMonthCard">
                                    <span class="badge bg-success position-absolute top-0 end-0 m-2">Économie</span>
                                    <h5>12 Mois</h5>
                                    <div class="price-display">
                                        <span class="h3 text-success">€38</span>
                                        <span class="text-muted">(24,890 FCFA)</span>
                                    </div>
                                    <p class="text-muted">€3.17/mois (2,074 FCFA/mois)</p>
                                    <small class="text-success">Économisez €10 (6,550 FCFA)</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Selected Plan Display -->
                    <div class="selected-plan-info p-3 bg-light rounded mb-4">
                        <h6>Plan sélectionné:</h6>
                        <div id="selectedPlanInfo">
                            <strong>Premium - 6 mois</strong><br>
                            <span class="text-primary">€24 (15,720 FCFA)</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Methods -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Choisissez votre méthode de paiement</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <!-- Orange Money Cameroun -->
                        <div class="col-md-6">
                            <div class="payment-method-card p-3 border rounded h-100" data-method="orange">
                                <div class="text-center">
                                    <div class="payment-icon mb-3">
                                        <i class="fas fa-mobile-alt fa-3x text-warning"></i>
                                    </div>
                                    <h6>Orange Money Cameroun</h6>
                                    <p class="text-muted small">Paiement mobile sécurisé</p>
                                    <div class="price-display">
                                        <span class="fw-bold" id="orangePrice">15,720 FCFA</span>
                                    </div>
                                    <div class="mt-2 p-2 bg-light rounded">
                                        <small class="text-dark">
                                            <i class="fas fa-phone me-1"></i>
                                            <strong id="orangeNumber">+237 6 92 85 73 08</strong>
                                            <button class="btn btn-sm btn-outline-secondary ms-1" onclick="copyToClipboard('orangeNumber')" title="Copier le numéro">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </small>
                                        <br>
                                        <small class="text-muted">Envoyez le montant à ce numéro</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- MTN Money Cameroun -->
                        <div class="col-md-6">
                            <div class="payment-method-card p-3 border rounded h-100" data-method="mtn">
                                <div class="text-center">
                                    <div class="payment-icon mb-3">
                                        <i class="fas fa-mobile-alt fa-3x text-primary"></i>
                                    </div>
                                    <h6>MTN Money Cameroun</h6>
                                    <p class="text-muted small">Paiement mobile sécurisé</p>
                                    <div class="price-display">
                                        <span class="fw-bold" id="mtnPrice">15,720 FCFA</span>
                                    </div>
                                    <div class="mt-2 p-2 bg-light rounded">
                                        <small class="text-dark">
                                            <i class="fas fa-phone me-1"></i>
                                            <strong id="mtnNumber">+237 6 72 02 75 04</strong>
                                            <button class="btn btn-sm btn-outline-secondary ms-1" onclick="copyToClipboard('mtnNumber')" title="Copier le numéro">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </small>
                                        <br>
                                        <small class="text-muted">Envoyez le montant à ce numéro</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- PayPal -->
                        <div class="col-md-6">
                            <div class="payment-method-card p-3 border rounded h-100" data-method="paypal">
                                <div class="text-center">
                                    <div class="payment-icon mb-3">
                                        <i class="fab fa-paypal fa-3x text-info"></i>
                                    </div>
                                    <h6>PayPal</h6>
                                    <p class="text-muted small">Paiement international sécurisé</p>
                                    <div class="price-display">
                                        <span class="fw-bold" id="paypalPrice">€24</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Stripe -->
                        <div class="col-md-6">
                            <div class="payment-method-card p-3 border rounded h-100" data-method="stripe">
                                <div class="text-center">
                                    <div class="payment-icon mb-3">
                                        <i class="fas fa-credit-card fa-3x text-success"></i>
                                    </div>
                                    <h6>Carte Bancaire (Stripe)</h6>
                                    <p class="text-muted small">Visa, Mastercard, etc.</p>
                                    <div class="price-display">
                                        <span class="fw-bold" id="stripePrice">€24</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Instructions -->
                    <div class="alert alert-info mt-4">
                        <h6><i class="fas fa-info-circle me-2"></i>Instructions de Paiement Mobile</h6>
                        <ol class="mb-0 small">
                            <li>Sélectionnez votre méthode de paiement (Orange Money ou MTN Money)</li>
                            <li>Envoyez le montant exact au numéro indiqué</li>
                            <li>Conservez le SMS de confirmation de transaction</li>
                            <li>Cliquez sur "Confirmer le Paiement" ci-dessous</li>
                            <li>Votre compte sera activé dans les 24h après vérification</li>
                        </ol>
                    </div>

                    <!-- Payment Button -->
                    <div class="text-center mt-4">
                        <button class="btn btn-primary btn-lg px-5" id="proceedPayment" disabled>
                            <i class="fas fa-lock me-2"></i>Confirmer le Paiement
                        </button>
                        <p class="text-muted mt-2 small">
                            <i class="fas fa-shield-alt me-1"></i>Paiement 100% sécurisé et crypté
                        </p>
                    </div>
                </div>
            </div>

            <!-- Features Reminder -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="text-center mb-3">
                        <i class="fas fa-crown me-2"></i>Ce que vous obtenez avec Premium:
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i><strong>2 boutiques</strong></li>
                                <li><i class="fas fa-check text-success me-2"></i><strong>30 produits</strong></li>
                                <li><i class="fas fa-check text-success me-2"></i><strong>500 crédits publicité GRATUITS</strong></li>
                                <li><i class="fas fa-check text-success me-2"></i>Statistiques boutique</li>
                                <li><i class="fas fa-check text-success me-2"></i>Statistiques visiteurs</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Support prioritaire</li>
                                <li><i class="fas fa-check text-success me-2"></i>Label Premium</li>
                                <li><i class="fas fa-check text-success me-2"></i>IA réécriture produits</li>
                                <li><i class="fas fa-check text-success me-2"></i>Dropshipping basique</li>
                                <li><i class="fas fa-check text-success me-2"></i>0% commission</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.price-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.price-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.price-card.selected {
    border-color: #007bff !important;
    background-color: #f8f9ff;
}

.payment-method-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.payment-method-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.payment-method-card.selected {
    border-color: #28a745 !important;
    background-color: #f8fff8;
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}
</style>

<script>
let selectedPeriod = '6months';
let selectedMethod = null;

const prices = {
    '6months': {
        euro: 24,
        fcfa: 15720,
        monthly_euro: 4,
        monthly_fcfa: 2620
    },
    '12months': {
        euro: 38,
        fcfa: 24890,
        monthly_euro: 3.17,
        monthly_fcfa: 2074
    }
};

function toggleBilling() {
    const toggle = document.getElementById('billingToggle');
    selectedPeriod = toggle.checked ? '12months' : '6months';
    updatePricing();
    updateSelectedPlan();
}

function updatePricing() {
    const price = prices[selectedPeriod];

    // Update payment method prices
    document.getElementById('orangePrice').textContent = price.fcfa.toLocaleString() + ' FCFA';
    document.getElementById('mtnPrice').textContent = price.fcfa.toLocaleString() + ' FCFA';
    document.getElementById('paypalPrice').textContent = '€' + price.euro;
    document.getElementById('stripePrice').textContent = '€' + price.euro;
}

function updateSelectedPlan() {
    const price = prices[selectedPeriod];
    const period = selectedPeriod === '6months' ? '6 mois' : '12 mois';

    document.getElementById('selectedPlanInfo').innerHTML = `
        <strong>Premium - ${period}</strong><br>
        <span class="text-primary">€${price.euro} (${price.fcfa.toLocaleString()} FCFA)</span>
    `;
}

// Payment method selection
document.querySelectorAll('.payment-method-card').forEach(card => {
    card.addEventListener('click', function() {
        // Remove previous selection
        document.querySelectorAll('.payment-method-card').forEach(c => c.classList.remove('selected'));

        // Add selection to clicked card
        this.classList.add('selected');
        selectedMethod = this.dataset.method;

        // Enable payment button
        document.getElementById('proceedPayment').disabled = false;
    });
});

// Proceed to payment
document.getElementById('proceedPayment').addEventListener('click', function() {
    if (!selectedMethod) {
        alert('Veuillez sélectionner une méthode de paiement');
        return;
    }

    const price = prices[selectedPeriod];
    const period = selectedPeriod === '6months' ? '6 mois' : '12 mois';

    // Here you would integrate with actual payment processors
    alert(`Redirection vers le paiement ${selectedMethod}\nPlan: Premium ${period}\nMontant: €${price.euro} (${price.fcfa.toLocaleString()} FCFA)`);
});

// Initialize
updatePricing();

// Copy to clipboard functionality
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    const text = element.textContent;

    navigator.clipboard.writeText(text).then(function() {
        // Show success feedback
        const button = element.nextElementSibling;
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check text-success"></i>';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');

        // Show toast notification
        showToast('Numéro copié!', 'Le numéro de téléphone a été copié dans le presse-papiers.', 'success');

        // Reset button after 2 seconds
        setTimeout(() => {
            button.innerHTML = originalIcon;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    }).catch(function(err) {
        console.error('Erreur lors de la copie: ', err);
        showToast('Erreur', 'Impossible de copier le numéro. Veuillez le sélectionner manuellement.', 'error');
    });
}

// Toast notification function
function showToast(title, message, type = 'info') {
    const toastContainer = document.getElementById('toastContainer') || createToastContainer();

    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'primary'} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <strong>${title}</strong><br>${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    toastContainer.appendChild(toast);

    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // Remove toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toastContainer';
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
}
</script>
{% endblock %}
