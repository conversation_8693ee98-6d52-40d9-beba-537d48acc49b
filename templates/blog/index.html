{% extends "base.html" %}

{% block title %}Blog - Afroly.org{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Header -->
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 fw-bold text-primary">
                <i class="fas fa-blog me-3"></i>Blog Afroly.org
            </h1>
            <p class="lead text-muted">Découvrez les dernières actualités, conseils et tendances du e-commerce africain</p>
        </div>
    </div>

    <!-- Featured Posts -->
    {% if featured_posts %}
    <div class="row mb-5">
        <div class="col-12">
            <h2 class="mb-4">Articles en Vedette</h2>
            <div class="row">
                {% for post in featured_posts %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 shadow-sm">
                        {% if post.featured_image %}
                        <img src="{{ post.featured_image }}" class="card-img-top" alt="{{ post.title }}" style="height: 200px; object-fit: cover;">
                        {% else %}
                        <div class="card-img-top bg-primary d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="fas fa-newspaper fa-3x text-white"></i>
                        </div>
                        {% endif %}
                        <div class="card-body d-flex flex-column">
                            <div class="mb-2">
                                {% if post.category %}
                                <span class="badge bg-secondary">{{ post.category }}</span>
                                {% endif %}
                                <small class="text-muted ms-2">{{ post.published_at.strftime('%d %B %Y') }}</small>
                            </div>
                            <h5 class="card-title">{{ post.title }}</h5>
                            <p class="card-text">{{ post.excerpt or (post.content[:150] + '...') }}</p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>{{ post.author.get_full_name() }}
                                    </small>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>{{ post.get_reading_time() }} min
                                    </small>
                                </div>
                                <a href="{{ url_for('blog.view_post', slug=post.slug) }}" class="btn btn-primary btn-sm mt-2">Lire la suite</a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Category Filter -->
            {% if categories %}
            <div class="mb-4">
                <h6>Filtrer par catégorie:</h6>
                <div class="d-flex flex-wrap gap-2">
                    <a href="{{ url_for('blog.index') }}" class="btn btn-sm {{ 'btn-primary' if not current_category else 'btn-outline-primary' }}">
                        Toutes
                    </a>
                    {% for category in categories %}
                    <a href="{{ url_for('blog.index', category=category) }}"
                       class="btn btn-sm {{ 'btn-primary' if current_category == category else 'btn-outline-primary' }}">
                        {{ category }}
                    </a>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Blog Posts -->
            {% if posts.items %}
            <div class="row">
                {% for post in posts.items %}
                <div class="col-12 mb-4">
                    <div class="card">
                        <div class="row g-0">
                            <div class="col-md-4">
                                {% if post.featured_image %}
                                <img src="{{ post.featured_image }}" class="img-fluid rounded-start h-100" alt="{{ post.title }}" style="object-fit: cover;">
                                {% else %}
                                <div class="bg-light d-flex align-items-center justify-content-center h-100 rounded-start">
                                    <i class="fas fa-newspaper fa-2x text-muted"></i>
                                </div>
                                {% endif %}
                            </div>
                            <div class="col-md-8">
                                <div class="card-body">
                                    <div class="mb-2">
                                        {% if post.category %}
                                        <span class="badge bg-secondary">{{ post.category }}</span>
                                        {% endif %}
                                        <small class="text-muted ms-2">{{ post.published_at.strftime('%d %B %Y') }}</small>
                                    </div>
                                    <h5 class="card-title">{{ post.title }}</h5>
                                    <p class="card-text">{{ post.excerpt or (post.content[:200] + '...') }}</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <small class="text-muted">
                                                <i class="fas fa-user me-1"></i>{{ post.author.get_full_name() }}
                                            </small>
                                            <small class="text-muted ms-3">
                                                <i class="fas fa-eye me-1"></i>{{ post.views }} vues
                                            </small>
                                            <small class="text-muted ms-3">
                                                <i class="fas fa-clock me-1"></i>{{ post.get_reading_time() }} min
                                            </small>
                                        </div>
                                        <div class="d-flex gap-2">
                                            <a href="{{ url_for('blog.view_post', slug=post.slug) }}" class="btn btn-primary btn-sm">Lire</a>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-outline-secondary btn-sm" onclick="sharePost('{{ url_for('blog.view_post', slug=post.slug, _external=True) }}', '{{ post.title }}')" title="Partager">
                                                    <i class="fas fa-share-alt"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if posts.pages > 1 %}
            <nav aria-label="Blog pagination">
                <ul class="pagination justify-content-center">
                    {% if posts.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('blog.index', page=posts.prev_num, category=current_category) }}">Précédent</a>
                    </li>
                    {% endif %}

                    {% for page_num in posts.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != posts.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('blog.index', page=page_num, category=current_category) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if posts.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('blog.index', page=posts.next_num, category=current_category) }}">Suivant</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-newspaper fa-4x text-muted mb-3"></i>
                <h4>Aucun article trouvé</h4>
                <p class="text-muted">Il n'y a pas encore d'articles dans cette catégorie.</p>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <div class="sticky-top" style="top: 2rem;">
                <!-- About Blog -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">À propos du Blog</h6>
                    </div>
                    <div class="card-body">
                        <p class="card-text">Découvrez les dernières tendances du e-commerce africain, des conseils pour développer votre business et des success stories inspirantes.</p>
                    </div>
                </div>

                <!-- Recent Posts -->
                {% if posts.items %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">Articles Récents</h6>
                    </div>
                    <div class="card-body">
                        {% for post in posts.items[:5] %}
                        <div class="d-flex mb-3">
                            <div class="flex-shrink-0">
                                {% if post.featured_image %}
                                <img src="{{ post.featured_image }}" alt="{{ post.title }}" class="rounded" style="width: 60px; height: 60px; object-fit: cover;">
                                {% else %}
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                    <i class="fas fa-newspaper text-muted"></i>
                                </div>
                                {% endif %}
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">
                                    <a href="{{ url_for('blog.view_post', slug=post.slug) }}" class="text-decoration-none">{{ post.title[:50] }}{% if post.title|length > 50 %}...{% endif %}</a>
                                </h6>
                                <small class="text-muted">{{ post.published_at.strftime('%d %B %Y') }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Categories -->
                {% if categories %}
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Catégories</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex flex-wrap gap-2">
                            {% for category in categories %}
                            <a href="{{ url_for('blog.index', category=category) }}" class="badge bg-secondary text-decoration-none">{{ category }}</a>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
function sharePost(url, title) {
    // Check if Web Share API is supported
    if (navigator.share) {
        navigator.share({
            title: title,
            url: url
        }).catch(console.error);
    } else {
        // Fallback to WhatsApp share (most popular in Africa)
        window.open(`https://wa.me/?text=${encodeURIComponent(title + ' ' + url)}`, '_blank');
    }
}
</script>
{% endblock %}
