{% extends "base.html" %}

{% block title %}Gestion du Blog - Admin Afroly.org{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-5 fw-bold">
                <i class="fas fa-blog me-3 text-primary"></i>Gestion du Blog
            </h1>
            <p class="lead text-muted">G<PERSON>rez les articles de blog et le contenu</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group" role="group">
                <a href="{{ url_for('blog.admin_create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Nouvel Article
                </a>
                <a href="{{ url_for('blog.admin_ai_generate') }}" class="btn btn-success">
                    <i class="fas fa-magic me-2"></i>Générer avec IA
                </a>
                <a href="{{ url_for('blog.admin_bulk_import') }}" class="btn btn-info">
                    <i class="fas fa-upload me-2"></i>Import CSV
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Articles</h6>
                            <h3 class="mb-0">{{ stats.total_posts }}</h3>
                        </div>
                        <div>
                            <i class="fas fa-newspaper fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Publiés</h6>
                            <h3 class="mb-0">{{ stats.published_posts }}</h3>
                        </div>
                        <div>
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Brouillons</h6>
                            <h3 class="mb-0">{{ stats.draft_posts }}</h3>
                        </div>
                        <div>
                            <i class="fas fa-edit fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Vues</h6>
                            <h3 class="mb-0">{{ stats.total_views }}</h3>
                        </div>
                        <div>
                            <i class="fas fa-eye fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Posts Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Articles de Blog</h5>
                </div>
                <div class="card-body">
                    {% if posts.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Titre</th>
                                    <th>Auteur</th>
                                    <th>Catégorie</th>
                                    <th>Statut</th>
                                    <th>Vues</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for post in posts.items %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if post.featured_image %}
                                            <img src="{{ post.featured_image }}" class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                            {% else %}
                                            <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                <i class="fas fa-newspaper text-muted"></i>
                                            </div>
                                            {% endif %}
                                            <div>
                                                <strong>{{ post.title[:50] }}{% if post.title|length > 50 %}...{% endif %}</strong>
                                                {% if post.is_featured %}
                                                <span class="badge bg-warning ms-1">Vedette</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ post.author.get_full_name() }}</td>
                                    <td>
                                        {% if post.category %}
                                        <span class="badge bg-secondary">{{ post.category }}</span>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if post.is_published %}
                                        <span class="badge bg-success">Publié</span>
                                        {% else %}
                                        <span class="badge bg-warning">Brouillon</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ post.views }}</td>
                                    <td>{{ post.created_at.strftime('%d/%m/%Y') }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            {% if post.is_published %}
                                            <a href="{{ url_for('blog.view_post', slug=post.slug) }}" class="btn btn-outline-primary" target="_blank" title="Voir">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% endif %}
                                            <a href="{{ url_for('blog.admin_edit', post_id=post.id) }}" class="btn btn-outline-warning" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-outline-danger" onclick="deletePost({{ post.id }})" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if posts.pages > 1 %}
                    <nav aria-label="Posts pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if posts.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('blog.admin_index', page=posts.prev_num) }}">Précédent</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in posts.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != posts.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('blog.admin_index', page=page_num) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if posts.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('blog.admin_index', page=posts.next_num) }}">Suivant</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-newspaper fa-4x text-muted mb-3"></i>
                        <h4>Aucun article</h4>
                        <p class="text-muted">Commencez par créer votre premier article de blog.</p>
                        <a href="{{ url_for('blog.admin_create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Créer un Article
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deletePost(postId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cet article ? Cette action est irréversible.')) {
        fetch(`/blog/admin/delete/${postId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Erreur lors de la suppression de l\'article');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur lors de la suppression de l\'article');
        });
    }
}
</script>
{% endblock %}
