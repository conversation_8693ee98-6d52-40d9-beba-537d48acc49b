{% extends "base.html" %}

{% block title %}{{ post.title }} - Blog Afroly.org{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Accueil</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('blog.index') }}">Blog</a></li>
                    {% if post.category %}
                    <li class="breadcrumb-item"><a href="{{ url_for('blog.index', category=post.category) }}">{{ post.category }}</a></li>
                    {% endif %}
                    <li class="breadcrumb-item active" aria-current="page">{{ post.title[:50] }}{% if post.title|length > 50 %}...{% endif %}</li>
                </ol>
            </nav>

            <!-- Article -->
            <article class="card">
                <!-- Featured Image -->
                {% if post.featured_image %}
                <img src="{{ post.featured_image }}" class="card-img-top" alt="{{ post.title }}" style="height: 300px; object-fit: cover;">
                {% endif %}

                <div class="card-body">
                    <!-- Article Meta -->
                    <div class="mb-3">
                        {% if post.category %}
                        <span class="badge bg-primary">{{ post.category }}</span>
                        {% endif %}
                        <small class="text-muted ms-2">
                            <i class="fas fa-calendar me-1"></i>{{ post.published_at.strftime('%d %B %Y') }}
                        </small>
                        <small class="text-muted ms-3">
                            <i class="fas fa-user me-1"></i>{{ post.author.get_full_name() }}
                        </small>
                        <small class="text-muted ms-3">
                            <i class="fas fa-eye me-1"></i>{{ post.views }} vues
                        </small>
                        <small class="text-muted ms-3">
                            <i class="fas fa-clock me-1"></i>{{ post.get_reading_time() }} min de lecture
                        </small>
                    </div>

                    <!-- Article Title -->
                    <h1 class="card-title mb-4">{{ post.title }}</h1>

                    <!-- Article Content -->
                    <div class="article-content">
                        {{ post.content|nl2br|safe }}
                    </div>

                    <!-- Tags -->
                    {% if post.tags %}
                    <div class="mt-4 pt-3 border-top">
                        <h6>Tags:</h6>
                        <div class="d-flex flex-wrap gap-2">
                            {% for tag in post.get_tags_list() %}
                            <span class="badge bg-secondary">{{ tag }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Share Buttons -->
                    <div class="mt-4 pt-3 border-top">
                        <h6>Partager cet article:</h6>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="shareOnFacebook('{{ request.url }}', '{{ post.title }}')">
                                <i class="fab fa-facebook me-1"></i>Facebook
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="shareOnTwitter('{{ request.url }}', '{{ post.title }}')">
                                <i class="fab fa-twitter me-1"></i>Twitter
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="shareOnWhatsApp('{{ request.url }}', '{{ post.title }}')">
                                <i class="fab fa-whatsapp me-1"></i>WhatsApp
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="shareOnTelegram('{{ request.url }}', '{{ post.title }}')">
                                <i class="fab fa-telegram me-1"></i>Telegram
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="copyToClipboard('{{ request.url }}')">
                                <i class="fas fa-link me-1"></i>Copier le lien
                            </button>
                        </div>
                    </div>
                </div>
            </article>

            <!-- Related Posts -->
            {% if related_posts %}
            <div class="mt-5">
                <h3 class="mb-4">Articles Similaires</h3>
                <div class="row">
                    {% for related_post in related_posts %}
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            {% if related_post.featured_image %}
                            <img src="{{ related_post.featured_image }}" class="card-img-top" alt="{{ related_post.title }}" style="height: 150px; object-fit: cover;">
                            {% else %}
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 150px;">
                                <i class="fas fa-newspaper fa-2x text-muted"></i>
                            </div>
                            {% endif %}
                            <div class="card-body d-flex flex-column">
                                <h6 class="card-title">{{ related_post.title }}</h6>
                                <p class="card-text small">{{ related_post.excerpt or (related_post.content[:100] + '...') }}</p>
                                <div class="mt-auto">
                                    <small class="text-muted">{{ related_post.published_at.strftime('%d %B %Y') }}</small>
                                    <a href="{{ url_for('blog.view_post', slug=related_post.slug) }}" class="btn btn-primary btn-sm mt-2 w-100">Lire</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <div class="sticky-top" style="top: 2rem;">
                <!-- Author Info -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">À propos de l'auteur</h6>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <div class="bg-primary rounded-circle mx-auto d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                <i class="fas fa-user fa-2x text-white"></i>
                            </div>
                        </div>
                        <h6>{{ post.author.get_full_name() }}</h6>
                        <p class="text-muted small">Auteur chez Afroly.org</p>
                    </div>
                </div>

                <!-- Recent Posts -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">Articles Récents</h6>
                    </div>
                    <div class="card-body">
                        {% for recent_post in related_posts %}
                        <div class="d-flex mb-3">
                            <div class="flex-shrink-0">
                                {% if recent_post.featured_image %}
                                <img src="{{ recent_post.featured_image }}" alt="{{ recent_post.title }}" class="rounded" style="width: 50px; height: 50px; object-fit: cover;">
                                {% else %}
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                    <i class="fas fa-newspaper text-muted"></i>
                                </div>
                                {% endif %}
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1 small">
                                    <a href="{{ url_for('blog.view_post', slug=recent_post.slug) }}" class="text-decoration-none">{{ recent_post.title[:40] }}{% if recent_post.title|length > 40 %}...{% endif %}</a>
                                </h6>
                                <small class="text-muted">{{ recent_post.published_at.strftime('%d %B') }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Back to Blog -->
                <div class="card">
                    <div class="card-body text-center">
                        <a href="{{ url_for('blog.index') }}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>Retour au Blog
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Share functions
function shareOnFacebook(url, title) {
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank', 'width=600,height=400');
}

function shareOnTwitter(url, title) {
    window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`, '_blank', 'width=600,height=400');
}

function shareOnWhatsApp(url, title) {
    window.open(`https://wa.me/?text=${encodeURIComponent(title + ' ' + url)}`, '_blank');
}

function shareOnTelegram(url, title) {
    window.open(`https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`, '_blank');
}

function copyToClipboard(url) {
    navigator.clipboard.writeText(url).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'alert alert-success alert-dismissible fade show position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
        toast.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            Lien copié dans le presse-papiers!
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(toast);

        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    });
}
</script>
{% endblock %}

{% block extra_head %}
<style>
.article-content {
    line-height: 1.8;
    font-size: 1.1rem;
}

.article-content p {
    margin-bottom: 1.5rem;
}

.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5,
.article-content h6 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: #333;
}

.article-content ul,
.article-content ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.article-content blockquote {
    border-left: 4px solid #007bff;
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    color: #666;
}
</style>
{% endblock %}
