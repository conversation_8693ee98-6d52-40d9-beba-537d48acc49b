#!/usr/bin/env python3

import sqlite3
import json

def fix_all_shop_images():
    """Fix images for all shops"""
    try:
        # Connect to database
        conn = sqlite3.connect('instance/afroly.db')
        cursor = conn.cursor()

        # Get all shops
        cursor.execute("SELECT id, name, slug FROM shop")
        shops = cursor.fetchall()

        print(f"🏪 Found {len(shops)} shops")

        # Image sets for different shop types
        image_sets = {
            'mode-africaine-elegante': [
                'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500&h=600&fit=crop',
                'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=500&h=600&fit=crop',
                'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=500&h=600&fit=crop',
                'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=500&h=600&fit=crop',
                'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=600&fit=crop'
            ],
            'techafrika-solutions': [
                'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=500&h=600&fit=crop',
                'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=500&h=600&fit=crop',
                'https://images.unsplash.com/photo-1542393545-10f5cde2c810?w=500&h=600&fit=crop'
            ],
            'artisanat-traditionnel-senegal': [
                'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=500&h=600&fit=crop',
                'https://images.unsplash.com/photo-1573408301185-9146fe634ad0?w=500&h=600&fit=crop',
                'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=500&h=600&fit=crop'
            ]
        }

        # Process each shop
        for shop_id, shop_name, shop_slug in shops:
            print(f"\n🏪 Processing: {shop_name} ({shop_slug})")

            # Get products for this shop
            cursor.execute("SELECT id, name, images FROM product WHERE shop_id = ?", (shop_id,))
            products = cursor.fetchall()

            if not products:
                print(f"   No products found")
                continue

            print(f"   Found {len(products)} products")

            # Get appropriate image set
            images_to_use = image_sets.get(shop_slug, image_sets['mode-africaine-elegante'])

            # Update each product
            updated = 0
            for i, (product_id, name, current_images) in enumerate(products):
                try:
                    # Parse current images
                    if current_images:
                        current_img_list = json.loads(current_images)
                    else:
                        current_img_list = []

                    # Check if needs update (has no-image.png or empty)
                    needs_update = (
                        not current_img_list or
                        any('/static/images/no-image.png' in str(img) for img in current_img_list) or
                        any('no-image' in str(img) for img in current_img_list)
                    )

                    if needs_update:
                        # Assign new image
                        new_image = images_to_use[i % len(images_to_use)]
                        new_images = [new_image]
                        new_images_json = json.dumps(new_images)

                        # Update in database
                        cursor.execute("UPDATE product SET images = ? WHERE id = ?", (new_images_json, product_id))
                        print(f"   ✅ Updated: {name}")
                        updated += 1
                    else:
                        print(f"   ⏭️  Skipped: {name} (already has good images)")

                except Exception as e:
                    print(f"   ❌ Error updating {name}: {e}")

            print(f"   📊 Updated {updated}/{len(products)} products")

        # Commit all changes
        conn.commit()
        print(f"\n🎉 Successfully processed all shops!")

        # Final verification
        print(f"\n📊 Final Verification:")
        for shop_id, shop_name, shop_slug in shops:
            cursor.execute("SELECT COUNT(*) FROM product WHERE shop_id = ? AND images LIKE '%no-image%'", (shop_id,))
            broken_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM product WHERE shop_id = ?", (shop_id,))
            total_count = cursor.fetchone()[0]

            status = "✅" if broken_count == 0 else "⚠️"
            print(f"   {status} {shop_name}: {total_count - broken_count}/{total_count} products with good images")

        conn.close()

    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == '__main__':
    fix_all_shop_images()
