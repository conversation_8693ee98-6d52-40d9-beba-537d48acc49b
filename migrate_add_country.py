#!/usr/bin/env python3
"""
Migration script to add country field to existing shops
"""

from app import create_app
from models import db, Shop
from config import Config

def migrate_add_country():
    """Add country field to existing shops and set default values"""
    app = create_app()

    with app.app_context():
        try:
            # Check if country column exists
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            columns = [column['name'] for column in inspector.get_columns('shop')]

            if 'country' not in columns:
                print("Adding country column to shop table...")
                # Add the column using text() for raw SQL
                from sqlalchemy import text
                with db.engine.connect() as conn:
                    conn.execute(text('ALTER TABLE shop ADD COLUMN country VARCHAR(100) DEFAULT "Sénégal"'))
                    conn.commit()
                print("✅ Country column added successfully!")
            else:
                print("Country column already exists.")

            # Update existing shops without country
            shops_without_country = Shop.query.filter(
                (Shop.country == None) | (Shop.country == '')
            ).all()

            if shops_without_country:
                print(f"Updating {len(shops_without_country)} shops without country...")

                # Set default countries based on shop names or addresses
                country_mapping = {
                    'sénégal': 'Sénégal',
                    'senegal': 'Sénégal',
                    'dakar': 'Sénégal',
                    'ghana': 'Ghana',
                    'accra': 'Ghana',
                    'nigeria': 'Nigéria',
                    'lagos': 'Nigéria',
                    'abuja': 'Nigéria',
                    'côte d\'ivoire': 'Côte d\'Ivoire',
                    'abidjan': 'Côte d\'Ivoire',
                    'mali': 'Mali',
                    'bamako': 'Mali',
                    'burkina': 'Burkina Faso',
                    'ouagadougou': 'Burkina Faso',
                    'cameroun': 'Cameroun',
                    'yaoundé': 'Cameroun',
                    'douala': 'Cameroun',
                    'maroc': 'Maroc',
                    'casablanca': 'Maroc',
                    'rabat': 'Maroc',
                    'tunisie': 'Tunisie',
                    'tunis': 'Tunisie',
                    'algérie': 'Algérie',
                    'alger': 'Algérie',
                    'égypte': 'Égypte',
                    'le caire': 'Égypte',
                    'kenya': 'Kenya',
                    'nairobi': 'Kenya',
                    'tanzanie': 'Tanzanie',
                    'dar es salaam': 'Tanzanie',
                    'ouganda': 'Ouganda',
                    'kampala': 'Ouganda',
                    'rwanda': 'Rwanda',
                    'kigali': 'Rwanda',
                    'éthiopie': 'Éthiopie',
                    'addis abeba': 'Éthiopie',
                    'afrique du sud': 'Afrique du Sud',
                    'johannesburg': 'Afrique du Sud',
                    'cape town': 'Afrique du Sud',
                    'le cap': 'Afrique du Sud'
                }

                for shop in shops_without_country:
                    # Try to determine country from shop name, description, or address
                    country = 'Sénégal'  # Default

                    search_text = f"{shop.name} {shop.description or ''} {shop.address or ''}".lower()

                    for keyword, detected_country in country_mapping.items():
                        if keyword in search_text:
                            country = detected_country
                            break

                    shop.country = country
                    print(f"  - {shop.name}: {country}")

                db.session.commit()
                print(f"✅ Updated {len(shops_without_country)} shops with country information!")
            else:
                print("All shops already have country information.")

            # Display statistics
            print("\n📊 Country Statistics:")
            from sqlalchemy import func
            country_stats = db.session.query(
                Shop.country,
                func.count(Shop.id).label('count')
            ).group_by(Shop.country).order_by(func.count(Shop.id).desc()).all()

            for country, count in country_stats:
                print(f"  - {country}: {count} boutique(s)")

            print(f"\n✅ Migration completed successfully!")
            print(f"Total shops: {Shop.query.count()}")

        except Exception as e:
            print(f"❌ Error during migration: {e}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    migrate_add_country()
