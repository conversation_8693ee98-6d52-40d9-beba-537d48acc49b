#!/usr/bin/env python3
"""
Script to fix blog articles with empty titles
"""

from app import create_app
from models import db, BlogPost
from utils.helpers import generate_slug
from datetime import datetime

def fix_blog_articles():
    """Fix blog articles with empty titles"""
    app = create_app()
    
    with app.app_context():
        # Find articles with empty or very short titles
        articles_to_fix = BlogPost.query.filter(
            (BlogPost.title == '') | (BlogPost.title.is_(None)) | (db.func.length(BlogPost.title) < 5)
        ).all()
        
        print(f"🔧 Trouvé {len(articles_to_fix)} articles à corriger...")
        
        # Predefined titles for French African countries
        backup_titles = [
            "Les Tendances du Shopping Digital au Cameroun en 2024",
            "Guide Complet pour Vendre en Ligne au Mali",
            "Stratégies Marketing Digital pour les Entreprises Togolaises",
            "Le Marché en Ligne au Niger : Potentiel et Perspectives",
            "Les Méthodes de Paiement Mobile en Afrique de l'Ouest",
            "Comment Créer une Boutique en Ligne Rentable en Guinée",
            "L'Innovation Fintech en Afrique Francophone",
            "Développer son E-commerce au Gabon",
            "Les Opportunités du Digital au Tchad",
            "Commerce Électronique en République Centrafricaine"
        ]
        
        for i, article in enumerate(articles_to_fix):
            try:
                # Use backup title
                new_title = backup_titles[i % len(backup_titles)]
                
                # Generate new slug
                new_slug = generate_slug(new_title)
                
                # Make sure slug is unique
                existing = BlogPost.query.filter_by(slug=new_slug).first()
                if existing and existing.id != article.id:
                    new_slug = f"{new_slug}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
                
                # Update article
                article.title = new_title
                article.slug = new_slug
                
                # If content is also empty, add some basic content
                if not article.content or len(article.content.strip()) < 50:
                    article.content = f"""
# {new_title}

L'Afrique francophone connaît une révolution numérique sans précédent. Le commerce électronique se développe rapidement, offrant de nouvelles opportunités aux entrepreneurs locaux.

## Les Opportunités du Marché

Le marché africain présente un potentiel énorme pour le e-commerce. Avec une population jeune et de plus en plus connectée, les entreprises peuvent toucher de nouveaux clients et développer leur activité.

## Défis et Solutions

Malgré les défis liés à l'infrastructure et aux méthodes de paiement, des solutions innovantes émergent. Les paiements mobiles, en particulier, révolutionnent la façon dont les transactions sont effectuées.

## Conseils pour Réussir

1. Comprendre le marché local
2. Adapter son offre aux besoins spécifiques
3. Utiliser les canaux de communication appropriés
4. Proposer des méthodes de paiement locales
5. Offrir un excellent service client

## Conclusion

L'avenir du commerce électronique en Afrique francophone est prometteur. Les entrepreneurs qui saisissent ces opportunités aujourd'hui seront les leaders de demain.
                    """.strip()
                
                # Add excerpt if missing
                if not article.excerpt:
                    article.excerpt = f"Découvrez les opportunités et défis du commerce électronique en Afrique francophone. Guide pratique pour les entrepreneurs."
                
                # Add tags if missing
                if not article.tags:
                    article.tags = "e-commerce, afrique, entrepreneuriat, digital, business"
                
                db.session.commit()
                print(f"✅ Article corrigé: {new_title}")
                
            except Exception as e:
                print(f"❌ Erreur lors de la correction de l'article {article.id}: {e}")
                db.session.rollback()
        
        print(f"\n🎉 Correction terminée!")
        
        # Display final summary
        total_posts = BlogPost.query.count()
        published_posts = BlogPost.query.filter_by(is_published=True).count()
        featured_posts = BlogPost.query.filter_by(is_featured=True).count()
        
        print(f"\n📊 Résumé final du blog:")
        print(f"   • Total articles: {total_posts}")
        print(f"   • Articles publiés: {published_posts}")
        print(f"   • Articles en vedette: {featured_posts}")
        
        # Show some article titles
        print(f"\n📝 Quelques articles:")
        recent_articles = BlogPost.query.filter_by(is_published=True).order_by(BlogPost.created_at.desc()).limit(5).all()
        for article in recent_articles:
            print(f"   • {article.title}")

if __name__ == "__main__":
    fix_blog_articles()
