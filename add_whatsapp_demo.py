#!/usr/bin/env python3
"""
Script to add WhatsApp demo data to shops
"""

from app import create_app
from models import db, Shop

def add_whatsapp_demo():
    """Add WhatsApp demo data to shops"""
    app = create_app()
    
    with app.app_context():
        try:
            shops = Shop.query.all()
            
            if not shops:
                print("No shops found to update")
                return
            
            # Demo WhatsApp data for shops
            whatsapp_data = [
                {
                    'shop_index': 0,  # Mode Africaine Élégante
                    'whatsapp': '+221771234567',
                    'telegram': '@modeafricaine',
                    'facebook': 'https://facebook.com/modeafricaineelegante'
                },
                {
                    'shop_index': 1,  # TechAfrika Solutions
                    'whatsapp': '+233241234568',
                    'telegram': '@techafrika',
                    'facebook': 'https://facebook.com/techafrikasolutions'
                },
                {
                    'shop_index': 2,  # Artisanat Traditionnel
                    'whatsapp': '+221771234569',
                    'telegram': '@artisanat_senegal',
                    'facebook': 'https://facebook.com/artisanat.traditionnel'
                }
            ]
            
            # Update shops with social media data
            for data in whatsapp_data:
                shop_index = data['shop_index']
                
                if shop_index < len(shops):
                    shop = shops[shop_index]
                    
                    # Create social media data
                    social_media = {
                        'whatsapp': {
                            'enabled': True,
                            'number': data['whatsapp']
                        },
                        'telegram': {
                            'enabled': True,
                            'username': data['telegram']
                        },
                        'facebook': {
                            'enabled': True,
                            'page': data['facebook']
                        }
                    }
                    
                    shop.social_links = social_media
                    print(f"✅ Added social media to {shop.name}")
                    print(f"   📱 WhatsApp: {data['whatsapp']}")
                    print(f"   💬 Telegram: {data['telegram']}")
                    print(f"   📘 Facebook: {data['facebook']}")
            
            db.session.commit()
            print(f"\n✅ Successfully updated social media for {len(whatsapp_data)} shops!")
            
            # Display current social media for all shops
            print("\n📊 Current Social Media by Shop:")
            for shop in shops:
                print(f"\n🏪 {shop.name} ({shop.country}):")
                social_media = shop.get_social_media()
                
                if social_media:
                    # WhatsApp
                    whatsapp = social_media.get('whatsapp', {})
                    if whatsapp.get('enabled', False):
                        print(f"   📱 WhatsApp: {whatsapp.get('number', 'N/A')}")
                        print(f"      Link: {shop.get_whatsapp_link()}")
                    
                    # Telegram
                    telegram = social_media.get('telegram', {})
                    if telegram.get('enabled', False):
                        print(f"   💬 Telegram: {telegram.get('username', 'N/A')}")
                    
                    # Facebook
                    facebook = social_media.get('facebook', {})
                    if facebook.get('enabled', False):
                        print(f"   📘 Facebook: {facebook.get('page', 'N/A')}")
                    
                    if not shop.has_enabled_social_media():
                        print("   ❌ No enabled social media")
                else:
                    print("   ❌ No social media configured")
            
        except Exception as e:
            print(f"❌ Error updating social media: {e}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    add_whatsapp_demo()
