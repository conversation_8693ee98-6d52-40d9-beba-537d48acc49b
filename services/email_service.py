"""
Email Service for sending emails using Flask-Mail
"""
from flask import current_app, render_template_string
from flask_mail import Mail, Message
from models import (db, EmailTemplate, EmailQueue, EmailType, EmailStatus, 
                   NewsletterSubscriber, Newsletter, User, Shop, Order)
from datetime import datetime
import logging
from typing import Dict, List, Optional, Any
import traceback

logger = logging.getLogger(__name__)

class EmailService:
    """Service for handling email operations."""
    
    def __init__(self, app=None):
        self.mail = None
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize the email service with Flask app."""
        self.mail = Mail(app)
    
    def send_email(self, recipient_email: str, subject: str, html_content: str, 
                   text_content: str = None, recipient_name: str = None) -> bool:
        """Send an email immediately."""
        try:
            if not self.mail:
                logger.error("Mail service not initialized")
                return False
            
            msg = Message(
                subject=subject,
                recipients=[recipient_email],
                html=html_content,
                body=text_content,
                sender=current_app.config.get('MAIL_USERNAME', '<EMAIL>')
            )
            
            self.mail.send(msg)
            logger.info(f"Email sent successfully to {recipient_email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email to {recipient_email}: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def queue_email(self, email_type: EmailType, recipient_email: str, 
                    template_data: Dict[str, Any], recipient_name: str = None,
                    user_id: int = None, shop_id: int = None, order_id: int = None) -> bool:
        """Queue an email for sending."""
        try:
            # Get email template
            template = EmailTemplate.query.filter_by(
                email_type=email_type, 
                is_active=True
            ).first()
            
            if not template:
                logger.error(f"No active template found for email type: {email_type.value}")
                return False
            
            # Render template with data
            subject = self._render_template(template.subject, template_data)
            html_content = self._render_template(template.html_content, template_data)
            text_content = self._render_template(template.text_content, template_data) if template.text_content else None
            
            # Create email queue entry
            email_queue = EmailQueue(
                recipient_email=recipient_email,
                recipient_name=recipient_name,
                subject=subject,
                html_content=html_content,
                text_content=text_content,
                email_type=email_type,
                user_id=user_id,
                shop_id=shop_id,
                order_id=order_id
            )
            
            db.session.add(email_queue)
            db.session.commit()
            
            logger.info(f"Email queued for {recipient_email} - Type: {email_type.value}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to queue email: {str(e)}")
            logger.error(traceback.format_exc())
            db.session.rollback()
            return False
    
    def process_email_queue(self, limit: int = 10) -> int:
        """Process pending emails in the queue."""
        try:
            pending_emails = EmailQueue.query.filter_by(
                status=EmailStatus.PENDING
            ).filter(
                EmailQueue.attempts < EmailQueue.max_attempts
            ).limit(limit).all()
            
            sent_count = 0
            
            for email in pending_emails:
                try:
                    # Attempt to send email
                    success = self.send_email(
                        recipient_email=email.recipient_email,
                        subject=email.subject,
                        html_content=email.html_content,
                        text_content=email.text_content,
                        recipient_name=email.recipient_name
                    )
                    
                    email.attempts += 1
                    
                    if success:
                        email.status = EmailStatus.SENT
                        email.sent_at = datetime.utcnow()
                        sent_count += 1
                    else:
                        if email.attempts >= email.max_attempts:
                            email.status = EmailStatus.FAILED
                            email.error_message = "Max attempts reached"
                    
                    db.session.commit()
                    
                except Exception as e:
                    email.attempts += 1
                    email.error_message = str(e)
                    
                    if email.attempts >= email.max_attempts:
                        email.status = EmailStatus.FAILED
                    
                    db.session.commit()
                    logger.error(f"Failed to send queued email {email.id}: {str(e)}")
            
            return sent_count
            
        except Exception as e:
            logger.error(f"Error processing email queue: {str(e)}")
            return 0
    
    def _render_template(self, template_string: str, data: Dict[str, Any]) -> str:
        """Render a template string with data."""
        if not template_string:
            return ""
        
        try:
            return render_template_string(template_string, **data)
        except Exception as e:
            logger.error(f"Template rendering error: {str(e)}")
            return template_string
    
    def send_welcome_email(self, user: User) -> bool:
        """Send welcome email to new user."""
        template_data = {
            'user_name': user.get_full_name(),
            'user_email': user.email,
            'user_role': user.role.value,
            'platform_name': 'Afroly.org',
            'login_url': current_app.config.get('BASE_URL', 'https://afroly.org') + '/auth/login'
        }
        
        return self.queue_email(
            email_type=EmailType.WELCOME,
            recipient_email=user.email,
            template_data=template_data,
            recipient_name=user.get_full_name(),
            user_id=user.id
        )
    
    def send_shop_created_email(self, shop: Shop) -> bool:
        """Send email when shop is created."""
        template_data = {
            'shop_name': shop.name,
            'shop_owner': shop.owner.get_full_name(),
            'shop_url': current_app.config.get('BASE_URL', 'https://afroly.org') + f'/shop/{shop.slug}',
            'platform_name': 'Afroly.org'
        }
        
        return self.queue_email(
            email_type=EmailType.SHOP_CREATED,
            recipient_email=shop.owner.email,
            template_data=template_data,
            recipient_name=shop.owner.get_full_name(),
            user_id=shop.owner.id,
            shop_id=shop.id
        )
    
    def send_shop_approved_email(self, shop: Shop) -> bool:
        """Send email when shop is approved."""
        template_data = {
            'shop_name': shop.name,
            'shop_owner': shop.owner.get_full_name(),
            'shop_url': current_app.config.get('BASE_URL', 'https://afroly.org') + f'/shop/{shop.slug}',
            'dashboard_url': current_app.config.get('BASE_URL', 'https://afroly.org') + '/shop/dashboard',
            'platform_name': 'Afroly.org'
        }
        
        return self.queue_email(
            email_type=EmailType.SHOP_APPROVED,
            recipient_email=shop.owner.email,
            template_data=template_data,
            recipient_name=shop.owner.get_full_name(),
            user_id=shop.owner.id,
            shop_id=shop.id
        )
    
    def send_order_confirmation_email(self, order: Order) -> bool:
        """Send order confirmation email."""
        template_data = {
            'order_number': order.order_number,
            'customer_name': order.customer.get_full_name(),
            'shop_name': order.shop.name,
            'total_amount': f"{order.total:.0f} FCFA",
            'order_url': current_app.config.get('BASE_URL', 'https://afroly.org') + f'/order/{order.order_number}',
            'platform_name': 'Afroly.org',
            'order_items': order.items
        }
        
        return self.queue_email(
            email_type=EmailType.ORDER_CONFIRMATION,
            recipient_email=order.customer.email,
            template_data=template_data,
            recipient_name=order.customer.get_full_name(),
            user_id=order.customer.id,
            shop_id=order.shop.id,
            order_id=order.id
        )

# Global email service instance
email_service = EmailService()
