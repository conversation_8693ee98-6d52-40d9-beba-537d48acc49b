"""
AI Service for content generation using OpenAI GPT-4
"""
from openai import OpenAI
import logging
from typing import Dict, List, Optional

# Initialize OpenAI client
client = OpenAI(
    api_key="************************************************************************************************************************************"
)

logger = logging.getLogger(__name__)

def clean_markdown_formatting(text: str) -> str:
    """Remove markdown formatting from text."""
    import re

    # Remove bold formatting (**text** or __text__)
    text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)
    text = re.sub(r'__(.*?)__', r'\1', text)

    # Remove italic formatting (*text* or _text_)
    text = re.sub(r'\*(.*?)\*', r'\1', text)
    text = re.sub(r'_(.*?)_', r'\1', text)

    # Remove headers (# ## ###)
    text = re.sub(r'^#{1,6}\s*', '', text, flags=re.MULTILINE)

    # Remove bullet points (- or *)
    text = re.sub(r'^[\*\-]\s*', '', text, flags=re.MULTILINE)

    # Clean up extra whitespace
    text = re.sub(r'\n\s*\n', '\n\n', text)
    text = text.strip()

    return text

class AIService:
    """Service for AI-powered content generation."""

    @staticmethod
    def rewrite_product_title(original_title: str, category: str = None) -> str:
        """
        Rewrite a product title to make it more appealing and SEO-friendly.

        Args:
            original_title: The original product title
            category: Optional product category for context

        Returns:
            Rewritten title
        """
        try:
            category_context = f" in the {category} category" if category else ""

            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert e-commerce copywriter specializing in African French-speaking markets. Rewrite product titles to be more appealing, SEO-friendly, and culturally relevant for African customers. IMPORTANT: Always write the output in French, even if the input is in English or another language. Keep titles concise (under 80 characters) but descriptive. Return only plain text without any formatting, quotes, or special characters."
                    },
                    {
                        "role": "user",
                        "content": f"Rewrite this product title{category_context} in French: '{original_title}'"
                    }
                ],
                max_tokens=100,
                temperature=0.7
            )

            result = response.choices[0].message.content.strip().strip('"\'')
            return clean_markdown_formatting(result)

        except Exception as e:
            logger.error(f"Error rewriting product title: {e}")
            return original_title

    @staticmethod
    def rewrite_product_description(original_description: str, title: str = None, category: str = None) -> str:
        """
        Rewrite a product description to make it more engaging and persuasive.

        Args:
            original_description: The original product description
            title: Optional product title for context
            category: Optional product category for context

        Returns:
            Rewritten description
        """
        try:
            context = ""
            if title:
                context += f"Product title: {title}\n"
            if category:
                context += f"Category: {category}\n"

            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert e-commerce copywriter specializing in African French-speaking markets. Rewrite product descriptions to be more engaging, persuasive, and culturally relevant for African customers. IMPORTANT: Always write the output in French, even if the input is in English or another language. Focus on benefits, quality, and value. Use warm, friendly language that resonates with African French-speaking consumers. Write in plain text format without any markdown formatting (no asterisks, no bold text, no special characters). Keep it natural and readable."
                    },
                    {
                        "role": "user",
                        "content": f"{context}Rewrite this product description in French: '{original_description}'"
                    }
                ],
                max_tokens=300,
                temperature=0.7
            )

            result = response.choices[0].message.content.strip()
            return clean_markdown_formatting(result)

        except Exception as e:
            logger.error(f"Error rewriting product description: {e}")
            return original_description

    @staticmethod
    def generate_blog_post(topic: str, category: str = "general", target_audience: str = "African entrepreneurs") -> Dict[str, str]:
        """
        Generate a complete blog post using AI.

        Args:
            topic: The blog post topic
            category: Blog category
            target_audience: Target audience description

        Returns:
            Dictionary with title, content, excerpt, and tags
        """
        try:
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "system",
                        "content": f"You are an expert content writer for Afroly.org, an African e-commerce platform. Write engaging, informative blog posts for {target_audience}. Focus on practical advice, success stories, and insights relevant to African business and e-commerce."
                    },
                    {
                        "role": "user",
                        "content": f"Write a comprehensive blog post about '{topic}' in the {category} category. Include:\n1. An engaging title\n2. A compelling excerpt (2-3 sentences)\n3. Full article content (800-1200 words) with proper structure\n4. 5 relevant tags\n\nFormat your response as:\nTITLE: [title]\nEXCERPT: [excerpt]\nCONTENT: [content]\nTAGS: [tag1, tag2, tag3, tag4, tag5]"
                    }
                ],
                max_tokens=2000,
                temperature=0.7
            )

            content = response.choices[0].message.content.strip()

            # Parse the response
            lines = content.split('\n')
            result = {
                'title': '',
                'excerpt': '',
                'content': '',
                'tags': ''
            }

            current_section = None
            content_lines = []

            for line in lines:
                if line.startswith('TITLE:'):
                    result['title'] = line.replace('TITLE:', '').strip()
                elif line.startswith('EXCERPT:'):
                    result['excerpt'] = line.replace('EXCERPT:', '').strip()
                elif line.startswith('CONTENT:'):
                    current_section = 'content'
                    continue
                elif line.startswith('TAGS:'):
                    result['tags'] = line.replace('TAGS:', '').strip()
                    break
                elif current_section == 'content':
                    content_lines.append(line)

            result['content'] = '\n'.join(content_lines).strip()

            return result

        except Exception as e:
            logger.error(f"Error generating blog post: {e}")
            return {
                'title': f"Guide: {topic}",
                'excerpt': f"Découvrez tout ce que vous devez savoir sur {topic}.",
                'content': f"# {topic}\n\nContenu généré automatiquement pour {topic}.",
                'tags': f"{category}, guide, afroly"
            }

    @staticmethod
    def generate_multiple_blog_posts(topics: List[str], category: str = "general") -> List[Dict[str, str]]:
        """
        Generate multiple blog posts from a list of topics.

        Args:
            topics: List of blog post topics
            category: Blog category

        Returns:
            List of blog post dictionaries
        """
        posts = []
        for topic in topics:
            post = AIService.generate_blog_post(topic, category)
            posts.append(post)

        return posts

    @staticmethod
    def improve_shop_description(original_description: str, shop_name: str, category: str = None) -> str:
        """
        Improve a shop description to make it more appealing.

        Args:
            original_description: Original shop description
            shop_name: Name of the shop
            category: Shop category/niche

        Returns:
            Improved description
        """
        try:
            category_context = f" specializing in {category}" if category else ""

            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert business copywriter specializing in African French-speaking e-commerce. Rewrite shop descriptions to be more professional, trustworthy, and appealing to African customers. IMPORTANT: Always write the output in French, even if the input is in English or another language. Focus on quality, reliability, and customer service."
                    },
                    {
                        "role": "user",
                        "content": f"Improve this shop description for '{shop_name}'{category_context} in French: '{original_description}'"
                    }
                ],
                max_tokens=200,
                temperature=0.7
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"Error improving shop description: {e}")
            return original_description

    @staticmethod
    def generate_product_tags(title: str, description: str, category: str = None) -> str:
        """
        Generate relevant tags for a product.

        Args:
            title: Product title
            description: Product description
            category: Product category

        Returns:
            Comma-separated tags
        """
        try:
            category_context = f"Category: {category}\n" if category else ""

            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "system",
                        "content": "Generate 5-8 relevant, SEO-friendly tags in French for products on an African French-speaking e-commerce platform. Focus on searchable keywords that African French-speaking customers would use. IMPORTANT: Always generate tags in French, even if the input is in English or another language."
                    },
                    {
                        "role": "user",
                        "content": f"{category_context}Title: {title}\nDescription: {description}\n\nGenerate relevant tags in French (comma-separated):"
                    }
                ],
                max_tokens=100,
                temperature=0.5
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"Error generating product tags: {e}")
            return category if category else "produit, qualité, afrique"
