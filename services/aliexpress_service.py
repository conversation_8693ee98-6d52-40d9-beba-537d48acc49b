"""
AliExpress API Service for Dropshipping Integration
"""

import requests
import json
import logging
from typing import Dict, Any, Optional, List
from config import Config

logger = logging.getLogger(__name__)

class AliExpressService:
    """Service for integrating with AliExpress API for dropshipping"""

    def __init__(self):
        self.api_key = Config.ALIEXPRESS_API_KEY
        # Switch to the working free-aliexpress-api
        self.api_host = 'free-aliexpress-api.p.rapidapi.com'
        self.base_url = f"https://{self.api_host}"

        self.headers = {
            "X-RapidAPI-Key": self.api_key,
            "X-RapidAPI-Host": self.api_host
        }

        print(f"🔄 Switched to Free AliExpress API: {self.base_url}")
        print(f"🔑 Using API key: {self.api_key[:10]}..." if self.api_key else "❌ No API key found")

    def get_featured_promos(self) -> Optional[List[Dict[str, Any]]]:
        """Get list of featured promotional campaigns using the working endpoint"""
        try:
            url = f"{self.base_url}/api/v3/lists/featured-promo"

            print(f"🎯 Fetching featured promos from: {url}")
            logger.info("Fetching featured promos from AliExpress")
            response = requests.get(url, headers=self.headers, timeout=30)

            print(f"📡 Featured promos API Response Status: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                print(f"📦 Featured promos response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")

                promos = data.get("promos", [])
                print(f"✅ Found {len(promos)} promotional campaigns")
                logger.info(f"Found {len(promos)} promotional campaigns")
                return promos
            else:
                print(f"❌ Featured promos API error: {response.status_code} - {response.text[:200]}")
                logger.error(f"AliExpress API error: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            print(f"❌ Error fetching featured promos: {str(e)}")
            logger.error(f"Error fetching featured promos: {str(e)}")
            return None

    def get_promo_products(self, promo_name: str, page_no: int = 1, page_size: int = 20,
                          target_currency: str = "USD", target_language: str = "EN") -> Optional[List[Dict[str, Any]]]:
        """Get products from a specific promotional campaign using the working endpoint from your code"""
        try:
            # Use the correct endpoint from your working code
            url = f"{self.base_url}/api/v3/lists/featured-promo-products"

            params = {
                "promo_name": promo_name,  # Use 'promo_name' as in your working code
                "page_no": str(page_no),
                "page_size": str(page_size),
                "target_currency": target_currency,
                "target_language": target_language
            }

            print(f"🎯 Fetching promo products for: {promo_name}")
            print(f"🔗 URL: {url}")
            print(f"📋 Params: {params}")
            logger.info(f"Fetching products for promo: {promo_name}")
            response = requests.get(url, headers=self.headers, params=params, timeout=30)

            print(f"📡 Promo API Response Status: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                print(f"📦 Promo API response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")

                # Use the exact structure from your working code
                product_list = []
                if "products" in data and "product" in data["products"]:
                    product_list = data["products"]["product"]
                    print(f"Found products in data['products']['product']")
                elif "result" in data and "products" in data["result"]:
                    product_list = data["result"]["products"]
                    print(f"Found products in data['result']['products']")
                elif "products" in data and isinstance(data["products"], list):
                    product_list = data["products"]
                    print(f"Found products in data['products'] (direct list)")

                print(f"✅ Found {len(product_list)} promo products for: {promo_name}")
                logger.info(f"Found {len(product_list)} products for promo: {promo_name}")
                return product_list
            else:
                print(f"❌ Promo API error: {response.status_code} - {response.text[:200]}")
                logger.error(f"AliExpress API error: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            print(f"❌ Error fetching promo products: {str(e)}")
            logger.error(f"Error fetching promo products: {str(e)}")
            return None

    def search_products(self, keywords: str, page_no: int = 1, page_size: int = 20,
                       target_currency: str = "USD", target_language: str = "EN",
                       category_id: Optional[str] = None, sort: str = "LAST_VOLUME_DESC",
                       ship_to_country: str = "US") -> Optional[List[Dict[str, Any]]]:
        """Search for products using the Free AliExpress API hot_products endpoint"""

        print(f"🔍 Searching for products with keywords: {keywords}")
        print(f"🆓 Using Free AliExpress API hot_products endpoint")

        try:
            # Try different search approaches for keyword-based search
            if keywords and keywords.strip():
                # First try: Test if there's a search endpoint
                search_result = self._try_keyword_search(keywords, page_no, page_size, target_currency, target_language)
                if search_result:
                    return search_result

                print("🔄 Keyword search failed, falling back to category-based hot products")

            # Fallback: Use hot_products endpoint with category mapping
            url = f"{self.base_url}/hot_products"
            cat_id = category_id if category_id else self._map_keywords_to_category(keywords)

            params = {
                "cat_id": cat_id,
                "sort": sort,
                "target_currency": target_currency,
                "target_language": target_language,
                "page": str(page_no)
            }

            print(f"🔗 URL: {url}")
            print(f"📋 Params: {params}")
            print(f"🔑 Headers: {self.headers}")

            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            print(f"📡 API Response Status: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                print(f"📦 Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")

                # Try different response structures for free-aliexpress-api
                product_list = []
                if "products" in data and isinstance(data["products"], list):
                    product_list = data["products"]
                    print(f"✅ Found products in data['products'] (direct list)")
                elif "result" in data and isinstance(data["result"], list):
                    product_list = data["result"]
                    print(f"✅ Found products in data['result']")
                elif "data" in data and isinstance(data["data"], list):
                    product_list = data["data"]
                    print(f"✅ Found products in data['data']")
                elif isinstance(data, list):
                    product_list = data
                    print(f"✅ Found products in direct list response")

                print(f"🎉 SUCCESS: Found {len(product_list)} products!")

                if product_list:
                    # Print sample product info
                    first_product = product_list[0]
                    product_id = first_product.get("product_id", first_product.get("id", ""))
                    product_title = first_product.get("product_title", first_product.get("title", ""))
                    price = first_product.get("target_sale_price", first_product.get("price", ""))
                    print(f"📦 Sample product: ID={product_id}, Title={product_title[:50]}..., Price={price}")

                    return product_list
                else:
                    print(f"❌ No products found in response")
                    return []
            else:
                print(f"❌ API error: {response.status_code} - {response.text[:200]}")
                return []

        except Exception as e:
            print(f"❌ Error searching products: {str(e)}")
            return []

    def get_product_details(self, product_id: str, target_currency: str = "USD",
                           target_language: str = "EN", ship_to_country: str = "US") -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific product using product-info endpoint"""
        try:
            url = f"{self.base_url}/api/v3/product-info"

            params = {
                "product_id": product_id,
                "target_currency": target_currency,
                "target_language": target_language,
                "ship_to_country": ship_to_country
            }

            logger.info(f"Fetching product details for ID: {product_id}")
            response = requests.get(url, headers=self.headers, params=params, timeout=30)

            if response.status_code == 200:
                data = response.json()
                logger.info(f"Successfully fetched details for product: {product_id}")
                return data
            else:
                logger.error(f"AliExpress API error: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"Error fetching product details: {str(e)}")
            return None

    def format_product_for_import(self, aliexpress_product: Dict[str, Any]) -> Dict[str, Any]:
        """Format AliExpress product data for import into our system"""
        try:
            # Extract basic product information
            product_id = aliexpress_product.get("product_id", "")
            title = aliexpress_product.get("product_title", "")

            # Extract price information
            price = aliexpress_product.get("target_sale_price", 0)
            original_price = aliexpress_product.get("target_original_price", price)
            currency = aliexpress_product.get("target_sale_price_currency", "USD")

            # Extract image URLs - simplified and more robust approach
            all_images = []

            # Get main image
            main_image = aliexpress_product.get("product_main_image_url", "")
            if main_image and main_image.startswith(('http://', 'https://')):
                all_images.append(main_image)
                logger.info(f"✅ Found main image: {main_image}")

            # Get small images - handle various possible structures
            small_image_data = aliexpress_product.get('product_small_image_urls', {})
            small_images = []

            if small_image_data:
                # Case 1: {'string': [list of URLs]}
                if isinstance(small_image_data, dict) and 'string' in small_image_data:
                    string_data = small_image_data['string']
                    if isinstance(string_data, list):
                        small_images = [img for img in string_data if isinstance(img, str) and img.startswith(('http://', 'https://'))]
                    elif isinstance(string_data, str) and string_data.startswith(('http://', 'https://')):
                        small_images = [string_data]

                # Case 2: Direct list of URLs
                elif isinstance(small_image_data, list):
                    small_images = [img for img in small_image_data if isinstance(img, str) and img.startswith(('http://', 'https://'))]

                # Case 3: Direct string URL
                elif isinstance(small_image_data, str) and small_image_data.startswith(('http://', 'https://')):
                    small_images = [small_image_data]

                # Case 4: Dictionary with URL values
                elif isinstance(small_image_data, dict):
                    for key, value in small_image_data.items():
                        if isinstance(value, str) and value.startswith(('http://', 'https://')):
                            small_images.append(value)
                        elif isinstance(value, list):
                            small_images.extend([v for v in value if isinstance(v, str) and v.startswith(('http://', 'https://'))])

            # Add small images to the list (limit to 4 additional images)
            all_images.extend(small_images[:4])

            # Check for other common image fields
            other_image_fields = [
                'product_image_url', 'image_url', 'images', 'product_images',
                'main_image', 'thumb_image', 'gallery_images'
            ]

            for field in other_image_fields:
                if field in aliexpress_product:
                    field_value = aliexpress_product[field]
                    if isinstance(field_value, str) and field_value.startswith(('http://', 'https://')):
                        if field_value not in all_images:
                            all_images.append(field_value)
                    elif isinstance(field_value, list):
                        for img in field_value:
                            if isinstance(img, str) and img.startswith(('http://', 'https://')) and img not in all_images:
                                all_images.append(img)

            # Remove duplicates while preserving order
            seen = set()
            unique_images = []
            for img in all_images:
                if img not in seen:
                    seen.add(img)
                    unique_images.append(img)

            all_images = unique_images[:5]  # Limit to 5 images total

            logger.info(f"✅ Final image list ({len(all_images)} images): {all_images}")

            # If no images found, try to construct image URL from product ID
            if not all_images:
                logger.warning("⚠️ No valid images found, trying to construct image URL")

                # Try to construct AliExpress image URL from product ID
                if product_id:
                    # AliExpress sometimes has predictable image URLs
                    constructed_urls = [
                        f"https://ae01.alicdn.com/kf/{product_id}.jpg",
                        f"https://ae-pic-a1.aliexpress-media.com/kf/{product_id}.jpg",
                        f"https://ae01.alicdn.com/kf/H{product_id}.jpg"
                    ]

                    for url in constructed_urls:
                        all_images.append(url)
                        logger.info(f"🔧 Trying constructed URL: {url}")

                # If still no images, use placeholder
                if not all_images:
                    all_images = ['/static/images/no-image.svg']

            # Extract other details
            min_order_quantity = aliexpress_product.get("product_min_order_quantity", 1)
            rating = aliexpress_product.get("evaluate_rate", "0")
            orders_count = aliexpress_product.get("orders_count", 0)

            # Create formatted product data
            formatted_product = {
                "aliexpress_id": product_id,
                "name": title,
                "price": float(price) if price else 0.0,
                "original_price": float(original_price) if original_price else 0.0,
                "currency": currency,
                "images": all_images,
                "min_order_quantity": min_order_quantity,
                "rating": rating,
                "orders_count": orders_count,
                "source": "aliexpress",
                "source_url": f"https://www.aliexpress.com/item/{product_id}.html"
            }

            return formatted_product

        except Exception as e:
            logger.error(f"Error formatting product for import: {str(e)}")
            return None

    def convert_currency(self, amount: float, from_currency: str, to_currency: str = "EUR") -> float:
        """Convert currency amounts (simplified conversion)"""
        # This is a simplified conversion. In production, you'd use a real currency API
        conversion_rates = {
            "USD": {"EUR": 0.85, "FCFA": 600},
            "EUR": {"USD": 1.18, "FCFA": 655},
            "FCFA": {"USD": 0.0017, "EUR": 0.0015}
        }

        if from_currency == to_currency:
            return amount

        if from_currency in conversion_rates and to_currency in conversion_rates[from_currency]:
            rate = conversion_rates[from_currency][to_currency]
            return round(amount * rate, 2)

        return amount  # Return original if conversion not available

    def get_friendly_promo_name(self, promo_name: str) -> str:
        """Convert internal promo names to user-friendly display names"""
        # Mapping of internal promo names to user-friendly names
        promo_mappings = {
            # Computer & Electronics
            "AEB_ ComputerAccessories_EG": "Accessoires Informatiques",
            "AEB_ PhoneAccessories_EG": "Accessoires Téléphone",
            "AEB_AU_HomeImprovement&Furniture&Lights&Tools&Luggage": "Maison & Bricolage",
            "AEB_AU_local stock_20240826": "Stock Local Australie",
            "AEB_BR_DropiSelectedItems_20241106": "Sélection Brésil",
            "AEB_BR_ShipFromBR_20241114": "Expédition Brésil",

            # Seasonal & Special
            "0225-1231 Idealo Promo": "Promotion Idealo",
            "Black Friday": "Black Friday",
            "Cyber Monday": "Cyber Monday",
            "Christmas Sale": "Vente de Noël",
            "New Year Sale": "Vente Nouvel An",

            # Categories
            "Electronics": "Électronique",
            "Fashion": "Mode",
            "Home & Garden": "Maison & Jardin",
            "Sports & Outdoors": "Sports & Plein Air",
            "Beauty & Health": "Beauté & Santé",
            "Toys & Games": "Jouets & Jeux",
            "Automotive": "Automobile",
            "Books & Media": "Livres & Médias",
        }

        # Check for exact match first
        if promo_name in promo_mappings:
            return promo_mappings[promo_name]

        # Try to extract meaningful parts from the promo name
        friendly_name = promo_name

        # Remove common prefixes
        if friendly_name.startswith("AEB_"):
            friendly_name = friendly_name[4:]

        # Replace underscores with spaces
        friendly_name = friendly_name.replace("_", " ")

        # Replace common abbreviations
        replacements = {
            "ComputerAccessories": "Accessoires Informatiques",
            "PhoneAccessories": "Accessoires Téléphone",
            "HomeImprovement": "Amélioration Maison",
            "Furniture": "Mobilier",
            "Tools": "Outils",
            "Luggage": "Bagages",
            "EG": "Égypte",
            "AU": "Australie",
            "BR": "Brésil",
            "US": "États-Unis",
            "UK": "Royaume-Uni",
            "DE": "Allemagne",
            "FR": "France",
            "IT": "Italie",
            "ES": "Espagne",
            "local stock": "Stock Local",
            "DropiSelectedItems": "Articles Sélectionnés",
            "ShipFrom": "Expédition depuis",
        }

        for old, new in replacements.items():
            friendly_name = friendly_name.replace(old, new)

        # Clean up extra spaces and capitalize
        friendly_name = " ".join(word.capitalize() for word in friendly_name.split())

        # If the name is still too technical, provide a generic name
        if len(friendly_name) > 50 or any(char in friendly_name for char in ['&', '20241', '20240']):
            # Extract category hints
            if "computer" in promo_name.lower() or "pc" in promo_name.lower():
                return "Informatique & Accessoires"
            elif "phone" in promo_name.lower() or "mobile" in promo_name.lower():
                return "Téléphones & Accessoires"
            elif "home" in promo_name.lower() or "house" in promo_name.lower():
                return "Maison & Décoration"
            elif "fashion" in promo_name.lower() or "clothing" in promo_name.lower():
                return "Mode & Vêtements"
            elif "beauty" in promo_name.lower() or "health" in promo_name.lower():
                return "Beauté & Santé"
            else:
                return "Promotion Spéciale"

        return friendly_name

    def _try_keyword_search(self, keywords: str, page_no: int, page_size: int,
                           target_currency: str, target_language: str) -> Optional[List[Dict[str, Any]]]:
        """Try different search endpoints for keyword-based search"""
        search_endpoints = [
            "/search",
            "/products/search",
            "/product/search",
            "/search_products",
            "/api/v3/products",
            "/api/v3/smart-match-products"
        ]

        for endpoint in search_endpoints:
            try:
                url = f"{self.base_url}{endpoint}"
                params = {
                    "keywords": keywords,
                    "page": str(page_no),
                    "page_size": str(page_size),
                    "target_currency": target_currency,
                    "target_language": target_language
                }

                print(f"🔍 Trying search endpoint: {endpoint}")
                response = requests.get(url, headers=self.headers, params=params, timeout=10)

                if response.status_code == 200:
                    data = response.json()
                    if isinstance(data, dict):
                        # Try different response structures
                        for key in ['products', 'items', 'data', 'result']:
                            if key in data and isinstance(data[key], list) and data[key]:
                                products = data[key]
                                print(f"🎉 SUCCESS with {endpoint}: Found {len(products)} products!")
                                return products
                    elif isinstance(data, list) and data:
                        print(f"🎉 SUCCESS with {endpoint}: Found {len(data)} products!")
                        return data

                print(f"❌ {endpoint} failed: {response.status_code}")

            except Exception as e:
                print(f"❌ Error with {endpoint}: {str(e)}")
                continue

        return None

    def _map_keywords_to_category(self, keywords: str) -> str:
        """Map search keywords to AliExpress category IDs for better results"""
        if not keywords:
            return "15"  # Default to electronics

        keywords_lower = keywords.lower()

        # Category mapping based on common keywords
        category_map = {
            # Fashion & Clothing
            "shoes": "322",
            "clothing": "3",
            "dress": "3",
            "shirt": "3",
            "pants": "3",
            "fashion": "3",

            # Beauty & Health
            "beauty": "66",
            "makeup": "66",
            "skincare": "66",
            "hair": "66",
            "health": "66",

            # Electronics (default)
            "phone": "15",
            "electronics": "15",
            "computer": "15",
            "laptop": "15",
            "headphones": "15",

            # Home & Garden
            "home": "13",
            "kitchen": "13",
            "furniture": "13",
            "garden": "13",

            # Sports & Entertainment
            "sports": "18",
            "fitness": "18",
            "toys": "18",
            "games": "18",

            # Jewelry & Accessories
            "jewelry": "36",
            "watch": "36",
            "accessories": "36",

            # Bags & Luggage
            "bag": "1501",
            "luggage": "1501",
            "backpack": "1501"
        }

        # Find matching category
        for keyword, cat_id in category_map.items():
            if keyword in keywords_lower:
                print(f"🎯 Mapped keyword '{keywords}' to category {cat_id} ({keyword})")
                return cat_id

        # Default to electronics if no match
        print(f"🎯 Using default electronics category (15) for keyword '{keywords}'")
        return "15"

    def _get_mock_products_with_real_images(self, keywords: str = "", page_size: int = 20) -> List[Dict[str, Any]]:
        """Return mock products with real AliExpress images for testing"""
        logger.info(f"🧪 Returning mock products with real images for keywords: {keywords}")

        mock_products = [
            {
                "product_id": "1005004123456789",
                "product_title": "Wireless Bluetooth Earphones with Charging Case",
                "target_sale_price": 15.99,
                "target_original_price": 29.99,
                "target_sale_price_currency": "USD",
                "product_main_image_url": "https://ae-pic-a1.aliexpress-media.com/kf/Sae045169cae34cd29414a480e176b74cx.jpg",
                "product_small_image_urls": {
                    "string": [
                        "https://ae-pic-a1.aliexpress-media.com/kf/Sae045169cae34cd29414a480e176b74cx.jpg",
                        "https://ae01.alicdn.com/kf/H123456789abcdef.jpg"
                    ]
                },
                "evaluate_rate": "4.5",
                "orders_count": 1250,
                "product_min_order_quantity": 1
            },
            {
                "product_id": "1005004987654321",
                "product_title": "Smartphone Case with Ring Holder - Clear Design",
                "target_sale_price": 8.99,
                "target_original_price": 15.99,
                "target_sale_price_currency": "USD",
                "product_main_image_url": "https://ae01.alicdn.com/kf/H987654321fedcba.jpg",
                "product_small_image_urls": {
                    "string": "https://ae01.alicdn.com/kf/H987654321fedcba.jpg"
                },
                "evaluate_rate": "4.2",
                "orders_count": 890,
                "product_min_order_quantity": 1
            },
            {
                "product_id": "1005004555666777",
                "product_title": "USB-C Fast Charging Cable 3 Pack",
                "target_sale_price": 12.50,
                "target_original_price": 19.99,
                "target_sale_price_currency": "USD",
                "product_main_image_url": "https://ae-pic-a1.aliexpress-media.com/kf/S555666777888999.jpg",
                "evaluate_rate": "4.7",
                "orders_count": 2100,
                "product_min_order_quantity": 1
            },
            {
                "product_id": "1005004111222333",
                "product_title": "Portable Phone Stand Adjustable Desktop Holder",
                "target_sale_price": 6.99,
                "target_original_price": 12.99,
                "target_sale_price_currency": "USD",
                "product_main_image_url": "https://ae01.alicdn.com/kf/H111222333444555.jpg",
                "product_small_image_urls": {
                    "string": [
                        "https://ae01.alicdn.com/kf/H111222333444555.jpg",
                        "https://ae-pic-a1.aliexpress-media.com/kf/S111222333444555.jpg"
                    ]
                },
                "evaluate_rate": "4.3",
                "orders_count": 567,
                "product_min_order_quantity": 1
            }
        ]

        # Filter by keywords if provided
        if keywords:
            filtered_products = []
            keywords_lower = keywords.lower()
            for product in mock_products:
                title_lower = product["product_title"].lower()
                if any(keyword in title_lower for keyword in keywords_lower.split()):
                    filtered_products.append(product)

            if filtered_products:
                return filtered_products[:page_size]

        return mock_products[:page_size]


