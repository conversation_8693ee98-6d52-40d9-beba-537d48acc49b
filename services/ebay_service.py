"""
eBay API Service for Dropshipping Integration
"""

import requests
import json
import logging
from typing import Dict, Any, Optional, List
from config import Config

logger = logging.getLogger(__name__)

class EbayService:
    """Service for integrating with eBay API for dropshipping"""
    
    def __init__(self):
        self.api_key = Config.EBAY_API_KEY
        self.api_host = Config.EBAY_API_HOST
        self.base_url = f"https://{self.api_host}"
        
        self.headers = {
            "X-RapidAPI-Key": self.api_key,
            "X-RapidAPI-Host": self.api_host
        }
    
    def search_products(self, keywords: str, page: int = 1, limit: int = 20) -> Optional[List[Dict[str, Any]]]:
        """Search for products on eBay by keywords"""
        try:
            # Clean keywords for URL
            clean_keywords = keywords.replace(' ', '%20')
            url = f"{self.base_url}/search/{clean_keywords}"
            
            logger.info(f"Searching eBay for keywords: {keywords}")
            response = requests.get(url, headers=self.headers, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                # Extract results from response
                results = data.get("results", [])
                
                # Apply pagination manually since API doesn't support it
                start_index = (page - 1) * limit
                end_index = start_index + limit
                paginated_results = results[start_index:end_index]
                
                logger.info(f"Found {len(results)} total products, returning {len(paginated_results)} for page {page}")
                return paginated_results
            else:
                logger.error(f"eBay API error: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error searching eBay products: {str(e)}")
            return None
    
    def get_product_categories(self) -> List[str]:
        """Get popular eBay categories for dropshipping"""
        return [
            "Electronics",
            "Fashion",
            "Home & Garden",
            "Sports & Outdoors",
            "Automotive",
            "Health & Beauty",
            "Toys & Games",
            "Books & Media",
            "Jewelry & Watches",
            "Business & Industrial"
        ]
    
    def format_product_for_import(self, ebay_product: Dict[str, Any]) -> Dict[str, Any]:
        """Format eBay product data for import into our system"""
        try:
            # Extract basic product information
            title = ebay_product.get("title", "")
            price_str = ebay_product.get("price", "0")
            
            # Clean price string (remove currency symbols and extract number)
            price = self._extract_price_from_string(price_str)
            
            # Extract image URL
            image_url = ebay_product.get("image", "")
            images = [image_url] if image_url else []
            
            # Extract other details
            shipping = ebay_product.get("shipping", "")
            rating = ebay_product.get("rating", "")
            product_url = ebay_product.get("url", "")
            
            # Extract eBay item ID from URL if possible
            ebay_id = self._extract_ebay_id_from_url(product_url)
            
            # Create formatted product data
            formatted_product = {
                "ebay_id": ebay_id,
                "name": title,
                "price": price,
                "original_price": price,
                "currency": "USD",  # eBay typically shows USD prices
                "images": images,
                "shipping_info": shipping,
                "rating": rating,
                "source": "ebay",
                "source_url": product_url,
                "description": f"Product imported from eBay.\n\nOriginal Title: {title}\nShipping: {shipping}\nRating: {rating}"
            }
            
            return formatted_product
            
        except Exception as e:
            logger.error(f"Error formatting eBay product for import: {str(e)}")
            return None
    
    def _extract_price_from_string(self, price_str: str) -> float:
        """Extract numeric price from price string"""
        try:
            # Remove common currency symbols and text
            import re
            # Extract numbers and decimal points
            price_match = re.search(r'[\d,]+\.?\d*', str(price_str).replace(',', ''))
            if price_match:
                return float(price_match.group())
            return 0.0
        except:
            return 0.0
    
    def _extract_ebay_id_from_url(self, url: str) -> str:
        """Extract eBay item ID from product URL"""
        try:
            import re
            # eBay URLs typically contain item IDs in various formats
            # Look for patterns like /itm/123456789 or ?item=123456789
            patterns = [
                r'/itm/(\d+)',
                r'item=(\d+)',
                r'/p/(\d+)',
                r'_(\d{10,})'  # Long numeric IDs
            ]
            
            for pattern in patterns:
                match = re.search(pattern, url)
                if match:
                    return match.group(1)
            
            # If no ID found, use a hash of the URL
            import hashlib
            return hashlib.md5(url.encode()).hexdigest()[:12]
        except:
            import time
            return f"ebay_{int(time.time())}"
    
    def get_trending_searches(self) -> List[str]:
        """Get trending search terms for eBay"""
        return [
            "iPhone case",
            "wireless earbuds",
            "laptop stand",
            "phone charger",
            "bluetooth speaker",
            "smartwatch",
            "gaming mouse",
            "led lights",
            "car accessories",
            "fitness tracker",
            "tablet case",
            "wireless mouse",
            "phone holder",
            "usb cable",
            "power bank"
        ]
    
    def convert_currency(self, amount: float, from_currency: str, to_currency: str = "EUR") -> float:
        """Convert currency amounts (simplified conversion)"""
        # This is a simplified conversion. In production, you'd use a real currency API
        conversion_rates = {
            "USD": {"EUR": 0.85, "FCFA": 600},
            "EUR": {"USD": 1.18, "FCFA": 655},
            "FCFA": {"USD": 0.0017, "EUR": 0.0015}
        }
        
        if from_currency == to_currency:
            return amount
        
        if from_currency in conversion_rates and to_currency in conversion_rates[from_currency]:
            rate = conversion_rates[from_currency][to_currency]
            return round(amount * rate, 2)
        
        return amount  # Return original if conversion not available
    
    def get_product_suggestions(self, category: str) -> List[str]:
        """Get product suggestions for a specific category"""
        suggestions = {
            "Electronics": [
                "smartphone accessories", "laptop accessories", "gaming gear", 
                "audio equipment", "smart home devices"
            ],
            "Fashion": [
                "women's clothing", "men's clothing", "shoes", "bags", 
                "jewelry", "watches", "sunglasses"
            ],
            "Home & Garden": [
                "kitchen gadgets", "home decor", "garden tools", "furniture", 
                "lighting", "storage solutions"
            ],
            "Sports & Outdoors": [
                "fitness equipment", "outdoor gear", "sports accessories", 
                "camping equipment", "cycling gear"
            ],
            "Automotive": [
                "car accessories", "car electronics", "car care", "tools", 
                "motorcycle accessories"
            ],
            "Health & Beauty": [
                "skincare", "makeup", "hair care", "health supplements", 
                "beauty tools"
            ],
            "Toys & Games": [
                "educational toys", "board games", "action figures", 
                "puzzles", "outdoor toys"
            ],
            "Books & Media": [
                "books", "movies", "music", "video games", "magazines"
            ],
            "Jewelry & Watches": [
                "necklaces", "rings", "bracelets", "earrings", "watches"
            ],
            "Business & Industrial": [
                "office supplies", "industrial equipment", "tools", 
                "safety equipment", "packaging supplies"
            ]
        }
        
        return suggestions.get(category, ["popular items", "trending products", "best sellers"])
    
    def validate_product_for_dropshipping(self, product: Dict[str, Any]) -> Dict[str, Any]:
        """Validate if a product is suitable for dropshipping"""
        validation_result = {
            "is_suitable": True,
            "warnings": [],
            "recommendations": []
        }
        
        # Check price
        price = product.get("price", 0)
        if price < 5:
            validation_result["warnings"].append("Very low price - check profit margins")
        elif price > 500:
            validation_result["warnings"].append("High price item - may have lower conversion")
        
        # Check if image is available
        if not product.get("images") or not product["images"][0]:
            validation_result["warnings"].append("No product image available")
            validation_result["is_suitable"] = False
        
        # Check title length
        title = product.get("name", "")
        if len(title) < 10:
            validation_result["warnings"].append("Very short product title")
        elif len(title) > 80:
            validation_result["recommendations"].append("Consider shortening the product title")
        
        # Add general recommendations
        validation_result["recommendations"].extend([
            "Research competitor pricing",
            "Check shipping costs and times",
            "Verify seller reputation",
            "Consider seasonal demand"
        ])
        
        return validation_result
