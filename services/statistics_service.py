"""
Statistics Service for tracking site and shop analytics
"""
from models import db, SiteStatistic, ShopStatistic, User, Shop, Product, Order
from datetime import datetime, date, timedelta
from sqlalchemy import func, and_
from typing import Dict, List, Optional

class StatisticsService:
    """Service for managing site and shop statistics."""
    
    @staticmethod
    def record_page_view(shop_id: Optional[int] = None, is_unique_visitor: bool = False):
        """
        Record a page view for the site or a specific shop.
        
        Args:
            shop_id: Optional shop ID for shop-specific tracking
            is_unique_visitor: Whether this is a unique visitor
        """
        today = date.today()
        
        # Record site statistics
        site_stat = SiteStatistic.query.filter_by(date=today).first()
        if not site_stat:
            site_stat = SiteStatistic(date=today)
            db.session.add(site_stat)
        
        site_stat.page_views += 1
        if is_unique_visitor:
            site_stat.unique_visitors += 1
        
        # Record shop statistics if shop_id provided
        if shop_id:
            shop_stat = ShopStatistic.query.filter_by(shop_id=shop_id, date=today).first()
            if not shop_stat:
                shop_stat = ShopStatistic(shop_id=shop_id, date=today)
                db.session.add(shop_stat)
            
            shop_stat.views += 1
            if is_unique_visitor:
                shop_stat.unique_visitors += 1
        
        db.session.commit()
    
    @staticmethod
    def record_product_view(shop_id: int):
        """Record a product view for a shop."""
        today = date.today()
        
        shop_stat = ShopStatistic.query.filter_by(shop_id=shop_id, date=today).first()
        if not shop_stat:
            shop_stat = ShopStatistic(shop_id=shop_id, date=today)
            db.session.add(shop_stat)
        
        shop_stat.product_views += 1
        db.session.commit()
    
    @staticmethod
    def record_new_user():
        """Record a new user registration."""
        today = date.today()
        
        site_stat = SiteStatistic.query.filter_by(date=today).first()
        if not site_stat:
            site_stat = SiteStatistic(date=today)
            db.session.add(site_stat)
        
        site_stat.new_users += 1
        db.session.commit()
    
    @staticmethod
    def record_new_shop():
        """Record a new shop creation."""
        today = date.today()
        
        site_stat = SiteStatistic.query.filter_by(date=today).first()
        if not site_stat:
            site_stat = SiteStatistic(date=today)
            db.session.add(site_stat)
        
        site_stat.new_shops += 1
        db.session.commit()
    
    @staticmethod
    def record_new_product():
        """Record a new product creation."""
        today = date.today()
        
        site_stat = SiteStatistic.query.filter_by(date=today).first()
        if not site_stat:
            site_stat = SiteStatistic(date=today)
            db.session.add(site_stat)
        
        site_stat.new_products += 1
        db.session.commit()
    
    @staticmethod
    def record_order(shop_id: int, amount: float):
        """Record a new order."""
        today = date.today()
        
        # Site statistics
        site_stat = SiteStatistic.query.filter_by(date=today).first()
        if not site_stat:
            site_stat = SiteStatistic(date=today)
            db.session.add(site_stat)
        
        site_stat.orders_count += 1
        site_stat.revenue += amount
        
        # Shop statistics
        shop_stat = ShopStatistic.query.filter_by(shop_id=shop_id, date=today).first()
        if not shop_stat:
            shop_stat = ShopStatistic(shop_id=shop_id, date=today)
            db.session.add(shop_stat)
        
        shop_stat.orders_count += 1
        shop_stat.revenue += amount
        
        db.session.commit()
    
    @staticmethod
    def get_site_statistics(days: int = 30) -> Dict:
        """
        Get site statistics for the last N days.
        
        Args:
            days: Number of days to include
            
        Returns:
            Dictionary with site statistics
        """
        end_date = date.today()
        start_date = end_date - timedelta(days=days-1)
        
        # Get daily statistics
        daily_stats = SiteStatistic.query.filter(
            and_(SiteStatistic.date >= start_date, SiteStatistic.date <= end_date)
        ).order_by(SiteStatistic.date).all()
        
        # Calculate totals
        total_views = sum(stat.page_views for stat in daily_stats)
        total_unique_visitors = sum(stat.unique_visitors for stat in daily_stats)
        total_new_users = sum(stat.new_users for stat in daily_stats)
        total_new_shops = sum(stat.new_shops for stat in daily_stats)
        total_new_products = sum(stat.new_products for stat in daily_stats)
        total_orders = sum(stat.orders_count for stat in daily_stats)
        total_revenue = sum(stat.revenue for stat in daily_stats)
        
        # Get overall totals
        total_users = User.query.count()
        total_shops = Shop.query.count()
        total_products = Product.query.count()
        
        return {
            'period_days': days,
            'daily_stats': daily_stats,
            'totals': {
                'page_views': total_views,
                'unique_visitors': total_unique_visitors,
                'new_users': total_new_users,
                'new_shops': total_new_shops,
                'new_products': total_new_products,
                'orders': total_orders,
                'revenue': total_revenue
            },
            'overall_totals': {
                'users': total_users,
                'shops': total_shops,
                'products': total_products
            }
        }
    
    @staticmethod
    def get_shop_statistics(shop_id: int, days: int = 30) -> Dict:
        """
        Get shop statistics for the last N days.
        
        Args:
            shop_id: Shop ID
            days: Number of days to include
            
        Returns:
            Dictionary with shop statistics
        """
        end_date = date.today()
        start_date = end_date - timedelta(days=days-1)
        
        # Get daily statistics
        daily_stats = ShopStatistic.query.filter(
            and_(
                ShopStatistic.shop_id == shop_id,
                ShopStatistic.date >= start_date,
                ShopStatistic.date <= end_date
            )
        ).order_by(ShopStatistic.date).all()
        
        # Calculate totals
        total_views = sum(stat.views for stat in daily_stats)
        total_unique_visitors = sum(stat.unique_visitors for stat in daily_stats)
        total_product_views = sum(stat.product_views for stat in daily_stats)
        total_orders = sum(stat.orders_count for stat in daily_stats)
        total_revenue = sum(stat.revenue for stat in daily_stats)
        
        # Get shop info
        shop = Shop.query.get(shop_id)
        
        return {
            'shop': shop,
            'period_days': days,
            'daily_stats': daily_stats,
            'totals': {
                'views': total_views,
                'unique_visitors': total_unique_visitors,
                'product_views': total_product_views,
                'orders': total_orders,
                'revenue': total_revenue
            }
        }
    
    @staticmethod
    def get_top_shops(days: int = 30, limit: int = 10) -> List[Dict]:
        """
        Get top performing shops by various metrics.
        
        Args:
            days: Number of days to include
            limit: Number of shops to return
            
        Returns:
            List of shop performance data
        """
        end_date = date.today()
        start_date = end_date - timedelta(days=days-1)
        
        # Get shop performance data
        shop_stats = db.session.query(
            ShopStatistic.shop_id,
            func.sum(ShopStatistic.views).label('total_views'),
            func.sum(ShopStatistic.unique_visitors).label('total_unique_visitors'),
            func.sum(ShopStatistic.product_views).label('total_product_views'),
            func.sum(ShopStatistic.orders_count).label('total_orders'),
            func.sum(ShopStatistic.revenue).label('total_revenue')
        ).filter(
            and_(
                ShopStatistic.date >= start_date,
                ShopStatistic.date <= end_date
            )
        ).group_by(ShopStatistic.shop_id).all()
        
        # Get shop details and combine with stats
        top_shops = []
        for stat in shop_stats:
            shop = Shop.query.get(stat.shop_id)
            if shop:
                top_shops.append({
                    'shop': shop,
                    'views': stat.total_views or 0,
                    'unique_visitors': stat.total_unique_visitors or 0,
                    'product_views': stat.total_product_views or 0,
                    'orders': stat.total_orders or 0,
                    'revenue': stat.total_revenue or 0
                })
        
        # Sort by revenue and limit
        top_shops.sort(key=lambda x: x['revenue'], reverse=True)
        return top_shops[:limit]
    
    @staticmethod
    def get_dashboard_summary() -> Dict:
        """Get summary statistics for admin dashboard."""
        today = date.today()
        yesterday = today - timedelta(days=1)
        week_ago = today - timedelta(days=7)
        
        # Today's stats
        today_stats = SiteStatistic.query.filter_by(date=today).first()
        yesterday_stats = SiteStatistic.query.filter_by(date=yesterday).first()
        
        # Week stats
        week_stats = db.session.query(
            func.sum(SiteStatistic.page_views).label('views'),
            func.sum(SiteStatistic.unique_visitors).label('visitors'),
            func.sum(SiteStatistic.new_users).label('users'),
            func.sum(SiteStatistic.orders_count).label('orders'),
            func.sum(SiteStatistic.revenue).label('revenue')
        ).filter(SiteStatistic.date >= week_ago).first()
        
        return {
            'today': {
                'views': today_stats.page_views if today_stats else 0,
                'visitors': today_stats.unique_visitors if today_stats else 0,
                'users': today_stats.new_users if today_stats else 0,
                'orders': today_stats.orders_count if today_stats else 0,
                'revenue': today_stats.revenue if today_stats else 0
            },
            'yesterday': {
                'views': yesterday_stats.page_views if yesterday_stats else 0,
                'visitors': yesterday_stats.unique_visitors if yesterday_stats else 0,
                'users': yesterday_stats.new_users if yesterday_stats else 0,
                'orders': yesterday_stats.orders_count if yesterday_stats else 0,
                'revenue': yesterday_stats.revenue if yesterday_stats else 0
            },
            'week': {
                'views': week_stats.views or 0,
                'visitors': week_stats.visitors or 0,
                'users': week_stats.users or 0,
                'orders': week_stats.orders or 0,
                'revenue': week_stats.revenue or 0
            }
        }
