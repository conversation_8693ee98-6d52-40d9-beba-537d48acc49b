#!/usr/bin/env python3
"""
Script pour réinitialiser et recréer les données de démonstration
"""

import os
import sys

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db

def reset_demo():
    """Réinitialiser et recréer les données de démonstration"""
    app = create_app()
    
    with app.app_context():
        print("🗑️  Suppression de l'ancienne base de données...")
        
        # Supprimer toutes les tables
        db.drop_all()
        
        print("🏗️  Création des nouvelles tables...")
        
        # Recréer toutes les tables
        db.create_all()
        
        print("📊 Initialisation des données de base...")
        
        # Importer et exécuter la fonction d'initialisation
        from create_demo_data import create_demo_data
        create_demo_data()
        
        print("✅ Démonstration réinitialisée avec succès !")

if __name__ == '__main__':
    reset_demo()
