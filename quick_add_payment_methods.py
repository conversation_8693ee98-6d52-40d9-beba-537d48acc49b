#!/usr/bin/env python3
"""
Quick script to add payment methods to shops.
"""

import sqlite3
import json

def add_payment_methods():
    """Add payment methods to shops in the database."""
    try:
        # Connect to the database
        conn = sqlite3.connect('afroly.db')
        cursor = conn.cursor()
        
        # Get all shops
        cursor.execute("SELECT id, name, country FROM shop")
        shops = cursor.fetchall()
        
        print(f"Found {len(shops)} shops")
        
        for shop_id, shop_name, country in shops:
            print(f"Updating shop: {shop_name} ({country})")
            
            # Create payment methods based on country
            if country == 'Sénégal':
                payment_methods = [
                    {
                        'type': 'orange_money',
                        'details': {
                            'phone_number': '+************',
                            'account_name': shop_name
                        }
                    },
                    {
                        'type': 'wave',
                        'details': {
                            'phone_number': '+************',
                            'account_name': shop_name
                        }
                    },
                    {
                        'type': 'bank_account',
                        'details': {
                            'bank_name': 'Banque Atlantique Sénégal',
                            'account_number': '***********',
                            'account_name': shop_name,
                            'swift_code': 'CBAOSNDA'
                        }
                    }
                ]
            else:
                payment_methods = [
                    {
                        'type': 'mtn_money',
                        'details': {
                            'phone_number': '+************',
                            'account_name': shop_name
                        }
                    },
                    {
                        'type': 'orange_money',
                        'details': {
                            'phone_number': '+************',
                            'account_name': shop_name
                        }
                    },
                    {
                        'type': 'bank_account',
                        'details': {
                            'bank_name': 'Banque Locale',
                            'account_number': '***********',
                            'account_name': shop_name,
                            'swift_code': 'LOCAAFXX'
                        }
                    }
                ]
            
            # Create payment_info JSON
            payment_info = {
                'methods': payment_methods
            }
            
            # Update the shop with payment methods
            cursor.execute(
                "UPDATE shop SET payment_info = ? WHERE id = ?",
                (json.dumps(payment_info), shop_id)
            )
            
            print(f"  Added {len(payment_methods)} payment methods")
        
        # Commit changes
        conn.commit()
        print("✅ Successfully updated all shops with payment methods!")
        
        # Verify the update
        cursor.execute("SELECT name, payment_info FROM shop")
        updated_shops = cursor.fetchall()
        
        print("\n📊 Verification:")
        for name, payment_info_json in updated_shops:
            if payment_info_json:
                payment_info = json.loads(payment_info_json)
                methods = payment_info.get('methods', [])
                print(f"  {name}: {len(methods)} payment methods")
                for method in methods:
                    print(f"    - {method.get('type', 'Unknown')}")
            else:
                print(f"  {name}: No payment methods")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == '__main__':
    add_payment_methods()
