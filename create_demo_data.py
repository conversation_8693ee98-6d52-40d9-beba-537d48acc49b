#!/usr/bin/env python3
"""
Script pour créer des données de démonstration pour AfroMall
"""

import os
import sys
from datetime import datetime, timedelta
from decimal import Decimal

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import (db, User, Shop, Product, Category, Review, Cart, CartItem,
                   UserRole, UserTier, ShopStatus, ProductStatus)
from utils.helpers import generate_slug

def create_demo_data():
    """Créer des données de démonstration"""
    app = create_app()

    with app.app_context():
        print("🚀 Création des données de démonstration...")

        # 1. Créer des utilisateurs de démonstration
        print("👥 Création des utilisateurs...")

        # Vendeur Premium
        vendor_premium = User(
            email='<EMAIL>',
            first_name='Aminata',
            last_name='<PERSON>allo',
            phone='+221771234567',
            role=UserRole.VENDOR,
            tier=UserTier.PREMIUM,
            is_active=True,
            email_verified=True
        )
        vendor_premium.set_password('demo123')
        db.session.add(vendor_premium)

        # Vendeur Gold
        vendor_gold = User(
            email='<EMAIL>',
            first_name='Kwame',
            last_name='Asante',
            phone='+233241234567',
            role=UserRole.VENDOR,
            tier=UserTier.GOLD,
            is_active=True,
            email_verified=True
        )
        vendor_gold.set_password('demo123')
        db.session.add(vendor_gold)

        # Vendeur Gratuit
        vendor_free = User(
            email='<EMAIL>',
            first_name='Fatou',
            last_name='Ndiaye',
            phone='+221771234568',
            role=UserRole.VENDOR,
            tier=UserTier.FREE,
            is_active=True,
            email_verified=True
        )
        vendor_free.set_password('demo123')
        db.session.add(vendor_free)

        # Acheteurs
        buyer1 = User(
            email='<EMAIL>',
            first_name='Omar',
            last_name='Ba',
            phone='+221771234569',
            role=UserRole.SHOPPER,
            tier=UserTier.FREE,
            is_active=True,
            email_verified=True
        )
        buyer1.set_password('demo123')
        db.session.add(buyer1)

        buyer2 = User(
            email='<EMAIL>',
            first_name='Aisha',
            last_name='Kone',
            phone='+225071234567',
            role=UserRole.SHOPPER,
            tier=UserTier.FREE,
            is_active=True,
            email_verified=True
        )
        buyer2.set_password('demo123')
        db.session.add(buyer2)

        db.session.flush()  # Pour obtenir les IDs

        # 2. Créer des boutiques de démonstration
        print("🏪 Création des boutiques...")

        # Boutique Mode Africaine (Premium)
        shop1 = Shop(
            owner_id=vendor_premium.id,
            name='Mode Africaine Élégante',
            slug='mode-africaine-elegante',
            description='Découvrez notre collection exclusive de vêtements africains traditionnels et modernes. Nous proposons des boubous, dashikis, et accessoires de haute qualité fabriqués par des artisans locaux.',
            country='Sénégal',
            status=ShopStatus.ACTIVE,
            contact_email='<EMAIL>',
            contact_phone='+221771234567',
            address='Dakar, Sénégal',
            social_links={
                'facebook': 'https://facebook.com/modeafricaine',
                'instagram': 'https://instagram.com/modeafricaine'
            },
            policies={
                'shipping': 'Livraison gratuite en Afrique de l\'Ouest pour commandes > 50€',
                'returns': 'Retours acceptés sous 14 jours',
                'warranty': 'Garantie qualité 6 mois'
            }
        )
        db.session.add(shop1)

        # Boutique Électronique (Gold)
        shop2 = Shop(
            owner_id=vendor_gold.id,
            name='TechAfrika Solutions',
            slug='techafrika-solutions',
            description='Votre partenaire technologique de confiance en Afrique. Nous vendons des smartphones, ordinateurs, accessoires et offrons un service après-vente exceptionnel.',
            country='Ghana',
            status=ShopStatus.ACTIVE,
            contact_email='<EMAIL>',
            contact_phone='+233241234567',
            address='Accra, Ghana',
            social_links={
                'facebook': 'https://facebook.com/techafrika',
                'twitter': 'https://twitter.com/techafrika',
                'linkedin': 'https://linkedin.com/company/techafrika'
            },
            policies={
                'shipping': 'Livraison express 24-48h dans les capitales',
                'returns': 'Retours acceptés sous 30 jours',
                'warranty': 'Garantie constructeur + extension possible'
            }
        )
        db.session.add(shop2)

        # Boutique Artisanat (Gratuit)
        shop3 = Shop(
            owner_id=vendor_free.id,
            name='Artisanat Traditionnel Sénégal',
            slug='artisanat-traditionnel-senegal',
            description='Artisanat authentique du Sénégal. Sculptures, bijoux, sacs en cuir, et objets décoratifs faits main par des artisans locaux.',
            country='Sénégal',
            status=ShopStatus.ACTIVE,
            contact_email='<EMAIL>',
            contact_phone='+221771234568',
            address='Saint-Louis, Sénégal',
            policies={
                'shipping': 'Livraison sous 5-7 jours ouvrés',
                'returns': 'Retours acceptés sous 7 jours',
                'warranty': 'Pièces uniques, garantie authenticité'
            }
        )
        db.session.add(shop3)

        db.session.flush()

        # 3. Créer des produits de démonstration
        print("📦 Création des produits...")

        # Obtenir les catégories
        mode_cat = Category.query.filter_by(slug='mode-vetements').first()
        electronique_cat = Category.query.filter_by(slug='electronique').first()
        arts_cat = Category.query.filter_by(slug='arts-artisanat').first()

        # Produits Mode Africaine
        products_mode = [
            {
                'name': 'Boubou Grand Boubou Brodé',
                'description': 'Magnifique boubou traditionnel sénégalais avec broderies dorées. Tissu 100% coton de qualité supérieure. Parfait pour les cérémonies et occasions spéciales.',
                'short_description': 'Boubou traditionnel brodé, 100% coton, idéal cérémonies',
                'price': Decimal('89.99'),
                'sale_price': Decimal('69.99'),
                'stock_quantity': 15,
                'featured': True
            },
            {
                'name': 'Dashiki Moderne Unisexe',
                'description': 'Dashiki coloré au design moderne, parfait pour un look décontracté. Motifs traditionnels africains revisités. Taille unique adaptable.',
                'short_description': 'Dashiki moderne coloré, motifs traditionnels, unisexe',
                'price': Decimal('34.99'),
                'stock_quantity': 25,
                'featured': False
            },
            {
                'name': 'Ensemble Wax Femme Élégant',
                'description': 'Ensemble deux pièces en tissu wax authentique. Haut ajusté et jupe longue. Motifs floraux colorés. Tailles disponibles: S à XL.',
                'short_description': 'Ensemble wax 2 pièces, motifs floraux, S-XL',
                'price': Decimal('79.99'),
                'sale_price': Decimal('59.99'),
                'stock_quantity': 12,
                'featured': True
            }
        ]

        for i, prod_data in enumerate(products_mode):
            product = Product(
                shop_id=shop1.id,
                category_id=mode_cat.id if mode_cat else None,
                name=prod_data['name'],
                slug=generate_slug(f"{prod_data['name']}-{shop1.name}"),
                description=prod_data['description'],
                short_description=prod_data['short_description'],
                price=prod_data['price'],
                sale_price=prod_data.get('sale_price'),
                stock_quantity=prod_data['stock_quantity'],
                featured=prod_data['featured'],
                status=ProductStatus.ACTIVE,
                images=['/static/images/no-image.svg']  # Placeholder images
            )
            db.session.add(product)

        # Produits Électronique
        products_tech = [
            {
                'name': 'Smartphone Samsung Galaxy A54',
                'description': 'Smartphone Samsung Galaxy A54 5G, écran 6.4", 128GB, appareil photo 50MP. Garantie internationale 2 ans. Livré avec chargeur et protection écran.',
                'short_description': 'Samsung Galaxy A54 5G, 128GB, 50MP, garantie 2 ans',
                'price': Decimal('349.99'),
                'sale_price': Decimal('299.99'),
                'stock_quantity': 8,
                'featured': True
            },
            {
                'name': 'Ordinateur Portable HP Pavilion',
                'description': 'HP Pavilion 15.6", Intel Core i5, 8GB RAM, 512GB SSD. Parfait pour le travail et les études. Windows 11 préinstallé.',
                'short_description': 'HP Pavilion 15.6", i5, 8GB RAM, 512GB SSD, Windows 11',
                'price': Decimal('649.99'),
                'stock_quantity': 5,
                'featured': True
            },
            {
                'name': 'Écouteurs Bluetooth JBL',
                'description': 'Écouteurs sans fil JBL avec réduction de bruit. Autonomie 24h, étanche IPX7. Parfait pour le sport et les déplacements.',
                'short_description': 'JBL Bluetooth, réduction bruit, 24h autonomie, IPX7',
                'price': Decimal('89.99'),
                'sale_price': Decimal('69.99'),
                'stock_quantity': 20,
                'featured': False
            }
        ]

        for prod_data in products_tech:
            product = Product(
                shop_id=shop2.id,
                category_id=electronique_cat.id if electronique_cat else None,
                name=prod_data['name'],
                slug=generate_slug(f"{prod_data['name']}-{shop2.name}"),
                description=prod_data['description'],
                short_description=prod_data['short_description'],
                price=prod_data['price'],
                sale_price=prod_data.get('sale_price'),
                stock_quantity=prod_data['stock_quantity'],
                featured=prod_data['featured'],
                status=ProductStatus.ACTIVE,
                images=['/static/images/no-image.svg']
            )
            db.session.add(product)

        # Produits Artisanat
        products_art = [
            {
                'name': 'Masque Africain Traditionnel',
                'description': 'Masque traditionnel sculpté à la main en bois d\'ébène. Pièce unique représentant les ancêtres. Parfait pour décoration murale.',
                'short_description': 'Masque traditionnel bois ébène, sculpté main, pièce unique',
                'price': Decimal('45.99'),
                'stock_quantity': 3,
                'featured': True
            },
            {
                'name': 'Collier Perles Africaines',
                'description': 'Collier artisanal en perles de verre colorées. Motifs géométriques traditionnels. Fermoir en argent. Longueur ajustable.',
                'short_description': 'Collier perles verre colorées, motifs traditionnels, argent',
                'price': Decimal('29.99'),
                'stock_quantity': 10,
                'featured': False
            },
            {
                'name': 'Sac Cuir Maroquinerie Locale',
                'description': 'Sac à main en cuir véritable tanné localement. Coutures main, finitions soignées. Compartiments multiples. Bandoulière amovible.',
                'short_description': 'Sac cuir véritable, tanné local, coutures main, bandoulière',
                'price': Decimal('69.99'),
                'sale_price': Decimal('54.99'),
                'stock_quantity': 7,
                'featured': True
            }
        ]

        for prod_data in products_art:
            product = Product(
                shop_id=shop3.id,
                category_id=arts_cat.id if arts_cat else None,
                name=prod_data['name'],
                slug=generate_slug(f"{prod_data['name']}-{shop3.name}"),
                description=prod_data['description'],
                short_description=prod_data['short_description'],
                price=prod_data['price'],
                sale_price=prod_data.get('sale_price'),
                stock_quantity=prod_data['stock_quantity'],
                featured=prod_data['featured'],
                status=ProductStatus.ACTIVE,
                images=['/static/images/no-image.svg']
            )
            db.session.add(product)

        db.session.flush()

        # 4. Créer des avis de démonstration
        print("⭐ Création des avis...")

        products = Product.query.all()

        reviews_data = [
            {
                'rating': 5,
                'title': 'Excellent produit !',
                'comment': 'Très satisfait de mon achat. La qualité est au rendez-vous et la livraison a été rapide. Je recommande vivement cette boutique.'
            },
            {
                'rating': 4,
                'title': 'Bon rapport qualité-prix',
                'comment': 'Produit conforme à la description. Quelques petits défauts mais rien de grave. Service client réactif.'
            },
            {
                'rating': 5,
                'title': 'Parfait pour un cadeau',
                'comment': 'J\'ai offert ce produit et la personne était ravie. Emballage soigné et produit authentique.'
            }
        ]

        for i, product in enumerate(products[:6]):  # Avis pour les 6 premiers produits
            review_data = reviews_data[i % len(reviews_data)]
            review = Review(
                user_id=buyer1.id if i % 2 == 0 else buyer2.id,
                product_id=product.id,
                rating=review_data['rating'],
                title=review_data['title'],
                comment=review_data['comment'],
                is_verified_purchase=True,
                is_approved=True
            )
            db.session.add(review)

        # 5. Créer des paniers de démonstration
        print("🛒 Création des paniers...")

        # Panier pour acheteur1 dans shop1
        cart1 = Cart(user_id=buyer1.id, shop_id=shop1.id)
        db.session.add(cart1)
        db.session.flush()

        # Ajouter des articles au panier
        product1 = Product.query.filter_by(shop_id=shop1.id).first()
        if product1:
            cart_item1 = CartItem(cart_id=cart1.id, product_id=product1.id, quantity=2)
            db.session.add(cart_item1)

        # Panier pour acheteur2 dans shop2
        cart2 = Cart(user_id=buyer2.id, shop_id=shop2.id)
        db.session.add(cart2)
        db.session.flush()

        product2 = Product.query.filter_by(shop_id=shop2.id).first()
        if product2:
            cart_item2 = CartItem(cart_id=cart2.id, product_id=product2.id, quantity=1)
            db.session.add(cart_item2)

        # Sauvegarder toutes les modifications
        db.session.commit()

        print("✅ Données de démonstration créées avec succès !")
        print("\n📋 COMPTES DE DÉMONSTRATION CRÉÉS :")
        print("=" * 50)
        print("🔐 ADMIN :")
        print("   Email: <EMAIL>")
        print("   Mot de passe: admin123")
        print()
        print("🏪 VENDEURS :")
        print("   Premium - Email: <EMAIL>")
        print("   Gold    - Email: <EMAIL>")
        print("   Gratuit - Email: <EMAIL>")
        print("   Mot de passe pour tous: demo123")
        print()
        print("🛍️ ACHETEURS :")
        print("   Email: <EMAIL>")
        print("   Email: <EMAIL>")
        print("   Mot de passe pour tous: demo123")
        print()
        print("🏬 BOUTIQUES CRÉÉES :")
        print("   • Mode Africaine Élégante (Premium)")
        print("   • TechAfrika Solutions (Gold)")
        print("   • Artisanat Traditionnel Sénégal (Gratuit)")
        print()
        print("📦 PRODUITS : 9 produits avec avis et paniers")
        print("=" * 50)

if __name__ == '__main__':
    create_demo_data()
