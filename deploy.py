#!/usr/bin/env python3
"""
Deployment script for Afroly.org
This script handles database initialization and basic setup for production deployment
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(command, description):
    """Run a shell command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully!")
        if result.stdout:
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed!")
        print(f"   Error: {e.stderr.strip()}")
        return False

def check_requirements():
    """Check if all required files exist"""
    required_files = [
        'app.py',
        'models.py',
        'requirements.txt',
        'init_database.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required files: {', '.join(missing_files)}")
        return False
    
    print("✅ All required files found!")
    return True

def setup_environment():
    """Setup the deployment environment"""
    print("🚀 Starting Afroly.org deployment setup...")
    
    # Check requirements
    if not check_requirements():
        return False
    
    # Create necessary directories
    directories = [
        'static/uploads/products',
        'static/uploads/shops',
        'static/uploads/users',
        'instance',
        'logs'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    # Set proper permissions for upload directories
    upload_dirs = [
        'static/uploads',
        'instance'
    ]
    
    for directory in upload_dirs:
        if Path(directory).exists():
            run_command(f"chmod -R 755 {directory}", f"Setting permissions for {directory}")
    
    return True

def install_dependencies():
    """Install Python dependencies"""
    print("📦 Installing Python dependencies...")
    
    # Check if we're in a virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Virtual environment detected")
    else:
        print("⚠️ Warning: Not in a virtual environment")
    
    # Install requirements
    return run_command("pip install -r requirements.txt", "Installing dependencies")

def initialize_database():
    """Initialize the database"""
    print("🗄️ Initializing database...")
    
    # Run the database initialization script
    return run_command("python init_database.py", "Database initialization")

def create_wsgi_file():
    """Create WSGI file for production deployment"""
    wsgi_content = '''#!/usr/bin/env python3
"""
WSGI configuration for Afroly.org
"""

import sys
import os
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

# Import the Flask application
from app import create_app

# Create the application instance
application = create_app()

if __name__ == "__main__":
    application.run()
'''
    
    try:
        with open('wsgi.py', 'w') as f:
            f.write(wsgi_content)
        print("✅ WSGI file created successfully!")
        return True
    except Exception as e:
        print(f"❌ Failed to create WSGI file: {str(e)}")
        return False

def create_htaccess():
    """Create .htaccess file for shared hosting"""
    htaccess_content = '''RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ wsgi.py/$1 [QSA,L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# Cache static files
<FilesMatch "\\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 month"
</FilesMatch>

# Protect sensitive files
<Files "*.py">
    Require all denied
</Files>

<Files "*.db">
    Require all denied
</Files>

<Files "requirements.txt">
    Require all denied
</Files>
'''
    
    try:
        with open('.htaccess', 'w') as f:
            f.write(htaccess_content)
        print("✅ .htaccess file created successfully!")
        return True
    except Exception as e:
        print(f"❌ Failed to create .htaccess file: {str(e)}")
        return False

def main():
    """Main deployment function"""
    print("🌍 Afroly.org Deployment Script")
    print("=" * 50)
    
    steps = [
        ("Environment Setup", setup_environment),
        ("Install Dependencies", install_dependencies),
        ("Initialize Database", initialize_database),
        ("Create WSGI File", create_wsgi_file),
        ("Create .htaccess", create_htaccess)
    ]
    
    failed_steps = []
    
    for step_name, step_function in steps:
        print(f"\n📋 Step: {step_name}")
        print("-" * 30)
        
        if not step_function():
            failed_steps.append(step_name)
            print(f"❌ Step '{step_name}' failed!")
        else:
            print(f"✅ Step '{step_name}' completed successfully!")
    
    print("\n" + "=" * 50)
    
    if failed_steps:
        print("❌ Deployment completed with errors!")
        print(f"Failed steps: {', '.join(failed_steps)}")
        print("\nPlease fix the errors and run the script again.")
        return False
    else:
        print("🎉 Deployment completed successfully!")
        print("\n📋 Next steps:")
        print("1. Configure your web server to point to wsgi.py")
        print("2. Set up SSL certificate for HTTPS")
        print("3. Configure email settings in admin panel")
        print("4. Test the application")
        print("\n🔐 Default login credentials:")
        print("   Admin: <EMAIL> / admin123")
        print("   Vendor: <EMAIL> / vendor123")
        print("\n⚠️ Remember to change default passwords!")
        return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
